import { useEffect, useState } from 'react';
import { auth, db } from '../lib/firebase-config';
import { AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';

export default function FirebaseSetupCheck() {
  const [checks, setChecks] = useState({
    firebaseConfig: false,
    authEnabled: false,
    firestoreEnabled: false
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const runChecks = async () => {
      const results = {
        firebaseConfig: false,
        authEnabled: false,
        firestoreEnabled: false
      };

      // Check Firebase config
      if (auth && db) {
        results.firebaseConfig = true;
      }

      // Check Auth
      try {
        if (auth) {
          results.authEnabled = true;
        }
      } catch (error) {
        console.error('Auth check failed:', error);
      }

      // Check Firestore
      try {
        if (db) {
          results.firestoreEnabled = true;
        }
      } catch (error) {
        console.error('Firestore check failed:', error);
      }

      setChecks(results);
      setLoading(false);
    };

    runChecks();
  }, []);

  if (loading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <p className="text-blue-800 text-sm">Checking Firebase setup...</p>
      </div>
    );
  }

  const allChecksPass = Object.values(checks).every(check => check);

  if (allChecksPass) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
        <div className="flex items-center space-x-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <p className="text-green-800 text-sm font-medium">Firebase is configured correctly!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
      <div className="flex items-start space-x-3">
        <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-red-800 font-medium text-sm mb-2">Firebase Setup Issues</h3>
          
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              {checks.firebaseConfig ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <span className={checks.firebaseConfig ? 'text-green-700' : 'text-red-700'}>
                Firebase Configuration
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {checks.authEnabled ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <span className={checks.authEnabled ? 'text-green-700' : 'text-red-700'}>
                Authentication Service
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {checks.firestoreEnabled ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <span className={checks.firestoreEnabled ? 'text-green-700' : 'text-red-700'}>
                Firestore Database
              </span>
            </div>
          </div>

          {!checks.authEnabled && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
              <p className="text-yellow-800 text-sm mb-2">
                <strong>Action Required:</strong> Enable Email/Password authentication in Firebase Console
              </p>
              <ol className="text-yellow-700 text-xs space-y-1 ml-4 list-decimal">
                <li>Go to Firebase Console → Authentication</li>
                <li>Click "Get started" if not already done</li>
                <li>Go to "Sign-in method" tab</li>
                <li>Enable "Email/Password" provider</li>
                <li>Save changes</li>
              </ol>
              <a 
                href="https://console.firebase.google.com/project/zatconss/authentication/providers" 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-1 text-yellow-700 hover:text-yellow-800 text-xs mt-2 underline"
              >
                <span>Open Firebase Authentication</span>
                <ExternalLink className="h-3 w-3" />
              </a>
            </div>
          )}

          {!checks.firestoreEnabled && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
              <p className="text-yellow-800 text-sm mb-2">
                <strong>Action Required:</strong> Set up Firestore Database
              </p>
              <ol className="text-yellow-700 text-xs space-y-1 ml-4 list-decimal">
                <li>Go to Firebase Console → Firestore Database</li>
                <li>Click "Create database"</li>
                <li>Choose "Start in test mode" for now</li>
                <li>Select a location for your database</li>
                <li>Click "Done"</li>
              </ol>
              <a 
                href="https://console.firebase.google.com/project/zatconss/firestore" 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-1 text-yellow-700 hover:text-yellow-800 text-xs mt-2 underline"
              >
                <span>Open Firestore Database</span>
                <ExternalLink className="h-3 w-3" />
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
