{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/fetch-cache.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "rateLimitedUntil", "memoryCache", "CACHE_TAGS_HEADER", "CACHE_HEADERS_HEADER", "CACHE_STATE_HEADER", "CACHE_REVALIDATE_HEADER", "CACHE_FETCH_URL_HEADER", "CACHE_CONTROL_VALUE_HEADER", "isAvailable", "ctx", "_requestHeaders", "process", "env", "SUSPENSE_CACHE_URL", "constructor", "debug", "NEXT_PRIVATE_DEBUG_CACHE", "headers", "newHeaders", "JSON", "parse", "k", "scHost", "sc<PERSON><PERSON><PERSON><PERSON>", "SUSPENSE_CACHE_BASEPATH", "SUSPENSE_CACHE_AUTH_TOKEN", "cacheEndpoint", "console", "log", "maxMemoryCacheSize", "L<PERSON><PERSON><PERSON>", "max", "length", "value", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "revalidateTag", "tag", "Date", "now", "res", "fetch", "method", "next", "internal", "status", "retryAfter", "get", "parseInt", "ok", "err", "warn", "key", "tags", "softTags", "fetchCache", "fetchIdx", "fetchUrl", "lastModified", "undefined", "start", "fetchParams", "fetchType", "join", "NEXT_CACHE_SOFT_TAGS_HEADER", "error", "text", "cached", "json", "cacheState", "age", "CACHE_ONE_YEAR", "Object", "keys", "set", "revalidate", "toString"], "mappings": ";;;;+BAyBA;;;eAAqBA;;;iEAvBA;2BAId;;;;;;AAEP,IAAIC,mBAAmB;AACvB,IAAIC;AASJ,MAAMC,oBAAoB;AAC1B,MAAMC,uBAAuB;AAC7B,MAAMC,qBAAqB;AAC3B,MAAMC,0BAA0B;AAChC,MAAMC,yBAAyB;AAC/B,MAAMC,6BAA6B;AAEpB,MAAMR;IAKnB,OAAOS,YAAYC,GAElB,EAAE;QACD,OAAO,CAAC,CACNA,CAAAA,IAAIC,eAAe,CAAC,mBAAmB,IAAIC,QAAQC,GAAG,CAACC,kBAAkB,AAAD;IAE5E;IAEAC,YAAYL,GAAwB,CAAE;QACpC,IAAI,CAACM,KAAK,GAAG,CAAC,CAACJ,QAAQC,GAAG,CAACI,wBAAwB;QACnD,IAAI,CAACC,OAAO,GAAG,CAAC;QAChB,IAAI,CAACA,OAAO,CAAC,eAAe,GAAG;QAE/B,IAAId,wBAAwBM,IAAIC,eAAe,EAAE;YAC/C,MAAMQ,aAAaC,KAAKC,KAAK,CAC3BX,IAAIC,eAAe,CAACP,qBAAqB;YAE3C,IAAK,MAAMkB,KAAKH,WAAY;gBAC1B,IAAI,CAACD,OAAO,CAACI,EAAE,GAAGH,UAAU,CAACG,EAAE;YACjC;YACA,OAAOZ,IAAIC,eAAe,CAACP,qBAAqB;QAClD;QACA,MAAMmB,SACJb,IAAIC,eAAe,CAAC,mBAAmB,IAAIC,QAAQC,GAAG,CAACC,kBAAkB;QAE3E,MAAMU,aACJd,IAAIC,eAAe,CAAC,uBAAuB,IAC3CC,QAAQC,GAAG,CAACY,uBAAuB;QAErC,IAAIb,QAAQC,GAAG,CAACa,yBAAyB,EAAE;YACzC,IAAI,CAACR,OAAO,CACV,gBACD,GAAG,CAAC,OAAO,EAAEN,QAAQC,GAAG,CAACa,yBAAyB,CAAC,CAAC;QACvD;QAEA,IAAIH,QAAQ;YACV,IAAI,CAACI,aAAa,GAAG,CAAC,QAAQ,EAAEJ,OAAO,EAAEC,cAAc,GAAG,CAAC;YAC3D,IAAI,IAAI,CAACR,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC,wBAAwB,IAAI,CAACF,aAAa;YACxD;QACF,OAAO,IAAI,IAAI,CAACX,KAAK,EAAE;YACrBY,QAAQC,GAAG,CAAC;QACd;QAEA,IAAInB,IAAIoB,kBAAkB,EAAE;YAC1B,IAAI,CAAC5B,aAAa;gBAChB,IAAI,IAAI,CAACc,KAAK,EAAE;oBACdY,QAAQC,GAAG,CAAC;gBACd;gBAEA3B,cAAc,IAAI6B,iBAAQ,CAAC;oBACzBC,KAAKtB,IAAIoB,kBAAkB;oBAC3BG,QAAO,EAAEC,KAAK,EAAE;4BAcSd;wBAbvB,IAAI,CAACc,OAAO;4BACV,OAAO;wBACT,OAAO,IAAIA,MAAMC,IAAI,KAAK,YAAY;4BACpC,OAAOf,KAAKgB,SAAS,CAACF,MAAMG,KAAK,EAAEJ,MAAM;wBAC3C,OAAO,IAAIC,MAAMC,IAAI,KAAK,SAAS;4BACjC,MAAM,IAAIG,MAAM;wBAClB,OAAO,IAAIJ,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOf,KAAKgB,SAAS,CAACF,MAAMK,IAAI,IAAI,IAAIN,MAAM;wBAChD,OAAO,IAAIC,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOD,MAAMM,IAAI,CAACP,MAAM;wBAC1B;wBACA,wCAAwC;wBACxC,OACEC,MAAMO,IAAI,CAACR,MAAM,GAAIb,CAAAA,EAAAA,kBAAAA,KAAKgB,SAAS,CAACF,MAAMQ,QAAQ,sBAA7BtB,gBAAgCa,MAAM,KAAI,CAAA;oBAEnE;gBACF;YACF;QACF,OAAO;YACL,IAAI,IAAI,CAACjB,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC;YACd;QACF;IACF;IAEA,MAAac,cAAcC,GAAW,EAAE;QACtC,IAAI,IAAI,CAAC5B,KAAK,EAAE;YACdY,QAAQC,GAAG,CAAC,iBAAiBe;QAC/B;QAEA,IAAIC,KAAKC,GAAG,KAAK7C,kBAAkB;YACjC,IAAI,IAAI,CAACe,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC,iBAAiB5B;YAC/B;YACA;QACF;QAEA,IAAI;YACF,MAAM8C,MAAM,MAAMC,MAChB,CAAC,EAAE,IAAI,CAACrB,aAAa,CAAC,mCAAmC,EAAEiB,IAAI,CAAC,EAChE;gBACEK,QAAQ;gBACR/B,SAAS,IAAI,CAACA,OAAO;gBACrB,sCAAsC;gBACtCgC,MAAM;oBAAEC,UAAU;gBAAK;YACzB;YAGF,IAAIJ,IAAIK,MAAM,KAAK,KAAK;gBACtB,MAAMC,aAAaN,IAAI7B,OAAO,CAACoC,GAAG,CAAC,kBAAkB;gBACrDrD,mBAAmB4C,KAAKC,GAAG,KAAKS,SAASF;YAC3C;YAEA,IAAI,CAACN,IAAIS,EAAE,EAAE;gBACX,MAAM,IAAIlB,MAAM,CAAC,2BAA2B,EAAES,IAAIK,MAAM,CAAC,CAAC,CAAC;YAC7D;QACF,EAAE,OAAOK,KAAK;YACZ7B,QAAQ8B,IAAI,CAAC,CAAC,yBAAyB,EAAEd,IAAI,CAAC,EAAEa;QAClD;IACF;IAEA,MAAaH,IACXK,GAAW,EACXjD,GAMC,EACD;QACA,MAAM,EAAEkD,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAGtD;QAE3D,IAAI,CAACoD,YAAY,OAAO;QAExB,IAAIjB,KAAKC,GAAG,KAAK7C,kBAAkB;YACjC,IAAI,IAAI,CAACe,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC;YACd;YACA,OAAO;QACT;QAEA,IAAIU,OAAOrC,+BAAAA,YAAaoD,GAAG,CAACK;QAE5B,0DAA0D;QAC1D,wDAAwD;QACxD,IAAId,KAAKC,GAAG,KAAMP,CAAAA,CAAAA,wBAAAA,KAAM0B,YAAY,KAAI,CAAA,IAAK,MAAM;YACjD1B,OAAO2B;QACT;QAEA,4BAA4B;QAC5B,IAAI,CAAC3B,QAAQ,IAAI,CAACZ,aAAa,EAAE;YAC/B,IAAI;gBACF,MAAMwC,QAAQtB,KAAKC,GAAG;gBACtB,MAAMsB,cAAoC;oBACxCjB,UAAU;oBACVkB,WAAW;oBACXL,UAAUA;oBACVD;gBACF;gBACA,MAAMhB,MAAM,MAAMC,MAChB,CAAC,EAAE,IAAI,CAACrB,aAAa,CAAC,mBAAmB,EAAEgC,IAAI,CAAC,EAChD;oBACEV,QAAQ;oBACR/B,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACX,uBAAuB,EAAEyD;wBAC1B,CAAC7D,kBAAkB,EAAEyD,CAAAA,wBAAAA,KAAMU,IAAI,CAAC,SAAQ;wBACxC,CAACC,sCAA2B,CAAC,EAAEV,CAAAA,4BAAAA,SAAUS,IAAI,CAAC,SAAQ;oBACxD;oBACApB,MAAMkB;gBACR;gBAGF,IAAIrB,IAAIK,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaN,IAAI7B,OAAO,CAACoC,GAAG,CAAC,kBAAkB;oBACrDrD,mBAAmB4C,KAAKC,GAAG,KAAKS,SAASF;gBAC3C;gBAEA,IAAIN,IAAIK,MAAM,KAAK,KAAK;oBACtB,IAAI,IAAI,CAACpC,KAAK,EAAE;wBACdY,QAAQC,GAAG,CACT,CAAC,yBAAyB,EAAE8B,IAAI,YAAY,EAC1Cd,KAAKC,GAAG,KAAKqB,MACd,EAAE,CAAC;oBAER;oBACA,OAAO;gBACT;gBAEA,IAAI,CAACpB,IAAIS,EAAE,EAAE;oBACX5B,QAAQ4C,KAAK,CAAC,MAAMzB,IAAI0B,IAAI;oBAC5B,MAAM,IAAInC,MAAM,CAAC,4BAA4B,EAAES,IAAIK,MAAM,CAAC,CAAC;gBAC7D;gBAEA,MAAMsB,SAAS,MAAM3B,IAAI4B,IAAI;gBAE7B,IAAI,CAACD,UAAUA,OAAOvC,IAAI,KAAK,SAAS;oBACtC,IAAI,CAACnB,KAAK,IAAIY,QAAQC,GAAG,CAAC;wBAAE6C;oBAAO;oBACnC,MAAM,IAAIpC,MAAM,CAAC,mBAAmB,CAAC;gBACvC;gBAEA,MAAMsC,aAAa7B,IAAI7B,OAAO,CAACoC,GAAG,CAACjD;gBACnC,MAAMwE,MAAM9B,IAAI7B,OAAO,CAACoC,GAAG,CAAC;gBAE5Bf,OAAO;oBACLL,OAAOwC;oBACP,qDAAqD;oBACrD,uCAAuC;oBACvCT,cACEW,eAAe,UACX/B,KAAKC,GAAG,KAAKgC,yBAAc,GAC3BjC,KAAKC,GAAG,KAAKS,SAASsB,OAAO,KAAK,MAAM;gBAChD;gBAEA,IAAI,IAAI,CAAC7D,KAAK,EAAE;oBACdY,QAAQC,GAAG,CACT,CAAC,0BAA0B,EAAE8B,IAAI,YAAY,EAC3Cd,KAAKC,GAAG,KAAKqB,MACd,UAAU,EACTY,OAAOC,IAAI,CAACN,QAAQzC,MAAM,CAC3B,eAAe,EAAE2C,WAAW,OAAO,EAAEhB,wBAAAA,KAAMU,IAAI,CAC9C,KACA,WAAW,EAAET,4BAAAA,SAAUS,IAAI,CAAC,KAAK,CAAC;gBAExC;gBAEA,IAAI/B,MAAM;oBACRrC,+BAAAA,YAAa+E,GAAG,CAACtB,KAAKpB;gBACxB;YACF,EAAE,OAAOkB,KAAK;gBACZ,sCAAsC;gBACtC,IAAI,IAAI,CAACzC,KAAK,EAAE;oBACdY,QAAQ4C,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAEf;gBAClD;YACF;QACF;QAEA,OAAOlB,QAAQ;IACjB;IAEA,MAAa0C,IACXtB,GAAW,EACXpB,IAAgC,EAChC,EACEuB,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRJ,IAAI,EAML,EACD;QACA,IAAI,CAACE,YAAY;QAEjB,IAAIjB,KAAKC,GAAG,KAAK7C,kBAAkB;YACjC,IAAI,IAAI,CAACe,KAAK,EAAE;gBACdY,QAAQC,GAAG,CAAC;YACd;YACA;QACF;QAEA3B,+BAAAA,YAAa+E,GAAG,CAACtB,KAAK;YACpBzB,OAAOK;YACP0B,cAAcpB,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAACnB,aAAa,EAAE;YACtB,IAAI;gBACF,MAAMwC,QAAQtB,KAAKC,GAAG;gBACtB,IAAIP,SAAS,QAAQ,gBAAgBA,MAAM;oBACzC,IAAI,CAACrB,OAAO,CAACZ,wBAAwB,GAAGiC,KAAK2C,UAAU,CAACC,QAAQ;gBAClE;gBACA,IACE,CAAC,IAAI,CAACjE,OAAO,CAACZ,wBAAwB,IACtCiC,SAAS,QACT,UAAUA,MACV;oBACA,IAAI,CAACrB,OAAO,CAACV,2BAA2B,GACtC+B,KAAKA,IAAI,CAACrB,OAAO,CAAC,gBAAgB;gBACtC;gBACA,MAAMsB,OAAOpB,KAAKgB,SAAS,CAAC;oBAC1B,GAAGG,IAAI;oBACP,yCAAyC;oBACzC,sBAAsB;oBACtBqB,MAAMM;gBACR;gBAEA,IAAI,IAAI,CAAClD,KAAK,EAAE;oBACdY,QAAQC,GAAG,CAAC,aAAa8B;gBAC3B;gBACA,MAAMS,cAAoC;oBACxCjB,UAAU;oBACVkB,WAAW;oBACXL;oBACAD;gBACF;gBACA,MAAMhB,MAAM,MAAMC,MAChB,CAAC,EAAE,IAAI,CAACrB,aAAa,CAAC,mBAAmB,EAAEgC,IAAI,CAAC,EAChD;oBACEV,QAAQ;oBACR/B,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACX,uBAAuB,EAAEyD,YAAY;wBACtC,CAAC7D,kBAAkB,EAAEyD,CAAAA,wBAAAA,KAAMU,IAAI,CAAC,SAAQ;oBAC1C;oBACA9B,MAAMA;oBACNU,MAAMkB;gBACR;gBAGF,IAAIrB,IAAIK,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaN,IAAI7B,OAAO,CAACoC,GAAG,CAAC,kBAAkB;oBACrDrD,mBAAmB4C,KAAKC,GAAG,KAAKS,SAASF;gBAC3C;gBAEA,IAAI,CAACN,IAAIS,EAAE,EAAE;oBACX,IAAI,CAACxC,KAAK,IAAIY,QAAQC,GAAG,CAAC,MAAMkB,IAAI0B,IAAI;oBACxC,MAAM,IAAInC,MAAM,CAAC,iBAAiB,EAAES,IAAIK,MAAM,CAAC,CAAC;gBAClD;gBAEA,IAAI,IAAI,CAACpC,KAAK,EAAE;oBACdY,QAAQC,GAAG,CACT,CAAC,oCAAoC,EAAE8B,IAAI,YAAY,EACrDd,KAAKC,GAAG,KAAKqB,MACd,UAAU,EAAE3B,KAAKP,MAAM,CAAC,CAAC;gBAE9B;YACF,EAAE,OAAOwB,KAAK;gBACZ,+BAA+B;gBAC/B,IAAI,IAAI,CAACzC,KAAK,EAAE;oBACdY,QAAQ4C,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAEf;gBAChD;YACF;QACF;QACA;IACF;AACF"}