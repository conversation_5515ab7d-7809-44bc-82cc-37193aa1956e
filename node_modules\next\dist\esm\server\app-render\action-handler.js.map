{"version": 3, "sources": ["../../../src/server/app-render/action-handler.ts"], "names": ["ACTION", "RSC", "RSC_CONTENT_TYPE_HEADER", "isNotFoundError", "getURLFromRedirectError", "isRedirectError", "RenderResult", "FlightRenderResult", "filterReqHeaders", "actionsForbiddenHeaders", "appendMutableCookies", "getModifiedCookieValues", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "formDataFromSearchQueryString", "query", "searchParams", "URLSearchParams", "formData", "FormData", "key", "value", "append", "nodeHeadersToRecord", "headers", "record", "Object", "entries", "undefined", "Array", "isArray", "join", "getForwardedHeaders", "req", "res", "requestHeaders", "requestCookies", "responseHeaders", "getHeaders", "rawSetCookies", "setCookies", "map", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "split", "mergedHeaders", "mergedCookies", "concat", "Headers", "addRevalidationHeader", "staticGenerationStore", "requestStore", "Promise", "all", "pendingRevalidates", "isTagRevalidated", "revalidatedTags", "length", "isCookieRevalidated", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "createRedirectRenderResult", "redirectUrl", "startsWith", "forwardedHeaders", "set", "host", "proto", "incrementalCache", "requestProtocol", "fetchUrl", "URL", "prerenderManifest", "preview", "previewModeId", "delete", "headResponse", "fetch", "method", "next", "internal", "get", "response", "includes", "body", "err", "console", "error", "handleAction", "ComponentMod", "serverModuleMap", "generateFlight", "serverActionsBodySizeLimit", "ctx", "actionId", "toLowerCase", "contentType", "isURLEncodedAction", "isMultipartAction", "isFetchAction", "originHostname", "warn", "Error", "statusCode", "promise", "reject", "type", "result", "actionResult", "skipFlight", "pathWasRevalidated", "bound", "actionAsyncStorage", "formState", "run", "isAction", "process", "env", "NEXT_RUNTIME", "decodeReply", "decodeAction", "decodeFormState", "webRequest", "request", "action", "actionReturnedState", "actionData", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "TextDecoder", "decode", "decodeReplyFromBusboy", "require", "busboy", "bb", "pipe", "readableStream", "ReadableStream", "start", "controller", "on", "chunk", "enqueue", "Uint8Array", "close", "fakeRequest", "Request", "duplex", "chunks", "push", "<PERSON><PERSON><PERSON>", "from", "toString", "limit", "parse", "ApiError", "actionModId", "id", "actionHandler", "__next_app__", "returnVal", "apply", "resolve", "values", "asNotFound"], "mappings": "AASA,SACEA,MAAM,EACNC,GAAG,EACHC,uBAAuB,QAClB,6CAA4C;AACnD,SAASC,eAAe,QAAQ,oCAAmC;AACnE,SACEC,uBAAuB,EACvBC,eAAe,QACV,mCAAkC;AACzC,OAAOC,kBAAkB,mBAAkB;AAE3C,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,SACEC,gBAAgB,EAChBC,uBAAuB,QAClB,0BAAyB;AAChC,SACEC,oBAAoB,EACpBC,uBAAuB,QAClB,iDAAgD;AAGvD,SACEC,kCAAkC,EAClCC,sCAAsC,QACjC,sBAAqB;AAG5B,SAASC,8BAA8BC,KAAa;IAClD,MAAMC,eAAe,IAAIC,gBAAgBF;IACzC,MAAMG,WAAW,IAAIC;IACrB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIL,aAAc;QACvCE,SAASI,MAAM,CAACF,KAAKC;IACvB;IACA,OAAOH;AACT;AAEA,SAASK,oBACPC,OAAkD;IAElD,MAAMC,SAAiC,CAAC;IACxC,KAAK,MAAM,CAACL,KAAKC,MAAM,IAAIK,OAAOC,OAAO,CAACH,SAAU;QAClD,IAAIH,UAAUO,WAAW;YACvBH,MAAM,CAACL,IAAI,GAAGS,MAAMC,OAAO,CAACT,SAASA,MAAMU,IAAI,CAAC,QAAQ,CAAC,EAAEV,MAAM,CAAC;QACpE;IACF;IACA,OAAOI;AACT;AAEA,SAASO,oBACPC,GAAoB,EACpBC,GAAmB;IAEnB,kCAAkC;IAClC,MAAMC,iBAAiBF,IAAIT,OAAO;IAClC,MAAMY,iBAAiBD,cAAc,CAAC,SAAS,IAAI;IAEnD,6CAA6C;IAC7C,MAAME,kBAAkBH,IAAII,UAAU;IACtC,MAAMC,gBAAgBF,eAAe,CAAC,aAAa;IACnD,MAAMG,aAAa,AACjBX,CAAAA,MAAMC,OAAO,CAACS,iBAAiBA,gBAAgB;QAACA;KAAc,AAAD,EAC7DE,GAAG,CAAC,CAACC;QACL,qDAAqD;QACrD,MAAM,CAACC,OAAO,GAAG,CAAC,EAAED,UAAU,CAAC,CAACE,KAAK,CAAC,KAAK;QAC3C,OAAOD;IACT;IAEA,qCAAqC;IACrC,MAAME,gBAAgBrC,iBACpB;QACE,GAAGe,oBAAoBY,eAAe;QACtC,GAAGZ,oBAAoBc,gBAAgB;IACzC,GACA5B;IAGF,gBAAgB;IAChB,MAAMqC,gBAAgBV,eAAeQ,KAAK,CAAC,MAAMG,MAAM,CAACP,YAAYT,IAAI,CAAC;IAEzE,qDAAqD;IACrDc,aAAa,CAAC,SAAS,GAAGC;IAE1B,8CAA8C;IAC9C,OAAOD,aAAa,CAAC,oBAAoB;IAEzC,OAAO,IAAIG,QAAQH;AACrB;AAEA,eAAeI,sBACbf,GAAmB,EACnB,EACEgB,qBAAqB,EACrBC,YAAY,EAIb;QAiBwBD;IAfzB,MAAME,QAAQC,GAAG,CAACH,sBAAsBI,kBAAkB,IAAI,EAAE;IAEhE,0EAA0E;IAC1E,+EAA+E;IAC/E,2DAA2D;IAE3D,mDAAmD;IACnD,8EAA8E;IAC9E,4BAA4B;IAE5B,2EAA2E;IAC3E,uEAAuE;IACvE,oFAAoF;IACpF,mBAAmB;IAEnB,MAAMC,mBAAmBL,EAAAA,yCAAAA,sBAAsBM,eAAe,qBAArCN,uCAAuCO,MAAM,IAAG,IAAI;IAC7E,MAAMC,sBAAsB/C,wBAC1BwC,aAAaQ,cAAc,EAC3BF,MAAM,GACJ,IACA;IAEJvB,IAAI0B,SAAS,CACX,wBACAC,KAAKC,SAAS,CAAC;QAAC,EAAE;QAAEP;QAAkBG;KAAoB;AAE9D;AAEA,eAAeK,2BACb9B,GAAoB,EACpBC,GAAmB,EACnB8B,WAAmB,EACnBd,qBAA4C;IAE5ChB,IAAI0B,SAAS,CAAC,qBAAqBI;IACnC,4EAA4E;IAC5E,IAAIA,YAAYC,UAAU,CAAC,MAAM;YAM7Bf;QALF,MAAMgB,mBAAmBlC,oBAAoBC,KAAKC;QAClDgC,iBAAiBC,GAAG,CAAClE,KAAK;QAE1B,MAAMmE,OAAOnC,IAAIT,OAAO,CAAC,OAAO;QAChC,MAAM6C,QACJnB,EAAAA,0CAAAA,sBAAsBoB,gBAAgB,qBAAtCpB,wCAAwCqB,eAAe,KAAI;QAC7D,MAAMC,WAAW,IAAIC,IAAI,CAAC,EAAEJ,MAAM,GAAG,EAAED,KAAK,EAAEJ,YAAY,CAAC;QAE3D,IAAId,sBAAsBM,eAAe,EAAE;gBAOvCN,mEAAAA,2DAAAA;YANFgB,iBAAiBC,GAAG,CAClBvD,oCACAsC,sBAAsBM,eAAe,CAACzB,IAAI,CAAC;YAE7CmC,iBAAiBC,GAAG,CAClBtD,wCACAqC,EAAAA,2CAAAA,sBAAsBoB,gBAAgB,sBAAtCpB,4DAAAA,yCAAwCwB,iBAAiB,sBAAzDxB,oEAAAA,0DAA2DyB,OAAO,qBAAlEzB,kEACI0B,aAAa,KAAI;QAEzB;QAEA,6FAA6F;QAC7F,kDAAkD;QAClDV,iBAAiBW,MAAM,CAAC;QACxB,IAAI;QAEJ,IAAI;YACF,MAAMC,eAAe,MAAMC,MAAMP,UAAU;gBACzCQ,QAAQ;gBACRxD,SAAS0C;gBACTe,MAAM;oBACJ,aAAa;oBACbC,UAAU;gBACZ;YACF;YAEA,IACEJ,aAAatD,OAAO,CAAC2D,GAAG,CAAC,oBAAoBjF,yBAC7C;gBACA,MAAMkF,WAAW,MAAML,MAAMP,UAAU;oBACrCQ,QAAQ;oBACRxD,SAAS0C;oBACTe,MAAM;wBACJ,aAAa;wBACbC,UAAU;oBACZ;gBACF;gBACA,4EAA4E;gBAC5E,KAAK,MAAM,CAAC9D,KAAKC,MAAM,IAAI+D,SAAS5D,OAAO,CAAE;oBAC3C,IAAI,CAACf,wBAAwB4E,QAAQ,CAACjE,MAAM;wBAC1Cc,IAAI0B,SAAS,CAACxC,KAAKC;oBACrB;gBACF;gBAEA,OAAO,IAAId,mBAAmB6E,SAASE,IAAI;YAC7C;QACF,EAAE,OAAOC,KAAK;YACZ,+EAA+E;YAC/EC,QAAQC,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAEF;QACnD;IACF;IACA,OAAO,IAAIjF,aAAauD,KAAKC,SAAS,CAAC,CAAC;AAC1C;AAEA,OAAO,eAAe4B,aAAa,EACjCzD,GAAG,EACHC,GAAG,EACHyD,YAAY,EACZC,eAAe,EACfC,cAAc,EACd3C,qBAAqB,EACrBC,YAAY,EACZ2C,0BAA0B,EAC1BC,GAAG,EAiBJ;IAWC,IAAIC,WAAW/D,IAAIT,OAAO,CAACxB,OAAOiG,WAAW,GAAG;IAChD,MAAMC,cAAcjE,IAAIT,OAAO,CAAC,eAAe;IAC/C,MAAM2E,qBACJlE,IAAI+C,MAAM,KAAK,UAAUkB,gBAAgB;IAC3C,MAAME,oBACJnE,IAAI+C,MAAM,KAAK,WAAUkB,+BAAAA,YAAajC,UAAU,CAAC;IAEnD,MAAMoC,gBACJL,aAAapE,aACb,OAAOoE,aAAa,YACpB/D,IAAI+C,MAAM,KAAK;IAEjB,8CAA8C;IAC9C,IAAI,CAAEqB,CAAAA,iBAAiBF,sBAAsBC,iBAAgB,GAAI;QAC/D;IACF;IAEA,MAAME,iBACJ,OAAOrE,IAAIT,OAAO,CAAC,SAAS,KAAK,WAC7B,IAAIiD,IAAIxC,IAAIT,OAAO,CAAC,SAAS,EAAE4C,IAAI,GACnCxC;IACN,MAAMwC,OAAOnC,IAAIT,OAAO,CAAC,mBAAmB,IAAIS,IAAIT,OAAO,CAAC,OAAO;IAEnE,4EAA4E;IAC5E,wDAAwD;IACxD,IAAI,CAAC8E,gBAAgB;QACnB,0EAA0E;QAC1E,aAAa;QACbd,QAAQe,IAAI,CACV;IAEJ,OAAO,IAAI,CAACnC,QAAQkC,mBAAmBlC,MAAM;QAC3C,uDAAuD;QACvDoB,QAAQC,KAAK,CACX;QAGF,MAAMA,QAAQ,IAAIe,MAAM;QAExB,IAAIH,eAAe;YACjBnE,IAAIuE,UAAU,GAAG;YACjB,MAAMrD,QAAQC,GAAG,CAACH,sBAAsBI,kBAAkB,IAAI,EAAE;YAChE,MAAMoD,UAAUtD,QAAQuD,MAAM,CAAClB;YAC/B,IAAI;gBACF,MAAMiB;YACR,EAAE,OAAM,CAAC;YAET,OAAO;gBACLE,MAAM;gBACNC,QAAQ,MAAMhB,eAAeE,KAAK;oBAChCe,cAAcJ;oBACd,6EAA6E;oBAC7EK,YAAY,CAAC7D,sBAAsB8D,kBAAkB;gBACvD;YACF;QACF;QAEA,MAAMvB;IACR;IAEA,sDAAsD;IACtDvD,IAAI0B,SAAS,CACX,iBACA;IAEF,IAAIqD,QAAQ,EAAE;IAEd,MAAM,EAAEC,kBAAkB,EAAE,GAAGvB;IAI/B,IAAImB;IACJ,IAAIK;IAEJ,IAAI;QACF,MAAMD,mBAAmBE,GAAG,CAAC;YAAEC,UAAU;QAAK,GAAG;YAC/C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,2CAA2C;gBAC3C,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAGhC;gBAEvD,MAAMiC,aAAa3F;gBACnB,IAAI,CAAC2F,WAAWtC,IAAI,EAAE;oBACpB,MAAM,IAAIkB,MAAM;gBAClB;gBAEA,IAAIJ,mBAAmB;oBACrB,kCAAkC;oBAClC,MAAMlF,WAAW,MAAM0G,WAAWC,OAAO,CAAC3G,QAAQ;oBAClD,IAAImF,eAAe;wBACjBY,QAAQ,MAAMQ,YAAYvG,UAAU0E;oBACtC,OAAO;wBACL,MAAMkC,SAAS,MAAMJ,aAAaxG,UAAU0E;wBAC5C,MAAMmC,sBAAsB,MAAMD;wBAClCX,YAAYQ,gBAAgBI,qBAAqB7G;wBAEjD,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,IAAI8G,aAAa;oBAEjB,MAAMC,SAASL,WAAWtC,IAAI,CAAC4C,SAAS;oBACxC,MAAO,KAAM;wBACX,MAAM,EAAEC,IAAI,EAAE9G,KAAK,EAAE,GAAG,MAAM4G,OAAOG,IAAI;wBACzC,IAAID,MAAM;4BACR;wBACF;wBAEAH,cAAc,IAAIK,cAAcC,MAAM,CAACjH;oBACzC;oBAEA,IAAI8E,oBAAoB;wBACtB,MAAMjF,WAAWJ,8BAA8BkH;wBAC/Cf,QAAQ,MAAMQ,YAAYvG,UAAU0E;oBACtC,OAAO;wBACLqB,QAAQ,MAAMQ,YAAYO,YAAYpC;oBACxC;gBACF;YACF,OAAO;gBACL,oEAAoE;gBACpE,MAAM,EACJ6B,WAAW,EACXc,qBAAqB,EACrBb,YAAY,EACZC,eAAe,EAChB,GAAGa,QAAQ,CAAC,mBAAmB,CAAC;gBAEjC,IAAIpC,mBAAmB;oBACrB,IAAIC,eAAe;wBACjB,MAAMoC,SAASD,QAAQ;wBACvB,MAAME,KAAKD,OAAO;4BAAEjH,SAASS,IAAIT,OAAO;wBAAC;wBACzCS,IAAI0G,IAAI,CAACD;wBAETzB,QAAQ,MAAMsB,sBAAsBG,IAAI9C;oBAC1C,OAAO;wBACL,uDAAuD;wBACvD,MAAMgD,iBAAiB,IAAIC,eAAe;4BACxCC,OAAMC,UAAU;gCACd9G,IAAI+G,EAAE,CAAC,QAAQ,CAACC;oCACdF,WAAWG,OAAO,CAAC,IAAIC,WAAWF;gCACpC;gCACAhH,IAAI+G,EAAE,CAAC,OAAO;oCACZD,WAAWK,KAAK;gCAClB;gCACAnH,IAAI+G,EAAE,CAAC,SAAS,CAACzD;oCACfwD,WAAWtD,KAAK,CAACF;gCACnB;4BACF;wBACF;wBAEA,6DAA6D;wBAC7D,0CAA0C;wBAC1C,MAAM8D,cAAc,IAAIC,QAAQ,oBAAoB;4BAClDtE,QAAQ;4BACR,mBAAmB;4BACnBxD,SAAS;gCAAE,gBAAgB0E;4BAAY;4BACvCZ,MAAMsD;4BACNW,QAAQ;wBACV;wBACA,MAAMrI,WAAW,MAAMmI,YAAYnI,QAAQ;wBAC3C,MAAM4G,SAAS,MAAMJ,aAAaxG,UAAU0E;wBAC5C,MAAMmC,sBAAsB,MAAMD;wBAClCX,YAAY,MAAMQ,gBAAgBI,qBAAqB7G;wBAEvD,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,MAAMsI,SAAS,EAAE;oBAEjB,WAAW,MAAMP,SAAShH,IAAK;wBAC7BuH,OAAOC,IAAI,CAACC,OAAOC,IAAI,CAACV;oBAC1B;oBAEA,MAAMjB,aAAa0B,OAAO3G,MAAM,CAACyG,QAAQI,QAAQ,CAAC;oBAElD,MAAMC,QAAQrB,QAAQ,4BAA4BsB,KAAK,CACrDhE,8BAA8B;oBAGhC,IAAIkC,WAAWvE,MAAM,GAAGoG,OAAO;wBAC7B,MAAM,EAAEE,QAAQ,EAAE,GAAGvB,QAAQ;wBAC7B,MAAM,IAAIuB,SACR,KACA,CAAC,cAAc,EAAEjE,2BAA2B;kIACwE,CAAC;oBAEzH;oBAEA,IAAIK,oBAAoB;wBACtB,MAAMjF,WAAWJ,8BAA8BkH;wBAC/Cf,QAAQ,MAAMQ,YAAYvG,UAAU0E;oBACtC,OAAO;wBACLqB,QAAQ,MAAMQ,YAAYO,YAAYpC;oBACxC;gBACF;YACF;YAEA,aAAa;YACb,cAAc;YACd,mBAAmB;YACnB,iBAAiB;YAEjB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAEhB,wEAAwE;YACxE,8EAA8E;YAE9E,IAAIoE;YACJ,IAAI;gBACFA,cAAcpE,eAAe,CAACI,SAAS,CAACiE,EAAE;YAC5C,EAAE,OAAO1E,KAAK;gBACZ,yEAAyE;gBACzE,8EAA8E;gBAC9EC,QAAQC,KAAK,CACX,CAAC,8BAA8B,EAAEO,SAAS,2DAA2D,CAAC;gBAExG,OAAO;oBACLY,MAAM;gBACR;YACF;YAEA,MAAMsD,gBACJvE,aAAawE,YAAY,CAAC3B,OAAO,CAACwB,YAAY,CAAChE,SAAS;YAE1D,MAAMoE,YAAY,MAAMF,cAAcG,KAAK,CAAC,MAAMpD;YAElD,4DAA4D;YAC5D,IAAIZ,eAAe;gBACjB,MAAMpD,sBAAsBf,KAAK;oBAC/BgB;oBACAC;gBACF;gBAEA2D,eAAe,MAAMjB,eAAeE,KAAK;oBACvCe,cAAc1D,QAAQkH,OAAO,CAACF;oBAC9B,6EAA6E;oBAC7ErD,YAAY,CAAC7D,sBAAsB8D,kBAAkB;gBACvD;YACF;QACF;QAEA,OAAO;YACLJ,MAAM;YACNC,QAAQC;YACRK;QACF;IACF,EAAE,OAAO5B,KAAK;QACZ,IAAIlF,gBAAgBkF,MAAM;YACxB,MAAMvB,cAAc5D,wBAAwBmF;YAE5C,qEAAqE;YACrE,2CAA2C;YAC3C,MAAMtC,sBAAsBf,KAAK;gBAC/BgB;gBACAC;YACF;YAEA,IAAIkD,eAAe;gBACjB,OAAO;oBACLO,MAAM;oBACNC,QAAQ,MAAM9C,2BACZ9B,KACAC,KACA8B,aACAd;gBAEJ;YACF;YAEA,IAAIqC,IAAI5B,cAAc,EAAE;gBACtB,MAAMnC,UAAU,IAAIwB;gBAEpB,gEAAgE;gBAChE,YAAY;gBACZ,IAAItC,qBAAqBc,SAAS+D,IAAI5B,cAAc,GAAG;oBACrDzB,IAAI0B,SAAS,CAAC,cAAc/B,MAAM8H,IAAI,CAACnI,QAAQ+I,MAAM;gBACvD;YACF;YAEArI,IAAI0B,SAAS,CAAC,YAAYI;YAC1B9B,IAAIuE,UAAU,GAAG;YACjB,OAAO;gBACLG,MAAM;gBACNC,QAAQ,IAAIvG,aAAa;YAC3B;QACF,OAAO,IAAIH,gBAAgBoF,MAAM;YAC/BrD,IAAIuE,UAAU,GAAG;YAEjB,MAAMxD,sBAAsBf,KAAK;gBAC/BgB;gBACAC;YACF;YAEA,IAAIkD,eAAe;gBACjB,MAAMK,UAAUtD,QAAQuD,MAAM,CAACpB;gBAC/B,IAAI;oBACF,MAAMmB;gBACR,EAAE,OAAM,CAAC;gBACT,OAAO;oBACLE,MAAM;oBACNC,QAAQ,MAAMhB,eAAeE,KAAK;wBAChCgB,YAAY;wBACZD,cAAcJ;wBACd8D,YAAY;oBACd;gBACF;YACF;YACA,OAAO;gBACL5D,MAAM;YACR;QACF;QAEA,IAAIP,eAAe;YACjBnE,IAAIuE,UAAU,GAAG;YACjB,MAAMrD,QAAQC,GAAG,CAACH,sBAAsBI,kBAAkB,IAAI,EAAE;YAChE,MAAMoD,UAAUtD,QAAQuD,MAAM,CAACpB;YAC/B,IAAI;gBACF,MAAMmB;YACR,EAAE,OAAM,CAAC;YAET,OAAO;gBACLE,MAAM;gBACNC,QAAQ,MAAMhB,eAAeE,KAAK;oBAChCe,cAAcJ;oBACd,6EAA6E;oBAC7EK,YAAY,CAAC7D,sBAAsB8D,kBAAkB;gBACvD;YACF;QACF;QAEA,MAAMzB;IACR;AACF"}