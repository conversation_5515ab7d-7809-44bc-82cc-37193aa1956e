{"version": 3, "sources": ["../../src/build/load-jsconfig.ts"], "names": ["loadJsConfig", "TSCONFIG_WARNED", "parseJsonFile", "filePath", "JSON5", "require", "contents", "readFileSync", "trim", "parse", "err", "isError", "codeFrameColumns", "codeFrame", "String", "start", "line", "lineNumber", "column", "columnNumber", "message", "highlightCode", "Error", "dir", "config", "typeScriptPath", "deps", "hasNecessaryDependencies", "pkg", "file", "exportsRestrict", "resolved", "get", "tsConfigPath", "path", "join", "typescript", "tsconfigPath", "useTypeScript", "Boolean", "fs", "existsSync", "implicit<PERSON><PERSON><PERSON>l", "jsConfig", "Log", "info", "ts", "Promise", "resolve", "tsConfig", "getTypeScriptConfiguration", "compilerOptions", "options", "dirname", "jsConfigPath", "resolvedBaseUrl", "baseUrl"], "mappings": ";;;;+BAuCA;;;eAA8BA;;;6DAvCb;4DACF;6DAEM;4CACsB;gEAEvB;0CACqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,IAAIC,kBAAkB;AAEtB,SAASC,cAAcC,QAAgB;IACrC,MAAMC,QAAQC,QAAQ;IACtB,MAAMC,WAAWC,IAAAA,gBAAY,EAACJ,UAAU;IAExC,6BAA6B;IAC7B,IAAIG,SAASE,IAAI,OAAO,IAAI;QAC1B,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOJ,MAAMK,KAAK,CAACH;IACrB,EAAE,OAAOI,KAAK;QACZ,IAAI,CAACC,IAAAA,gBAAO,EAACD,MAAM,MAAMA;QACzB,MAAM,EAAEE,gBAAgB,EAAE,GAAGP,QAAQ;QACrC,MAAMQ,YAAYD,iBAChBE,OAAOR,WACP;YACES,OAAO;gBACLC,MAAM,AAACN,IAAwCO,UAAU,IAAI;gBAC7DC,QAAQ,AAACR,IAA0CS,YAAY,IAAI;YACrE;QACF,GACA;YAAEC,SAASV,IAAIU,OAAO;YAAEC,eAAe;QAAK;QAE9C,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAEnB,SAAS,IAAI,EAAEU,UAAU,CAAC;IAChE;AACF;AAEe,eAAeb,aAC5BuB,GAAW,EACXC,MAA0B;IAE1B,IAAIC;IACJ,IAAI;QACF,MAAMC,OAAO,MAAMC,IAAAA,kDAAwB,EAACJ,KAAK;YAC/C;gBACEK,KAAK;gBACLC,MAAM;gBACNC,iBAAiB;YACnB;SACD;QACDL,iBAAiBC,KAAKK,QAAQ,CAACC,GAAG,CAAC;IACrC,EAAE,OAAM,CAAC;IACT,MAAMC,eAAeC,aAAI,CAACC,IAAI,CAACZ,KAAKC,OAAOY,UAAU,CAACC,YAAY;IAClE,MAAMC,gBAAgBC,QAAQd,kBAAkBe,WAAE,CAACC,UAAU,CAACR;IAE9D,IAAIS;IACJ,IAAIC;IACJ,mCAAmC;IACnC,IAAIL,eAAe;QACjB,IACEd,OAAOY,UAAU,CAACC,YAAY,KAAK,mBACnCpC,oBAAoB,OACpB;YACAA,kBAAkB;YAClB2C,KAAIC,IAAI,CAAC,CAAC,qBAAqB,EAAErB,OAAOY,UAAU,CAACC,YAAY,CAAC,CAAC;QACnE;QAEA,MAAMS,KAAM,MAAMC,QAAQC,OAAO,CAC/B3C,QAAQoB;QAEV,MAAMwB,WAAW,MAAMC,IAAAA,sDAA0B,EAACJ,IAAIb,cAAc;QACpEU,WAAW;YAAEQ,iBAAiBF,SAASG,OAAO;QAAC;QAC/CV,kBAAkBR,aAAI,CAACmB,OAAO,CAACpB;IACjC;IAEA,MAAMqB,eAAepB,aAAI,CAACC,IAAI,CAACZ,KAAK;IACpC,IAAI,CAACe,iBAAiBE,WAAE,CAACC,UAAU,CAACa,eAAe;QACjDX,WAAWzC,cAAcoD;QACzBZ,kBAAkBR,aAAI,CAACmB,OAAO,CAACC;IACjC;IAEA,IAAIC;IACJ,IAAIZ,UAAU;YACRA;QAAJ,KAAIA,4BAAAA,SAASQ,eAAe,qBAAxBR,0BAA0Ba,OAAO,EAAE;YACrCD,kBAAkBrB,aAAI,CAACc,OAAO,CAACzB,KAAKoB,SAASQ,eAAe,CAACK,OAAO;QACtE,OAAO;YACLD,kBAAkBb;QACpB;IACF;IAEA,OAAO;QACLJ;QACAK;QACAY;IACF;AACF"}