{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "names": ["Log", "NextBuildContext", "Worker", "origDebug", "path", "debug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNames", "config", "telemetryPlugin", "buildSpinner", "nextBuildSpan", "prunedBuildContext", "getWorker", "compilerName", "_worker", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "getStderr", "pipe", "stderr", "getStdout", "stdout", "worker", "_workerPool", "_workers", "_child", "on", "code", "signal", "console", "error", "combinedResult", "duration", "buildTraceContext", "curR<PERSON>ult", "worker<PERSON>ain", "buildContext", "end", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "stopAndPersist", "event", "webpackBuild", "with<PERSON><PERSON>ker", "webpackBuildImpl", "require"], "mappings": "AACA,YAAYA,SAAS,gBAAe;AACpC,SAASC,gBAAgB,QAAQ,mBAAkB;AAEnD,SAASC,MAAM,QAAQ,iCAAgC;AACvD,OAAOC,eAAe,2BAA0B;AAEhD,OAAOC,UAAU,OAAM;AAEvB,MAAMC,QAAQF,UAAU;AAExB,MAAMG,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACxDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACjB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAA+CZ,sBAAsB;IAErE,MAAM,EACJa,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,aAAa,EACb,GAAGC,oBACJ,GAAGtB;IAEJsB,mBAAmBhB,WAAW,GAAGA;IAEjC,MAAMiB,YAAY,CAACC;YAeK;QAdtB,MAAMC,UAAU,IAAIxB,OAAOE,KAAKuB,IAAI,CAACC,WAAW,YAAY;YAC1DC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;gBAC7B;YACF;QACF;QACAT,QAAQU,SAAS,GAAGC,IAAI,CAACH,QAAQI,MAAM;QACvCZ,QAAQa,SAAS,GAAGF,IAAI,CAACH,QAAQM,MAAM;QAEvC,KAAK,MAAMC,UAAW,EAAA,sBAAA,AAACf,QAAgBgB,WAAW,qBAA5B,oBAA8BC,QAAQ,KAAI,EAAE,CAE7D;YACHF,OAAOG,MAAM,CAACC,EAAE,CAAC,QAAQ,CAACC,MAAMC;gBAC9B,IAAID,QAASC,UAAUA,WAAW,UAAW;oBAC3CC,QAAQC,KAAK,CACX,CAAC,SAAS,EAAExB,aAAa,gCAAgC,EAAEqB,KAAK,aAAa,EAAEC,OAAO,CAAC;gBAE3F;YACF;QACF;QAEA,OAAOrB;IACT;IAEA,MAAMwB,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,KAAK,MAAM3B,gBAAgBP,cAAe;YAgBpCmC;QAfJ,MAAMZ,SAASjB,UAAUC;QAEzB,MAAM4B,YAAY,MAAMZ,OAAOa,UAAU,CAAC;YACxCC,cAAchC;YACdE;QACF;QACA,0DAA0D;QAC1D,MAAMgB,OAAOe,GAAG;QAEhB,sBAAsB;QACtBjD,cAAcC,UAAUD,aAAa8C,UAAU9C,WAAW;QAC1DgB,mBAAmBhB,WAAW,GAAGA;QAEjC2C,eAAeC,QAAQ,IAAIE,UAAUF,QAAQ;QAE7C,KAAIE,+BAAAA,UAAUD,iBAAiB,qBAA3BC,6BAA6BI,YAAY,EAAE;gBAUzCJ;YATJ,MAAM,EAAEK,YAAY,EAAE,GAAGL,UAAUD,iBAAiB,CAACK,YAAY;YAEjE,IAAIC,cAAc;gBAChBR,eAAeE,iBAAiB,CAACK,YAAY,GAC3CJ,UAAUD,iBAAiB,CAACK,YAAY;gBAC1CP,eAAeE,iBAAiB,CAACK,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAIL,gCAAAA,UAAUD,iBAAiB,qBAA3BC,8BAA6BM,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAGP,UAAUD,iBAAiB,CAACO,WAAW;gBAErE,IAAIC,mBAAmB;oBACrBV,eAAeE,iBAAiB,CAACO,WAAW,GAC1CN,UAAUD,iBAAiB,CAACO,WAAW;oBAEzCT,eAAeE,iBAAiB,CAACO,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAI1C,cAAc2C,MAAM,KAAK,GAAG;QAC9BxC,gCAAAA,aAAcyC,cAAc;QAC5B9D,IAAI+D,KAAK,CAAC;IACZ;IAEA,OAAOb;AACT;AAEA,OAAO,SAASc,aACdC,UAAmB,EACnB/C,aAA6C;IAE7C,IAAI+C,YAAY;QACd5D,MAAM;QACN,OAAOY,uBAAuBC;IAChC,OAAO;QACLb,MAAM;QACN,MAAM6D,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,OAAOA;IACT;AACF"}