{"version": 3, "sources": ["../../src/build/create-compiler-aliases.ts"], "names": ["path", "DOT_NEXT_ALIAS", "PAGES_DIR_ALIAS", "ROOT_DIR_ALIAS", "APP_DIR_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "defaultOverrides", "NEXT_PROJECT_ROOT", "NEXT_PROJECT_ROOT_DIST", "hasExternalOtelApiPackage", "WEBPACK_LAYERS", "createWebpackAliases", "isClient", "isEdgeServer", "isNodeServer", "dev", "config", "pagesDir", "appDir", "dir", "reactProductionProfiling", "hasRewrites", "distDir", "join", "pageExtensions", "clientResolveRewrites", "require", "resolve", "customAppAliases", "customDocumentAliases", "nextDistPath", "reduce", "prev", "ext", "push", "undefined", "images", "loaderFile", "next", "getOptimizedModuleAliases", "getReactProfilingInProduction", "getBarrelOptimizationAliases", "experimental", "optimizePackageImports", "dirname", "setimmediate", "createServerOnlyClientOnlyAliases", "isServer", "createRSCAliases", "bundledReactChannel", "layer", "alias", "react$", "serverSideRendering", "Object", "assign", "reactServerComponents", "unfetch", "url", "packages", "aliases", "mainFields", "pkg", "descriptionFileData", "descriptionFilePath", "field", "hasOwnProperty", "createServerComponentsNoopAliases"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SACEC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,aAAa,EACbC,sBAAsB,EACtBC,+BAA+B,EAC/BC,yBAAyB,EACzBC,2BAA2B,QAEtB,mBAAkB;AAEzB,SAASC,gBAAgB,QAAQ,yBAAwB;AACzD,SACEC,iBAAiB,EACjBC,sBAAsB,EACtBC,yBAAyB,QACpB,mBAAkB;AACzB,SAASC,cAAc,QAAQ,mBAAkB;AAMjD,OAAO,SAASC,qBAAqB,EACnCC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,wBAAwB,EACxBC,WAAW,EAYZ;IACC,MAAMC,UAAUzB,KAAK0B,IAAI,CAACJ,KAAKH,OAAOM,OAAO;IAC7C,MAAME,iBAAiBR,OAAOQ,cAAc;IAC5C,MAAMC,wBAAwBC,QAAQC,OAAO,CAC3C;IAEF,MAAMC,mBAAoC,CAAC;IAC3C,MAAMC,wBAAyC,CAAC;IAEhD,oDAAoD;IACpD,qDAAqD;IACrD,sCAAsC;IACtC,IAAId,KAAK;QACP,MAAMe,eAAe,eAAgBjB,CAAAA,eAAe,SAAS,EAAC;QAC9De,gBAAgB,CAAC,CAAC,EAAE7B,gBAAgB,KAAK,CAAC,CAAC,GAAG;eACxCkB,WACAO,eAAeO,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACrC,KAAK0B,IAAI,CAACN,UAAU,CAAC,KAAK,EAAEgB,IAAI,CAAC;gBAC3C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEF,aAAa,aAAa,CAAC;SAC/B;QACDF,gBAAgB,CAAC,CAAC,EAAE7B,gBAAgB,OAAO,CAAC,CAAC,GAAG;eAC1CkB,WACAO,eAAeO,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACrC,KAAK0B,IAAI,CAACN,UAAU,CAAC,OAAO,EAAEgB,IAAI,CAAC;gBAC7C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEF,aAAa,eAAe,CAAC;SACjC;QACDD,qBAAqB,CAAC,CAAC,EAAE9B,gBAAgB,UAAU,CAAC,CAAC,GAAG;eAClDkB,WACAO,eAAeO,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACrC,KAAK0B,IAAI,CAACN,UAAU,CAAC,UAAU,EAAEgB,IAAI,CAAC;gBAChD,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEF,aAAa,kBAAkB,CAAC;SACpC;IACH;IAEA,OAAO;QACL,cAAc;QAEd,mDAAmD;QACnD,0CAA0C;QAC1C,GAAIjB,eACA;YACE,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,mBAAmB;YACnB,iBAAiB;YACjB,oBAAoB;YAEpB,sCAAsC;YACtC,CAAChB,KAAK0B,IAAI,CAAChB,mBAAmB,UAAU,EACtC;YACF,CAACV,KAAK0B,IAAI,CAAChB,mBAAmB,MAAM,EAClC;YACF,CAACV,KAAK0B,IAAI,CAACf,wBAAwB,UAAU,QAAQ,EACnD;YACF,CAACX,KAAK0B,IAAI,CACRhB,mBACA,QACA,UACA,OACA,kBACA,EAAE;YACJ,CAACV,KAAK0B,IAAI,CAACf,wBAAwB,UAAU,UAAU,EACrD;YACF,CAACX,KAAK0B,IAAI,CAACf,wBAAwB,UAAU,UAAU,EACrD;YACF,CAACX,KAAK0B,IAAI,CAACf,wBAAwB,UAAU,OAAO,QAAQ,EAC1D;YACF,CAACX,KAAK0B,IAAI,CAACf,wBAAwB,UAAU,OAAO,WAAW,EAC7D;YACF,CAACX,KAAK0B,IAAI,CAACf,wBAAwB,SAAS,aAAa,EACvD;YACF,CAACX,KAAK0B,IAAI,CAACf,wBAAwB,SAAS,QAAQ,EAClD;YACF,CAACX,KAAK0B,IAAI,CACRf,wBACA,UACA,cACA,cACA,EAAE;YACJ,CAACX,KAAK0B,IAAI,CACRf,wBACA,UACA,cACA,WACA,EAAE;QACN,IACA2B,SAAS;QAEb,wBAAwB;QACxB,GAAI,CAAC1B,+BAA+B;YAClC,sBAAsB;QACxB,CAAC;QAED,GAAIO,OAAOoB,MAAM,CAACC,UAAU,GACxB;YACE,qCAAqCrB,OAAOoB,MAAM,CAACC,UAAU;YAC7D,GAAIxB,gBAAgB;gBAClB,yCAAyCG,OAAOoB,MAAM,CAACC,UAAU;YACnE,CAAC;QACH,IACAF,SAAS;QAEbG,MAAM/B;QAEN,qBAAqBD,gBAAgB,CAAC,mBAAmB;QACzD,eAAeA,gBAAgB,CAAC,aAAa;QAE7C,GAAGsB,gBAAgB;QACnB,GAAGC,qBAAqB;QAExB,GAAIZ,WAAW;YAAE,CAAClB,gBAAgB,EAAEkB;QAAS,IAAI,CAAC,CAAC;QACnD,GAAIC,SAAS;YAAE,CAACjB,cAAc,EAAEiB;QAAO,IAAI,CAAC,CAAC;QAC7C,CAAClB,eAAe,EAAEmB;QAClB,CAACrB,eAAe,EAAEwB;QAClB,GAAIV,YAAYC,eAAe0B,8BAA8B,CAAC,CAAC;QAC/D,GAAInB,2BAA2BoB,kCAAkC,CAAC,CAAC;QAEnE,wEAAwE;QACxE,6BAA6B;QAC7B,GAAI1B,eACA2B,6BACEzB,OAAO0B,YAAY,CAACC,sBAAsB,IAAI,EAAE,IAElD,CAAC,CAAC;QAEN,CAACvC,0BAA0B,EACzB;QAEF,CAACD,gCAAgC,EAC/B;QAEF,CAACD,uBAAuB,EACtB;QAEF,CAACG,4BAA4B,EAC3B;QAEF,GAAIO,YAAYC,eACZ;YACE,CAACY,sBAAsB,EAAEJ,cACrBI,wBAEA;QACN,IACA,CAAC,CAAC;QAEN,kBAAkB5B,KAAK0B,IAAI,CACzB1B,KAAK+C,OAAO,CAAClB,QAAQC,OAAO,CAAC,+BAC7B;QAGFkB,cAAc;IAChB;AACF;AAEA,OAAO,SAASC,kCACdC,QAAiB;IAEjB,OAAOA,WACH;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,mCACE;IACJ,IACA;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,kCACE;IACJ;AACN;AAEA,OAAO,SAASC,iBACdC,mBAA2B,EAC3B,EACEC,KAAK,EACLrC,YAAY,EACZO,wBAAwB,EAKzB;IAED,IAAI+B,QAAgC;QAClCC,QAAQ,CAAC,wBAAwB,EAAEH,oBAAoB,CAAC;QACxD,cAAc,CAAC,4BAA4B,EAAEA,oBAAoB,CAAC;QAClE,sBAAsB,CAAC,wBAAwB,EAAEA,oBAAoB,YAAY,CAAC;QAClF,0BAA0B,CAAC,wBAAwB,EAAEA,oBAAoB,gBAAgB,CAAC;QAC1F,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,gDAAgD,CAAC;QACvE,0BAA0B,CAAC,qDAAqD,CAAC;QACjF,6BAA6B,CAAC,wDAAwD,CAAC;QACvF,0BAA0B,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;QAC1F,6BAA6B,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;QAChG,oCAAoC,CAAC,2CAA2C,EAAEA,oBAAoB,OAAO,CAAC;QAC9G,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,+DAA+D;QAC/D,2DAA2D,CAAC,4CAA4C,CAAC;QACzG,wDAAwD,CAAC,4CAA4C,CAAC;IACxG;IAEA,IAAI,CAACpC,cAAc;QACjB,IAAIqC,UAAUxC,eAAe2C,mBAAmB,EAAE;YAChDF,QAAQG,OAAOC,MAAM,CAACJ,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF,OAAO,IAAIA,UAAUxC,eAAe8C,qBAAqB,EAAE;YACzDL,QAAQG,OAAOC,MAAM,CAACJ,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;gBAChJ,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF;IACF;IAEA,IAAIrC,cAAc;QAChB,IAAIqC,UAAUxC,eAAe8C,qBAAqB,EAAE;YAClDL,KAAK,CACH,SACD,GAAG,CAAC,wBAAwB,EAAEF,oBAAoB,oBAAoB,CAAC;QAC1E;QACA,4CAA4C;QAC5C,sDAAsD;QACtDE,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,sBAAsB,CAAC;IAChF;IAEA,IAAI7B,0BAA0B;QAC5B+B,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,UAAU,CAAC;IACpE;IAEAE,KAAK,CACH,gEACD,GAAG,CAAC,uCAAuC,CAAC;IAE7C,OAAOA;AACT;AAEA,oEAAoE;AACpE,qEAAqE;AACrE,OAAO,SAASZ;IACd,OAAO;QACLkB,SAAS/B,QAAQC,OAAO,CAAC;QACzB,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gBAAgBD,QAAQC,OAAO,CAC7B;QAEF,iBAAiBD,QAAQC,OAAO,CAC9B;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gCAAgCD,QAAQC,OAAO,CAC7C;QAEF,0BAA0BD,QAAQC,OAAO,CACvC;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF+B,KAAKhC,QAAQC,OAAO,CAAC;IACvB;AACF;AAEA,gEAAgE;AAChE,SAASc,6BAA6BkB,QAAkB;IACtD,MAAMC,UAAqC,CAAC;IAC5C,MAAMC,aAAa;QAAC;QAAU;KAAO;IAErC,KAAK,MAAMC,OAAOH,SAAU;QAC1B,IAAI;YACF,MAAMI,sBAAsBrC,QAAQ,CAAC,EAAEoC,IAAI,aAAa,CAAC;YACzD,MAAME,sBAAsBtC,QAAQC,OAAO,CAAC,CAAC,EAAEmC,IAAI,aAAa,CAAC;YAEjE,KAAK,MAAMG,SAASJ,WAAY;gBAC9B,IAAIE,oBAAoBG,cAAc,CAACD,QAAQ;oBAC7CL,OAAO,CAACE,MAAM,IAAI,GAAGjE,KAAK0B,IAAI,CAC5B1B,KAAK+C,OAAO,CAACoB,sBACbD,mBAAmB,CAACE,MAAM;oBAE5B;gBACF;YACF;QACF,EAAE,OAAM,CAAC;IACX;IAEA,OAAOL;AACT;AACA,SAASpB;IACP,OAAO;QACL,cAAc;IAChB;AACF;AACA,OAAO,SAAS2B;IACd,OAAO;QACL,CAACzC,QAAQC,OAAO,CAAC,aAAa,EAAED,QAAQC,OAAO,CAC7C;QAEF,qBAAqB;QACrB,CAACD,QAAQC,OAAO,CAAC,gBAAgB,EAAED,QAAQC,OAAO,CAChD;IAEJ;AACF"}