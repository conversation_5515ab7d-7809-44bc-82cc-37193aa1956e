import { DynamicServerError } from "./hooks-server-context";
import { maybePostpone } from "./maybe-postpone";
import { staticGenerationAsyncStorage } from "./static-generation-async-storage.external";
class StaticGenBailoutError extends Error {
    constructor(...args){
        super(...args);
        this.code = "NEXT_STATIC_GEN_BAILOUT";
    }
}
function formatErrorMessage(reason, opts) {
    const { dynamic, link } = opts || {};
    const suffix = link ? " See more info here: " + link : "";
    return "Page" + (dynamic ? ' with `dynamic = "' + dynamic + '"`' : "") + " couldn't be rendered statically because it used `" + reason + "`." + suffix;
}
export const staticGenerationBailout = (reason, opts)=>{
    const staticGenerationStore = staticGenerationAsyncStorage.getStore();
    if (!staticGenerationStore) return false;
    if (staticGenerationStore.forceStatic) {
        return true;
    }
    if (staticGenerationStore.dynamicShouldError) {
        var _opts_dynamic;
        throw new StaticGenBailoutError(formatErrorMessage(reason, {
            ...opts,
            dynamic: (_opts_dynamic = opts == null ? void 0 : opts.dynamic) != null ? _opts_dynamic : "error"
        }));
    }
    const message = formatErrorMessage(reason, {
        ...opts,
        // this error should be caught by Next to bail out of static generation
        // in case it's uncaught, this link provides some additional context as to why
        link: "https://nextjs.org/docs/messages/dynamic-server-error"
    });
    maybePostpone(staticGenerationStore, message);
    // As this is a bailout, we don't want to revalidate, so set the revalidate
    // to 0.
    staticGenerationStore.revalidate = 0;
    if (!(opts == null ? void 0 : opts.dynamic)) {
        // we can statically prefetch pages that opt into dynamic,
        // but not things like headers/cookies
        staticGenerationStore.staticPrefetchBailout = true;
    }
    if (staticGenerationStore.isStaticGeneration) {
        const err = new DynamicServerError(message);
        staticGenerationStore.dynamicUsageDescription = reason;
        staticGenerationStore.dynamicUsageStack = err.stack;
        throw err;
    }
    return false;
};

//# sourceMappingURL=static-generation-bailout.js.map