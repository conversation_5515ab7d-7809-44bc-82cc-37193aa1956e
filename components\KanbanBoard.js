import { useState } from 'react';
import {
  DndContext,
  DragOverlay,
  closestCorners,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useTaskStore } from '../lib/store';
import KanbanColumn from './KanbanColumn';
import TaskCard from './TaskCard';

const columns = [
  {
    id: 'todo',
    title: 'To Do',
    color: 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700',
    headerColor: 'text-gray-700 dark:text-gray-300'
  },
  {
    id: 'inprogress',
    title: 'In Progress',
    color: 'bg-primary/5 dark:bg-primary/10 border-primary/20 dark:border-primary/30',
    headerColor: 'text-primary'
  },
  {
    id: 'done',
    title: 'Done',
    color: 'bg-accent/10 dark:bg-accent/20 border-accent/30 dark:border-accent/40',
    headerColor: 'text-accent'
  }
];

export default function KanbanBoard() {
  const { tasks, moveTask } = useTaskStore();
  const [activeId, setActiveId] = useState(null);
  
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const getTasksByStatus = (status) => {
    return tasks.filter(task => task.status === status);
  };

  const findContainer = (id) => {
    if (id in tasks) {
      return id;
    }
    
    const task = tasks.find(task => task.id === id);
    return task ? task.status : null;
  };

  const handleDragStart = (event) => {
    const { active } = event;
    setActiveId(active.id);
  };

  const handleDragOver = (event) => {
    const { active, over } = event;
    const overId = over?.id;

    if (!overId) return;

    const activeContainer = findContainer(active.id);
    const overContainer = findContainer(overId);

    if (!activeContainer || !overContainer || activeContainer === overContainer) {
      return;
    }
  };

  const handleDragEnd = async (event) => {
    const { active, over } = event;
    
    if (!over) {
      setActiveId(null);
      return;
    }

    const activeId = active.id;
    const overId = over.id;

    const activeTask = tasks.find(task => task.id === activeId);
    if (!activeTask) {
      setActiveId(null);
      return;
    }

    let newStatus = activeTask.status;

    if (columns.some(col => col.id === overId)) {
      newStatus = overId;
    } else {
      const overTask = tasks.find(task => task.id === overId);
      if (overTask) {
        newStatus = overTask.status;
      }
    }

    if (newStatus !== activeTask.status) {
      try {
        await moveTask(activeId, newStatus);
      } catch (error) {
        console.error('Failed to move task:', error);
      }
    }

    setActiveId(null);
  };

  const activeTask = activeId ? tasks.find(task => task.id === activeId) : null;

  return (
    <div className="h-full p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Task Board</h1>
        <p className="text-gray-600 dark:text-gray-400">Drag and drop tasks between columns</p>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
          {columns.map((column) => (
            <SortableContext
              key={column.id}
              items={getTasksByStatus(column.id).map(task => task.id)}
              strategy={verticalListSortingStrategy}
            >
              <KanbanColumn
                id={column.id}
                title={column.title}
                color={column.color}
                headerColor={column.headerColor}
                tasks={getTasksByStatus(column.id)}
              />
            </SortableContext>
          ))}
        </div>

        <DragOverlay>
          {activeTask ? <TaskCard task={activeTask} isDragging /> : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
