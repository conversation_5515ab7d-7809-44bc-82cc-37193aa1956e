{"version": 3, "sources": ["../../../src/build/swc/index.ts"], "names": ["getSupportedArchTriples", "lockfilePatchPromise", "loadBindings", "createDefineEnv", "isWasm", "transform", "transformSync", "minify", "minifySync", "parse", "getBinaryMetadata", "initCustomTraceSubscriber", "initHeapProfiler", "teardownHeapProfiler", "teardownTraceSubscriber", "teardownCrashReporter", "nextVersion", "process", "env", "__NEXT_VERSION", "Arch<PERSON>ame", "arch", "PlatformName", "platform", "infoLog", "args", "NEXT_PRIVATE_BUILD_WORKER", "DEBUG", "Log", "info", "darwin", "win32", "linux", "platformArchTriples", "arm64", "ia32", "filter", "triple", "abi", "x64", "triples", "supportedArchTriples", "targetTriple", "rawTargetTriple", "warn", "__INTERNAL_CUSTOM_TURBOPACK_BINDINGS", "checkVersionMismatch", "pkgData", "version", "lastNativeBindingsLoadErrorCode", "undefined", "nativeBindings", "wasmBindings", "downloadWasmPromise", "pendingBindings", "swcTraceFlushGuard", "swcHeapProfilerFlushGuard", "swcCrashReporterFlushGuard", "downloadNativeBindingsPromise", "stdout", "_handle", "setBlocking", "stderr", "Promise", "resolve", "_reject", "cur", "patchIncorrectLockfile", "cwd", "catch", "console", "error", "attempts", "loadNative", "a", "Array", "isArray", "every", "m", "includes", "fallback<PERSON><PERSON><PERSON>", "tryLoadNativeWithFallback", "concat", "logLoadFailure", "nativeBindingsDirectory", "path", "join", "dirname", "require", "downloadNativeNextSwc", "map", "platformArchABI", "bindings", "tryLoadWasmWithFallback", "loadWasm", "eventSwcLoadFailure", "wasm", "nativeBindingsErrorCode", "wasmDirectory", "downloadWasmSwc", "pathToFileURL", "href", "attempt", "loadBindingsSync", "loggingLoadFailure", "then", "finally", "exit", "isTurbopack", "allowedRevalidateHeaderKeys", "clientRouterFilters", "config", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "middlewareMatchers", "previewModeId", "defineEnv", "client", "edge", "nodejs", "variant", "Object", "keys", "rustifyEnv", "getDefineEnv", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "entries", "_", "value", "name", "bindingToApi", "binding", "_wasm", "cancel", "Cancel", "Error", "invariant", "never", "computeMessage", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "nativeError", "message", "cause", "subscribe", "useBuffer", "nativeFunction", "buffer", "waiting", "canceled", "emitResult", "err", "reject", "item", "push", "iterator", "task", "length", "shift", "e", "rootTaskDispose", "return", "done", "rustifyProjectOptions", "options", "nextConfig", "serializeNextConfig", "jsConfig", "JSON", "stringify", "ProjectImpl", "constructor", "nativeProject", "_nativeProject", "update", "projectUpdate", "entrypointsSubscribe", "subscription", "callback", "projectEntrypointsSubscribe", "entrypoints", "routes", "Map", "pathname", "nativeRoute", "route", "routeType", "type", "htmlEndpoint", "EndpointImpl", "dataEndpoint", "endpoint", "rscEndpoint", "_exhaustiveCheck", "set", "napiMiddlewareToMiddleware", "middleware", "runtime", "matcher", "pagesDocumentEndpoint", "pagesAppEndpoint", "pagesErrorEndpoint", "issues", "diagnostics", "hmrEvents", "identifier", "projectHmrEvents", "hmrIdentifiersSubscribe", "projectHmrIdentifiersSubscribe", "traceSource", "stackFrame", "projectTraceSource", "getSourceForAsset", "filePath", "projectGetSourceForAsset", "updateInfoSubscribe", "projectUpdateInfoSubscribe", "nativeEndpoint", "_nativeEndpoint", "writeToDisk", "endpointWriteToDisk", "clientChanged", "clientSubscription", "endpointClientChangedSubscribe", "next", "serverChanged", "includeIssues", "serverSubscription", "endpointServerChangedSubscribe", "nextConfigSerializable", "generateBuildId", "exportPathMap", "webpack", "experimental", "turbo", "rules", "ensureLoadersHaveSerializableOptions", "modularizeImports", "fromEntries", "mod", "key", "turbopackRules", "glob", "rule", "loaderItems", "loaders", "loaderItem", "isDeepStrictEqual", "loader", "createProject", "turboEngineOptions", "projectNew", "importPath", "pkg", "pkgPath", "default", "src", "toString", "parseSync", "astStr", "getTargetTriple", "startTrace", "stream", "turboTasks", "rootDir", "applicationDir", "pageExtensions", "callbackFn", "streamEntrypoints", "get", "getEntrypoints", "mdx", "compile", "mdxCompile", "getMdxOptions", "compileSync", "mdxCompileSync", "code", "customBindings", "isModule", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsc", "parser", "syntax", "<PERSON><PERSON><PERSON><PERSON>", "nextBuild", "ret", "runTurboTracing", "exact", "createTurboTasks", "memoryLimit", "development", "jsx", "gfmStrikethroughSingleTilde", "mathTextSingleDollar", "t", "from", "parserOptions", "getParserOptions", "target", "traceFileName", "flushed"], "mappings": "AAAA,0DAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgC7CA,uBAAuB;eAAvBA;;IAoFAC,oBAAoB;eAApBA;;IAmCSC,YAAY;eAAZA;;IAwNNC,eAAe;eAAfA;;IAy6BMC,MAAM;eAANA;;IAKAC,SAAS;eAATA;;IAKNC,aAAa;eAAbA;;IAKMC,MAAM;eAANA;;IAKNC,UAAU;eAAVA;;IAKMC,KAAK;eAALA;;IAQNC,iBAAiB;eAAjBA;;IAiBHC,yBAAyB;eAAzBA;;IAcAC,gBAAgB;eAAhBA;;IAiBAC,oBAAoB;eAApBA;;IA0BAC,uBAAuB;eAAvBA;;IAiBAC,qBAAqB;eAArBA;;;6DAn5CI;qBACa;oBACC;yBACK;6DACf;yBACY;gCACG;wCACG;6BACgB;sBAErB;iCACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG7B,MAAMC,cAAcC,QAAQC,GAAG,CAACC,cAAc;AAE9C,MAAMC,WAAWC,IAAAA,QAAI;AACrB,MAAMC,eAAeC,IAAAA,YAAQ;AAE7B,MAAMC,UAAU,CAAC,GAAGC;IAClB,IAAIR,QAAQC,GAAG,CAACQ,yBAAyB,EAAE;QACzC;IACF;IACA,IAAIT,QAAQC,GAAG,CAACS,KAAK,EAAE;QACrBC,KAAIC,IAAI,IAAIJ;IACd;AACF;AAKO,MAAMzB,0BAAqD;IAChE,MAAM,EAAE8B,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE,GAAGC,4BAAmB;IAEpD,OAAO;QACLH;QACAC,OAAO;YACLG,OAAOH,MAAMG,KAAK;YAClBC,MAAMJ,MAAMI,IAAI,CAACC,MAAM,CACrB,CAACC,SAA4BA,OAAOC,GAAG,KAAK;YAE9CC,KAAKR,MAAMQ,GAAG,CAACH,MAAM,CAAC,CAACC,SAA4BA,OAAOC,GAAG,KAAK;QACpE;QACAN,OAAO;YACL,mDAAmD;YACnDO,KAAKP,MAAMO,GAAG,CAACH,MAAM,CACnB,CAACC,SAA4BA,OAAOC,GAAG,KAAK;YAE9CJ,OAAOF,MAAME,KAAK;QACpB;IACF;AACF;AAEA,MAAMM,UAAU,AAAC,CAAA;QAEMC,oCASCR;IAVtB,MAAMQ,uBAAuBzC;IAC7B,MAAM0C,gBAAeD,qCAAAA,oBAAoB,CAACnB,aAAa,qBAAlCmB,kCAAoC,CAACrB,SAAS;IAEnE,oDAAoD;IACpD,IAAIsB,cAAc;QAChB,OAAOA;IACT;IAEA,yHAAyH;IACzH,qDAAqD;IACrD,IAAIC,mBAAkBV,oCAAAA,4BAAmB,CAACX,aAAa,qBAAjCW,iCAAmC,CAACb,SAAS;IAEnE,IAAIuB,iBAAiB;QACnBf,KAAIgB,IAAI,CACN,CAAC,0CAA0C,EAAED,gBAAgB,kCAAkC,CAAC;IAEpG,OAAO;QACLf,KAAIgB,IAAI,CACN,CAAC,kDAAkD,EAAEtB,aAAa,CAAC,EAAEF,SAAS,CAAC;IAEnF;IAEA,OAAO,EAAE;AACX,CAAA;AAEA,4EAA4E;AAC5E,qGAAqG;AACrG,oGAAoG;AACpG,kFAAkF;AAClF,EAAE;AACF,yEAAyE;AACzE,MAAMyB,uCACJ5B,QAAQC,GAAG,CAAC2B,oCAAoC;AAElD,SAASC,qBAAqBC,OAAY;IACxC,MAAMC,UAAUD,QAAQC,OAAO;IAE/B,IAAIA,WAAWA,YAAYhC,aAAa;QACtCY,KAAIgB,IAAI,CACN,CAAC,yCAAyC,EAAEI,QAAQ,qBAAqB,EAAEhC,YAAY,2BAA2B,CAAC;IAEvH;AACF;AAEA,oFAAoF;AACpF,gGAAgG;AAChG,oGAAoG;AACpG,IAAIiC,kCAIYC;AAChB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC,gCAA2DR;AAExD,MAAMjD,uBAAgD,CAAC;AAmCvD,eAAeC;IACpB,IAAIoD,iBAAiB;QACnB,OAAOA;IACT;IAEA,iIAAiI;IACjI,qDAAqD;IACrD,uFAAuF;IACvF,IAAIrC,QAAQ0C,MAAM,CAACC,OAAO,IAAI,MAAM;QAClC,aAAa;QACb3C,QAAQ0C,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC;IACrC;IACA,IAAI5C,QAAQ6C,MAAM,CAACF,OAAO,IAAI,MAAM;QAClC,aAAa;QACb3C,QAAQ6C,MAAM,CAACF,OAAO,CAACC,WAAW,CAAC;IACrC;IAEAP,kBAAkB,IAAIS,QAAQ,OAAOC,SAASC;QAC5C,IAAI,CAAChE,qBAAqBiE,GAAG,EAAE;YAC7B,yDAAyD;YACzD,0CAA0C;YAC1CjE,qBAAqBiE,GAAG,GAAGC,IAAAA,8CAAsB,EAAClD,QAAQmD,GAAG,IAAIC,KAAK,CACpEC,QAAQC,KAAK;QAEjB;QAEA,IAAIC,WAAkB,EAAE;QAExB,4CAA4C;QAC5C,EAAE;QACF,kEAAkE;QAClE,0GAA0G;QAC1G,gHAAgH;QAChH,kHAAkH;QAClH,kDAAkD;QAClD,IAAI;YACF,OAAOR,QAAQS;QACjB,EAAE,OAAOC,GAAG;YACV,IACEC,MAAMC,OAAO,CAACF,MACdA,EAAEG,KAAK,CAAC,CAACC,IAAMA,EAAEC,QAAQ,CAAC,0BAC1B;gBACA,IAAIC,mBAAmB,MAAMC,0BAA0BT;gBAEvD,IAAIQ,kBAAkB;oBACpB,OAAOhB,QAAQgB;gBACjB;YACF;YAEAR,WAAWA,SAASU,MAAM,CAACR;QAC7B;QAEAS,eAAeX;IACjB;IACA,OAAOlB;AACT;AAEA,eAAe2B,0BAA0BT,QAAuB;IAC9D,MAAMY,0BAA0BC,aAAI,CAACC,IAAI,CACvCD,aAAI,CAACE,OAAO,CAACC,QAAQxB,OAAO,CAAC,uBAC7B;IAGF,IAAI,CAACN,+BAA+B;QAClCA,gCAAgC+B,IAAAA,kCAAqB,EACnDzE,aACAoE,yBACA5C,QAAQkD,GAAG,CAAC,CAACrD,SAAgBA,OAAOsD,eAAe;IAEvD;IACA,MAAMjC;IAEN,IAAI;QACF,IAAIkC,WAAWnB,WAAWW;QAC1B,OAAOQ;IACT,EAAE,OAAOlB,GAAQ;QACfF,SAASU,MAAM,CAACR;IAClB;IACA,OAAOxB;AACT;AAEA,uFAAuF;AACvF,6DAA6D;AAC7D,eAAe2C,wBAAwBrB,QAAa;IAClD,IAAI;QACF,IAAIoB,WAAW,MAAME,SAAS;QAC9B,sDAAsD;QACtDC,IAAAA,mCAAmB,EAAC;YAClBC,MAAM;YACNC,yBAAyBhD;QAC3B;QACA,OAAO2C;IACT,EAAE,OAAOlB,GAAG;QACVF,WAAWA,SAASU,MAAM,CAACR;IAC7B;IAEA,IAAI;QACF,2DAA2D;QAC3D,+DAA+D;QAC/D,sEAAsE;QACtE,sDAAsD;QACtD,MAAMwB,gBAAgBb,aAAI,CAACC,IAAI,CAC7BD,aAAI,CAACE,OAAO,CAACC,QAAQxB,OAAO,CAAC,uBAC7B;QAEF,IAAI,CAACX,qBAAqB;YACxBA,sBAAsB8C,IAAAA,4BAAe,EAACnF,aAAakF;QACrD;QACA,MAAM7C;QACN,IAAIuC,WAAW,MAAME,SAASM,IAAAA,kBAAa,EAACF,eAAeG,IAAI;QAC/D,sDAAsD;QACtDN,IAAAA,mCAAmB,EAAC;YAClBC,MAAM;YACNC,yBAAyBhD;QAC3B;QAEA,4CAA4C;QAC5C,sCAAsC;QACtC,KAAK,MAAMqD,WAAW9B,SAAU;YAC9B5C,KAAIgB,IAAI,CAAC0D;QACX;QACA,OAAOV;IACT,EAAE,OAAOlB,GAAG;QACVF,WAAWA,SAASU,MAAM,CAACR;IAC7B;AACF;AAEA,SAAS6B;IACP,IAAI/B,WAAkB,EAAE;IACxB,IAAI;QACF,OAAOC;IACT,EAAE,OAAOC,GAAG;QACVF,WAAWA,SAASU,MAAM,CAACR;IAC7B;IAEAS,eAAeX;AACjB;AAEA,IAAIgC,qBAAqB;AAEzB,SAASrB,eAAeX,QAAa;IACnC,4DAA4D;IAC5D,IAAIgC,oBAAoB;IACxBA,qBAAqB;IAErB,KAAK,IAAIF,WAAW9B,SAAU;QAC5B5C,KAAIgB,IAAI,CAAC0D;IACX;IAEA,sDAAsD;IACtDP,IAAAA,mCAAmB,EAAC;QAClBE,yBAAyBhD;IAC3B,GACGwD,IAAI,CAAC,IAAMxG,qBAAqBiE,GAAG,IAAIH,QAAQC,OAAO,IACtD0C,OAAO,CAAC;QACP9E,KAAI2C,KAAK,CACP,CAAC,8BAA8B,EAAEjD,aAAa,CAAC,EAAEF,SAAS,yEAAyE,CAAC;QAEtIH,QAAQ0F,IAAI,CAAC;IACf;AACJ;AAwDO,SAASxG,gBAAgB,EAC9ByG,WAAW,EACXC,2BAA2B,EAC3BC,mBAAmB,EACnBC,MAAM,EACNC,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,kBAAkB,EAClBC,aAAa,EAId;IACC,IAAIC,YAAuB;QACzBC,QAAQ,EAAE;QACVC,MAAM,EAAE;QACRC,QAAQ,EAAE;IACZ;IAEA,KAAK,MAAMC,WAAWC,OAAOC,IAAI,CAACN,WAA0C;QAC1EA,SAAS,CAACI,QAAQ,GAAGG,WACnBC,IAAAA,6BAAY,EAAC;YACXlB;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAY,UAAUL,YAAY;YACtBM,cAAcN,YAAY;YAC1BO,yBAAyBP,YAAY,YAAYA,YAAY;YAC7DQ,cAAcR,YAAY;YAC1BN;YACAC;QACF;IAEJ;IAEA,OAAOC;AACT;AAuKA,SAASO,WAAW3G,GAA2B;IAC7C,OAAOyG,OAAOQ,OAAO,CAACjH,KACnBkB,MAAM,CAAC,CAAC,CAACgG,GAAGC,MAAM,GAAKA,SAAS,MAChC3C,GAAG,CAAC,CAAC,CAAC4C,MAAMD,MAAM,GAAM,CAAA;YACvBC;YACAD;QACF,CAAA;AACJ;AAEA,mCAAmC;AACnC,SAASE,aAAaC,OAAY,EAAEC,KAAc;IAKhD,MAAMC,SAAS,IAAK,MAAMC,eAAeC;IAAO;IAEhD;;GAEC,GACD,SAASC,UACPC,KAAY,EACZC,cAAoC;QAEpC,MAAM,IAAIH,MAAM,CAAC,WAAW,EAAEG,eAAeD,OAAO,CAAC;IACvD;IAEA,eAAeE,eAAkBC,EAAoB;QACnD,IAAI;YACF,OAAO,MAAMA;QACf,EAAE,OAAOC,aAAkB;YACzB,MAAM,IAAIN,MAAMM,YAAYC,OAAO,EAAE;gBAAEC,OAAOF;YAAY;QAC5D;IACF;IAEA;;;;;GAKC,GACD,SAASG,UACPC,SAAkB,EAClBC,cAAiC;QAKjC,mEAAmE;QACnE,wCAAwC;QACxC,IAAIC,SAAuB,EAAE;QAC7B,sEAAsE;QACtE,qDAAqD;QACrD,IAAIC;QAMJ,IAAIC,WAAW;QAEf,0EAA0E;QAC1E,2EAA2E;QAC3E,2BAA2B;QAC3B,MAAMC,aAAa,CAACC,KAAwBvB;YAC1C,IAAIoB,SAAS;gBACX,IAAI,EAAEzF,OAAO,EAAE6F,MAAM,EAAE,GAAGJ;gBAC1BA,UAAUvG;gBACV,IAAI0G,KAAKC,OAAOD;qBACX5F,QAAQqE;YACf,OAAO;gBACL,MAAMyB,OAAO;oBAAEF;oBAAKvB;gBAAM;gBAC1B,IAAIiB,WAAWE,OAAOO,IAAI,CAACD;qBACtBN,MAAM,CAAC,EAAE,GAAGM;YACnB;QACF;QAEA,MAAME,WAAW,AAAC;YAChB,MAAMC,OAAO,MAAMjB,eAAe,IAAMO,eAAeI;YACvD,IAAI;gBACF,MAAO,CAACD,SAAU;oBAChB,IAAIF,OAAOU,MAAM,GAAG,GAAG;wBACrB,MAAMJ,OAAON,OAAOW,KAAK;wBACzB,IAAIL,KAAKF,GAAG,EAAE,MAAME,KAAKF,GAAG;wBAC5B,MAAME,KAAKzB,KAAK;oBAClB,OAAO;wBACL,wCAAwC;wBACxC,MAAM,IAAItE,QAAW,CAACC,SAAS6F;4BAC7BJ,UAAU;gCAAEzF;gCAAS6F;4BAAO;wBAC9B;oBACF;gBACF;YACF,EAAE,OAAOO,GAAG;gBACV,IAAIA,MAAM1B,QAAQ;gBAClB,MAAM0B;YACR,SAAU;gBACR5B,QAAQ6B,eAAe,CAACJ;YAC1B;QACF;QACAD,SAASM,MAAM,GAAG;YAChBZ,WAAW;YACX,IAAID,SAASA,QAAQI,MAAM,CAACnB;YAC5B,OAAO;gBAAEL,OAAOnF;gBAAWqH,MAAM;YAAK;QACxC;QACA,OAAOP;IACT;IAEA,eAAeQ,sBACbC,OAAgC;QAEhC,OAAO;YACL,GAAGA,OAAO;YACVC,YACED,QAAQC,UAAU,IAAK,MAAMC,oBAAoBF,QAAQC,UAAU;YACrEE,UAAUH,QAAQG,QAAQ,IAAIC,KAAKC,SAAS,CAACL,QAAQG,QAAQ;YAC7D1J,KAAKuJ,QAAQvJ,GAAG,IAAI2G,WAAW4C,QAAQvJ,GAAG;YAC1CoG,WAAWmD,QAAQnD,SAAS;QAC9B;IACF;IAEA,MAAMyD;QAGJC,YAAYC,aAAwC,CAAE;YACpD,IAAI,CAACC,cAAc,GAAGD;QACxB;QAEA,MAAME,OAAOV,OAAuB,EAAE;YACpC,MAAMzB,eAAe,UACnBR,QAAQ4C,aAAa,CACnB,IAAI,CAACF,cAAc,EACnB,MAAMV,sBAAsBC;QAGlC;QAEAY,uBAAuB;YA2CrB,MAAMC,eAAejC,UACnB,OACA,OAAOkC,WACL/C,QAAQgD,2BAA2B,CAAC,IAAI,CAACN,cAAc,EAAEK;YAE7D,OAAO,AAAC;gBACN,WAAW,MAAME,eAAeH,aAAc;oBAC5C,MAAMI,SAAS,IAAIC;oBACnB,KAAK,MAAM,EAAEC,QAAQ,EAAE,GAAGC,aAAa,IAAIJ,YAAYC,MAAM,CAAE;wBAC7D,IAAII;wBACJ,MAAMC,YAAYF,YAAYG,IAAI;wBAClC,OAAQD;4BACN,KAAK;gCACHD,QAAQ;oCACNE,MAAM;oCACNC,cAAc,IAAIC,aAAaL,YAAYI,YAAY;oCACvDE,cAAc,IAAID,aAAaL,YAAYM,YAAY;gCACzD;gCACA;4BACF,KAAK;gCACHL,QAAQ;oCACNE,MAAM;oCACNI,UAAU,IAAIF,aAAaL,YAAYO,QAAQ;gCACjD;gCACA;4BACF,KAAK;gCACHN,QAAQ;oCACNE,MAAM;oCACNC,cAAc,IAAIC,aAAaL,YAAYI,YAAY;oCACvDI,aAAa,IAAIH,aAAaL,YAAYQ,WAAW;gCACvD;gCACA;4BACF,KAAK;gCACHP,QAAQ;oCACNE,MAAM;oCACNI,UAAU,IAAIF,aAAaL,YAAYO,QAAQ;gCACjD;gCACA;4BACF,KAAK;gCACHN,QAAQ;oCACNE,MAAM;gCACR;gCACA;4BACF;gCACE,MAAMM,mBAA0BP;gCAChClD,UACEgD,aACA,IAAM,CAAC,oBAAoB,EAAES,iBAAiB,CAAC;wBAErD;wBACAZ,OAAOa,GAAG,CAACX,UAAUE;oBACvB;oBACA,MAAMU,6BAA6B,CAACC,aAAgC,CAAA;4BAClEL,UAAU,IAAIF,aAAaO,WAAWL,QAAQ;4BAC9CM,SAASD,WAAWC,OAAO;4BAC3BC,SAASF,WAAWE,OAAO;wBAC7B,CAAA;oBACA,MAAMF,aAAahB,YAAYgB,UAAU,GACrCD,2BAA2Bf,YAAYgB,UAAU,IACjDvJ;oBACJ,MAAM;wBACJwI;wBACAe;wBACAG,uBAAuB,IAAIV,aACzBT,YAAYmB,qBAAqB;wBAEnCC,kBAAkB,IAAIX,aAAaT,YAAYoB,gBAAgB;wBAC/DC,oBAAoB,IAAIZ,aACtBT,YAAYqB,kBAAkB;wBAEhCC,QAAQtB,YAAYsB,MAAM;wBAC1BC,aAAavB,YAAYuB,WAAW;oBACtC;gBACF;YACF;QACF;QAEAC,UAAUC,UAAkB,EAAE;YAC5B,MAAM5B,eAAejC,UACnB,MACA,OAAOkC,WACL/C,QAAQ2E,gBAAgB,CAAC,IAAI,CAACjC,cAAc,EAAEgC,YAAY3B;YAE9D,OAAOD;QACT;QAEA8B,0BAA0B;YACxB,MAAM9B,eAAejC,UACnB,OACA,OAAOkC,WACL/C,QAAQ6E,8BAA8B,CAAC,IAAI,CAACnC,cAAc,EAAEK;YAEhE,OAAOD;QACT;QAEAgC,YACEC,UAA+B,EACM;YACrC,OAAO/E,QAAQgF,kBAAkB,CAAC,IAAI,CAACtC,cAAc,EAAEqC;QACzD;QAEAE,kBAAkBC,QAAgB,EAA0B;YAC1D,OAAOlF,QAAQmF,wBAAwB,CAAC,IAAI,CAACzC,cAAc,EAAEwC;QAC/D;QAEAE,sBAAsB;YACpB,MAAMtC,eAAejC,UACnB,MACA,OAAOkC,WACL/C,QAAQqF,0BAA0B,CAAC,IAAI,CAAC3C,cAAc,EAAEK;YAE5D,OAAOD;QACT;IACF;IAEA,MAAMY;QAGJlB,YAAY8C,cAA0C,CAAE;YACtD,IAAI,CAACC,eAAe,GAAGD;QACzB;QAEA,MAAME,cAAyD;YAC7D,OAAO,MAAMhF,eAAe,IAC1BR,QAAQyF,mBAAmB,CAAC,IAAI,CAACF,eAAe;QAEpD;QAEA,MAAMG,gBAAqE;YACzE,MAAMC,qBAAqB9E,UACzB,OACA,OAAOkC,WACL/C,QAAQ4F,8BAA8B,CACpC,MAAM,IAAI,CAACL,eAAe,EAC1BxC;YAGN,MAAM4C,mBAAmBE,IAAI;YAC7B,OAAOF;QACT;QAEA,MAAMG,cACJC,aAAsB,EAC+B;YACrD,MAAMC,qBAAqBnF,UACzB,OACA,OAAOkC,WACL/C,QAAQiG,8BAA8B,CACpC,MAAM,IAAI,CAACV,eAAe,EAC1BQ,eACAhD;YAGN,MAAMiD,mBAAmBH,IAAI;YAC7B,OAAOG;QACT;IACF;IAEA,eAAe7D,oBACbD,UAA8B;YAW1BA,gCAAAA;QATJ,IAAIgE,yBAAyBhE;QAE7BgE,uBAAuBC,eAAe,GACpC,OAAMjE,WAAWiE,eAAe,oBAA1BjE,WAAWiE,eAAe,MAA1BjE;QAER,iFAAiF;QACjFgE,uBAAuBE,aAAa,GAAG,CAAC;QACxCF,uBAAuBG,OAAO,GAAGnE,WAAWmE,OAAO,IAAI,CAAC;QAExD,KAAInE,2BAAAA,WAAWoE,YAAY,sBAAvBpE,iCAAAA,yBAAyBqE,KAAK,qBAA9BrE,+BAAgCsE,KAAK,EAAE;gBACJtE;YAArCuE,sCAAqCvE,kCAAAA,WAAWoE,YAAY,CAACC,KAAK,qBAA7BrE,gCAA+BsE,KAAK;QAC3E;QAEAN,uBAAuBQ,iBAAiB,GACtCR,uBAAuBQ,iBAAiB,GACpCvH,OAAOwH,WAAW,CAChBxH,OAAOQ,OAAO,CAAMuG,uBAAuBQ,iBAAiB,EAAExJ,GAAG,CAC/D,CAAC,CAAC0J,KAAKrI,OAAO,GAAK;gBACjBqI;gBACA;oBACE,GAAGrI,MAAM;oBACT1G,WACE,OAAO0G,OAAO1G,SAAS,KAAK,WACxB0G,OAAO1G,SAAS,GAChBsH,OAAOQ,OAAO,CAACpB,OAAO1G,SAAS,EAAEqF,GAAG,CAAC,CAAC,CAAC2J,KAAKhH,MAAM,GAAK;4BACrDgH;4BACAhH;yBACD;gBACT;aACD,KAGLnF;QAEN,OAAO2H,KAAKC,SAAS,CAAC4D,wBAAwB,MAAM;IACtD;IAEA,SAASO,qCACPK,cAAyC;QAEzC,KAAK,MAAM,CAACC,MAAMC,KAAK,IAAI7H,OAAOQ,OAAO,CAACmH,gBAAiB;YACzD,MAAMG,cAAc9K,MAAMC,OAAO,CAAC4K,QAAQA,OAAOA,KAAKE,OAAO;YAC7D,KAAK,MAAMC,cAAcF,YAAa;gBACpC,IACE,OAAOE,eAAe,YACtB,CAACC,IAAAA,uBAAiB,EAACD,YAAY9E,KAAKpK,KAAK,CAACoK,KAAKC,SAAS,CAAC6E,eACzD;oBACA,MAAM,IAAI/G,MACR,CAAC,OAAO,EAAE+G,WAAWE,MAAM,CAAC,YAAY,EAAEN,KAAK,yGAAyG,CAAC;gBAE7J;YACF;QACF;IACF;IAEA,eAAeO,cACbrF,OAAuB,EACvBsF,kBAAsC;QAEtC,OAAO,IAAIhF,YACT,MAAMvC,QAAQwH,UAAU,CACtB,MAAMxF,sBAAsBC,UAC5BsF,sBAAsB,CAAC;IAG7B;IAEA,OAAOD;AACT;AAEA,eAAehK,SAASmK,aAAa,EAAE;IACrC,IAAI7M,cAAc;QAChB,OAAOA;IACT;IAEA,IAAIoB,WAAW,EAAE;IACjB,KAAK,IAAI0L,OAAO;QAAC;QAAyB;KAAqB,CAAE;QAC/D,IAAI;YACF,IAAIC,UAAUD;YAEd,IAAID,YAAY;gBACd,yDAAyD;gBACzDE,UAAU9K,aAAI,CAACC,IAAI,CAAC2K,YAAYC,KAAK;YACvC;YACA,IAAItK,WAAW,MAAM,MAAM,CAACuK;YAC5B,IAAID,QAAQ,sBAAsB;gBAChCtK,WAAW,MAAMA,SAASwK,OAAO;YACnC;YACA5O,QAAQ;YAER,mEAAmE;YACnE,yCAAyC;YACzC4B,eAAe;gBACbhD,QAAQ;gBACRC,WAAUgQ,GAAW,EAAE5F,OAAY;oBACjC,oHAAoH;oBACpH,OAAO7E,CAAAA,4BAAAA,SAAUvF,SAAS,IACtBuF,SAASvF,SAAS,CAACgQ,IAAIC,QAAQ,IAAI7F,WACnC1G,QAAQC,OAAO,CAAC4B,SAAStF,aAAa,CAAC+P,IAAIC,QAAQ,IAAI7F;gBAC7D;gBACAnK,eAAc+P,GAAW,EAAE5F,OAAY;oBACrC,OAAO7E,SAAStF,aAAa,CAAC+P,IAAIC,QAAQ,IAAI7F;gBAChD;gBACAlK,QAAO8P,GAAW,EAAE5F,OAAY;oBAC9B,OAAO7E,CAAAA,4BAAAA,SAAUrF,MAAM,IACnBqF,SAASrF,MAAM,CAAC8P,IAAIC,QAAQ,IAAI7F,WAChC1G,QAAQC,OAAO,CAAC4B,SAASpF,UAAU,CAAC6P,IAAIC,QAAQ,IAAI7F;gBAC1D;gBACAjK,YAAW6P,GAAW,EAAE5F,OAAY;oBAClC,OAAO7E,SAASpF,UAAU,CAAC6P,IAAIC,QAAQ,IAAI7F;gBAC7C;gBACAhK,OAAM4P,GAAW,EAAE5F,OAAY;oBAC7B,OAAO7E,CAAAA,4BAAAA,SAAUnF,KAAK,IAClBmF,SAASnF,KAAK,CAAC4P,IAAIC,QAAQ,IAAI7F,WAC/B1G,QAAQC,OAAO,CAAC4B,SAAS2K,SAAS,CAACF,IAAIC,QAAQ,IAAI7F;gBACzD;gBACA8F,WAAUF,GAAW,EAAE5F,OAAY;oBACjC,MAAM+F,SAAS5K,SAAS2K,SAAS,CAACF,IAAIC,QAAQ,IAAI7F;oBAClD,OAAO+F;gBACT;gBACAC;oBACE,OAAOvN;gBACT;gBACA6L,OAAO;oBACL2B,YAAY;wBACV9O,KAAI2C,KAAK,CAAC;oBACZ;oBACAkH,aAAa;wBACXkF,QAAQ,CACNC,YACAC,SACAC,gBACAC,gBACAC;4BAEA,OAAOpL,SAASqL,iBAAiB,CAC/BL,YACAC,SACAC,gBACAC,gBACAC;wBAEJ;wBACAE,KAAK,CACHN,YACAC,SACAC,gBACAC;4BAEA,OAAOnL,SAASuL,cAAc,CAC5BP,YACAC,SACAC,gBACAC;wBAEJ;oBACF;gBACF;gBACAK,KAAK;oBACHC,SAAS,CAAChB,KAAa5F,UACrB7E,SAAS0L,UAAU,CAACjB,KAAKkB,cAAc9G;oBACzC+G,aAAa,CAACnB,KAAa5F,UACzB7E,SAAS6L,cAAc,CAACpB,KAAKkB,cAAc9G;gBAC/C;YACF;YACA,OAAOrH;QACT,EAAE,OAAOgH,GAAQ;YACf,8DAA8D;YAC9D,IAAI6F,YAAY;gBACd,IAAI7F,CAAAA,qBAAAA,EAAGsH,IAAI,MAAK,wBAAwB;oBACtClN,SAASuF,IAAI,CAAC,CAAC,kBAAkB,EAAEmG,IAAI,0BAA0B,CAAC;gBACpE,OAAO;oBACL1L,SAASuF,IAAI,CACX,CAAC,kBAAkB,EAAEmG,IAAI,yBAAyB,EAAE9F,EAAEjB,OAAO,IAAIiB,EAAE,CAAC;gBAExE;YACF;QACF;IACF;IAEA,MAAM5F;AACR;AAEA,SAASC,WAAWwL,UAAmB;IACrC,IAAI9M,gBAAgB;QAClB,OAAOA;IACT;IAEA,MAAMwO,iBAAiB,CAAC,CAAC9O,uCACrB2C,QAAQ3C,wCACR;IACJ,IAAI+C;IACJ,IAAIpB,WAAkB,EAAE;IAExB,KAAK,MAAMnC,UAAUG,QAAS;QAC5B,IAAI;YACFoD,WAAWJ,QAAQ,CAAC,0BAA0B,EAAEnD,OAAOsD,eAAe,CAAC,KAAK,CAAC;YAC7EnE,QAAQ;YACR;QACF,EAAE,OAAO4I,GAAG,CAAC;IACf;IAEA,IAAI,CAACxE,UAAU;QACb,KAAK,MAAMvD,UAAUG,QAAS;YAC5B,IAAI0N,MAAMD,aACN5K,aAAI,CAACC,IAAI,CACP2K,YACA,CAAC,UAAU,EAAE5N,OAAOsD,eAAe,CAAC,CAAC,EACrC,CAAC,SAAS,EAAEtD,OAAOsD,eAAe,CAAC,KAAK,CAAC,IAE3C,CAAC,UAAU,EAAEtD,OAAOsD,eAAe,CAAC,CAAC;YACzC,IAAI;gBACFC,WAAWJ,QAAQ0K;gBACnB,IAAI,CAACD,YAAY;oBACfnN,qBAAqB0C,QAAQ,CAAC,EAAE0K,IAAI,aAAa,CAAC;gBACpD;gBACA;YACF,EAAE,OAAO9F,GAAQ;gBACf,IAAIA,CAAAA,qBAAAA,EAAGsH,IAAI,MAAK,oBAAoB;oBAClClN,SAASuF,IAAI,CAAC,CAAC,kBAAkB,EAAEmG,IAAI,0BAA0B,CAAC;gBACpE,OAAO;oBACL1L,SAASuF,IAAI,CACX,CAAC,kBAAkB,EAAEmG,IAAI,yBAAyB,EAAE9F,EAAEjB,OAAO,IAAIiB,EAAE,CAAC;gBAExE;gBACAnH,kCAAkCmH,CAAAA,qBAAAA,EAAGsH,IAAI,KAAI;YAC/C;QACF;IACF;IAEA,IAAI9L,UAAU;QACZ,+EAA+E;QAC/E,kGAAkG;QAClG,gFAAgF;QAChF,IAAI,CAACnC,4BAA4B;QAC/B,6FAA6F;QAC7F;;;;OAIC,GACH;QAEAN,iBAAiB;YACf/C,QAAQ;YACRC,WAAUgQ,GAAW,EAAE5F,OAAY;oBAO7BA;gBANJ,MAAMmH,WACJ,OAAOvB,QAAQnN,aACf,OAAOmN,QAAQ,YACf,CAACwB,OAAOC,QAAQ,CAACzB;gBACnB5F,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAASsH,GAAG,qBAAZtH,aAAcuH,MAAM,EAAE;oBACxBvH,QAAQsH,GAAG,CAACC,MAAM,CAACC,MAAM,GAAGxH,QAAQsH,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAOrM,SAASvF,SAAS,CACvBuR,WAAW/G,KAAKC,SAAS,CAACuF,OAAOA,KACjCuB,UACAM,SAASzH;YAEb;YAEAnK,eAAc+P,GAAW,EAAE5F,OAAY;oBAajCA;gBAZJ,IAAI,OAAO4F,QAAQnN,WAAW;oBAC5B,MAAM,IAAI0F,MACR;gBAEJ,OAAO,IAAIiJ,OAAOC,QAAQ,CAACzB,MAAM;oBAC/B,MAAM,IAAIzH,MACR;gBAEJ;gBACA,MAAMgJ,WAAW,OAAOvB,QAAQ;gBAChC5F,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAASsH,GAAG,qBAAZtH,aAAcuH,MAAM,EAAE;oBACxBvH,QAAQsH,GAAG,CAACC,MAAM,CAACC,MAAM,GAAGxH,QAAQsH,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAOrM,SAAStF,aAAa,CAC3BsR,WAAW/G,KAAKC,SAAS,CAACuF,OAAOA,KACjCuB,UACAM,SAASzH;YAEb;YAEAlK,QAAO8P,GAAW,EAAE5F,OAAY;gBAC9B,OAAO7E,SAASrF,MAAM,CAAC2R,SAAS7B,MAAM6B,SAASzH,WAAW,CAAC;YAC7D;YAEAjK,YAAW6P,GAAW,EAAE5F,OAAY;gBAClC,OAAO7E,SAASpF,UAAU,CAAC0R,SAAS7B,MAAM6B,SAASzH,WAAW,CAAC;YACjE;YAEAhK,OAAM4P,GAAW,EAAE5F,OAAY;gBAC7B,OAAO7E,SAASnF,KAAK,CAAC4P,KAAK6B,SAASzH,WAAW,CAAC;YAClD;YAEAgG,iBAAiB7K,SAAS6K,eAAe;YACzC9P,2BAA2BiF,SAASjF,yBAAyB;YAC7DG,yBAAyB8E,SAAS9E,uBAAuB;YACzDF,kBAAkBgF,SAAShF,gBAAgB;YAC3CC,sBAAsB+E,SAAS/E,oBAAoB;YACnDE,uBAAuB6E,SAAS7E,qBAAqB;YACrDgO,OAAO;gBACLoD,WAAW,CAAC1H;oBACV7J;oBACA,MAAMwR,MAAM,AAACT,CAAAA,kBAAkB/L,QAAO,EAAGuM,SAAS,CAAC1H;oBAEnD,OAAO2H;gBACT;gBACA1B,YAAY,CAACjG,UAAU,CAAC,CAAC,EAAEmG;oBACzBhQ;oBACA,MAAMwR,MAAM,AAACT,CAAAA,kBAAkB/L,QAAO,EAAGyM,eAAe,CACtDH,SAAS;wBAAEI,OAAO;wBAAM,GAAG7H,OAAO;oBAAC,IACnCmG;oBAEF,OAAOwB;gBACT;gBACAG,kBAAkB,CAACC,cACjB5M,SAAS2M,gBAAgB,CAACC;gBAC5B/G,aAAa;oBACXkF,QAAQ,CACNC,YACAC,SACAC,gBACAC,gBACA9H;wBAEA,OAAO,AAAC0I,CAAAA,kBAAkB/L,QAAO,EAAGqL,iBAAiB,CACnDL,YACAC,SACAC,gBACAC,gBACA9H;oBAEJ;oBACAiI,KAAK,CACHN,YACAC,SACAC,gBACAC;wBAEA,OAAO,AAACY,CAAAA,kBAAkB/L,QAAO,EAAGuL,cAAc,CAChDP,YACAC,SACAC,gBACAC;oBAEJ;gBACF;gBACAjB,eAAevH,aAAaoJ,kBAAkB/L,UAAU;YAC1D;YACAwL,KAAK;gBACHC,SAAS,CAAChB,KAAa5F,UACrB7E,SAAS0L,UAAU,CAACjB,KAAK6B,SAASX,cAAc9G;gBAClD+G,aAAa,CAACnB,KAAa5F,UACzB7E,SAAS6L,cAAc,CAACpB,KAAK6B,SAASX,cAAc9G;YACxD;QACF;QACA,OAAOtH;IACT;IAEA,MAAMqB;AACR;AAEA,2DAA2D;AAC3D,0CAA0C;AAC1C,SAAS+M,cAAc9G,UAAe,CAAC,CAAC;IACtC,MAAM2H,MAAM;QACV,GAAG3H,OAAO;QACVgI,aAAahI,QAAQgI,WAAW,IAAI;QACpCC,KAAKjI,QAAQiI,GAAG,IAAI;QACpBjS,OAAOgK,QAAQhK,KAAK,IAAI;YACtBkS,6BAA6B;YAC7BC,sBAAsB;QACxB;IACF;IAEA,OAAOR;AACT;AAEA,SAASF,SAASW,CAAM;IACtB,OAAOhB,OAAOiB,IAAI,CAACjI,KAAKC,SAAS,CAAC+H;AACpC;AAEO,eAAezS;IACpB,IAAIwF,WAAW,MAAM1F;IACrB,OAAO0F,SAASxF,MAAM;AACxB;AAEO,eAAeC,UAAUgQ,GAAW,EAAE5F,OAAa;IACxD,IAAI7E,WAAW,MAAM1F;IACrB,OAAO0F,SAASvF,SAAS,CAACgQ,KAAK5F;AACjC;AAEO,SAASnK,cAAc+P,GAAW,EAAE5F,OAAa;IACtD,IAAI7E,WAAWW;IACf,OAAOX,SAAStF,aAAa,CAAC+P,KAAK5F;AACrC;AAEO,eAAelK,OAAO8P,GAAW,EAAE5F,OAAY;IACpD,IAAI7E,WAAW,MAAM1F;IACrB,OAAO0F,SAASrF,MAAM,CAAC8P,KAAK5F;AAC9B;AAEO,SAASjK,WAAW6P,GAAW,EAAE5F,OAAY;IAClD,IAAI7E,WAAWW;IACf,OAAOX,SAASpF,UAAU,CAAC6P,KAAK5F;AAClC;AAEO,eAAehK,MAAM4P,GAAW,EAAE5F,OAAY;IACnD,IAAI7E,WAAW,MAAM1F;IACrB,IAAI6S,gBAAgBC,IAAAA,yBAAgB,EAACvI;IACrC,OAAO7E,SACJnF,KAAK,CAAC4P,KAAK0C,eACXtM,IAAI,CAAC,CAAC+J,SAAgB3F,KAAKpK,KAAK,CAAC+P;AACtC;AAEO,SAAS9P;QASJkF;IARV,IAAIA;IACJ,IAAI;QACFA,WAAWnB;IACb,EAAE,OAAO2F,GAAG;IACV,sEAAsE;IACxE;IAEA,OAAO;QACL6I,MAAM,EAAErN,6BAAAA,4BAAAA,SAAU6K,eAAe,qBAAzB7K,+BAAAA;IACV;AACF;AAMO,MAAMjF,4BAA4B,CAACuS;IACxC,IAAI,CAAC3P,oBAAoB;QACvB,6CAA6C;QAC7C,IAAIqC,WAAWnB;QACflB,qBAAqBqC,SAASjF,yBAAyB,CAACuS;IAC1D;AACF;AAQO,MAAMtS,mBAAmB;IAC9B,IAAI;QACF,IAAI,CAAC4C,2BAA2B;YAC9B,IAAIoC,WAAWnB;YACfjB,4BAA4BoC,SAAShF,gBAAgB;QACvD;IACF,EAAE,OAAOwH,GAAG;IACV,sEAAsE;IACxE;AACF;AAQO,MAAMvH,uBAAuB,AAAC,CAAA;IACnC,IAAIsS,UAAU;IACd,OAAO;QACL,IAAI,CAACA,SAAS;YACZA,UAAU;YACV,IAAI;gBACF,IAAIvN,WAAWnB;gBACf,IAAIjB,2BAA2B;oBAC7BoC,SAAS/E,oBAAoB,CAAC2C;gBAChC;YACF,EAAE,OAAO4G,GAAG;YACV,sEAAsE;YACxE;QACF;IACF;AACF,CAAA;AAWO,MAAMtJ,0BAA0B,AAAC,CAAA;IACtC,IAAIqS,UAAU;IACd,OAAO;QACL,IAAI,CAACA,SAAS;YACZA,UAAU;YACV,IAAI;gBACF,IAAIvN,WAAWnB;gBACf,IAAIlB,oBAAoB;oBACtBqC,SAAS9E,uBAAuB,CAACyC;gBACnC;YACF,EAAE,OAAO6G,GAAG;YACV,sEAAsE;YACxE;QACF;IACF;AACF,CAAA;AAEO,MAAMrJ,wBAAwB,AAAC,CAAA;IACpC,IAAIoS,UAAU;IACd,OAAO;QACL,IAAI,CAACA,SAAS;YACZA,UAAU;YACV,IAAI;gBACF,IAAIvN,WAAWnB;gBACf,IAAIhB,4BAA4B;oBAC9BmC,SAAS7E,qBAAqB,CAAC0C;gBACjC;YACF,EAAE,OAAO2G,GAAG;YACV,sEAAsE;YACxE;QACF;IACF;AACF,CAAA"}