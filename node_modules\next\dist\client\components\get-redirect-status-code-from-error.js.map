{"version": 3, "sources": ["../../../src/client/components/get-redirect-status-code-from-error.ts"], "names": ["getRedirectStatusCodeFromError", "error", "isRedirectError", "Error", "digest", "split"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;0BAFoC;AAE7C,SAASA,+BACdC,KAAuB;IAEvB,IAAI,CAACC,IAAAA,yBAAe,EAACD,QAAQ;QAC3B,MAAM,IAAIE,MAAM;IAClB;IAEA,OAAOF,MAAMG,MAAM,CAACC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,SAAS,MAAM;AAC1D"}