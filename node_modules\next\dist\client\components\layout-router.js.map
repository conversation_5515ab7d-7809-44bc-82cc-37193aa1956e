{"version": 3, "sources": ["../../../src/client/components/layout-router.tsx"], "names": ["OuterLayoutRouter", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "matchSegment", "hasOwnProperty", "subTree", "undefined", "slice", "findDOMNode", "instance", "window", "process", "env", "NODE_ENV", "originalConsoleError", "console", "error", "messages", "includes", "ReactDOM", "rectProperties", "shouldSkipElement", "element", "getComputedStyle", "position", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "React", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "nextElement<PERSON><PERSON>ling", "handleSmoothScroll", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "useContext", "GlobalLayoutRouterContext", "Error", "InnerLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "url", "childNodes", "childProp", "tree", "cache<PERSON>ey", "buildId", "changeByServerResponse", "fullTree", "childNode", "get", "current", "status", "CacheStates", "READY", "data", "subTreeData", "parallelRoutes", "Map", "set", "LAZY_INITIALIZED", "refetchTree", "DATA_FETCH", "createRecordFromThenable", "fetchServerResponse", "URL", "location", "origin", "nextUrl", "head", "flightData", "overrideCanonicalUrl", "use", "setTimeout", "startTransition", "createInfinitePromise", "subtree", "LayoutRouterContext", "Provider", "value", "LoadingBoundary", "loading", "loadingStyles", "loadingScripts", "hasLoading", "Suspense", "fallback", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "notFoundStyles", "styles", "childNodesForParallelRouter", "treeSegment", "childPropSegment", "currentChildSegmentValue", "getSegmentValue", "preservedSegments", "map", "preservedSegment", "isChildPropSegment", "preservedSegmentValue", "createRouterCache<PERSON>ey", "TemplateContext", "key", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "NotFoundBoundary", "RedirectBoundary", "isActive"], "mappings": "AAAA;;;;;+BAgfA;;;CAGC,GACD;;;eAAwBA;;;;;iEAxe0C;mEAC7C;+CAMd;qCAC6B;iCACE;+BACR;+BACD;oCACM;kCACF;kCACA;iCACD;sCACK;0CACI;AAEzC;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIC,IAAAA,2BAAY,EAACL,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACM,cAAc,CAACJ,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMI,UAAUT,eACdU,WACAR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBK,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLP,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBU,KAAK,CAAC,IACxBT,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,4FAA4F;AAC5F;;CAEC,GACD,SAASU,YACPC,QAAoD;IAEpD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAC1C,wDAAwD;IACxD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,uBAAuBC,QAAQC,KAAK;QAC1C,IAAI;YACFD,QAAQC,KAAK,GAAG;iDAAIC;oBAAAA;;gBAClB,4DAA4D;gBAC5D,IAAI,CAACA,QAAQ,CAAC,EAAE,CAACC,QAAQ,CAAC,6CAA6C;oBACrEJ,wBAAwBG;gBAC1B;YACF;YACA,OAAOE,iBAAQ,CAACX,WAAW,CAACC;QAC9B,SAAU;YACRM,QAAQC,KAAK,GAAGF;QAClB;IACF;IACA,OAAOK,iBAAQ,CAACX,WAAW,CAACC;AAC9B;AAEA,MAAMW,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACJ,QAAQ,CAACK,iBAAiBD,SAASE,QAAQ,GAAG;QACpE,IAAIb,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CE,QAAQU,IAAI,CACV,4FACAH;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMI,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOP,eAAeQ,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBR,OAAoB,EAAES,cAAsB;IAC1E,MAAML,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,yBAAxBC,2BACA,8FAA8F;IAC9FA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE;AAE/C;AAMA,MAAMK,mCAAmCC,cAAK,CAACC,SAAS;IAoGtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;;aAhHAN,wBAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAACjD,MAAM,KAAK,KAC1C,CAAC4C,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYtB,KAAK,CAAC,CAAC7B,SAASuD,QAC1BnD,IAAAA,2BAAY,EAACJ,SAASsD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMrB,eAAeY,kBAAkBZ,YAAY;gBAEnD,IAAIA,cAAc;oBAChBqB,UAAUtB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACqB,SAAS;oBACZA,UAAU/C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE+C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMpC,kBAAkBkC,SAAU;oBACtE,uGAAuG;oBACvG,IAAIA,QAAQG,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAH,UAAUA,QAAQG,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7EZ,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBZ,YAAY,GAAG;gBACjCY,kBAAkBK,YAAY,GAAG,EAAE;gBAEnCQ,IAAAA,sCAAkB,EAChB;oBACE,uEAAuE;oBACvE,IAAIzB,cAAc;wBACdqB,QAAwBK,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc1B,SAAS2B,eAAe;oBAC5C,MAAM/B,iBAAiB8B,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAIjC,uBAAuByB,SAAwBxB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7H8B,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAAClC,uBAAuByB,SAAwBxB,iBAAiB;wBAEjEwB,QAAwBK,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBpB,kBAAkBoB,cAAc;gBAClD;gBAGF,wEAAwE;gBACxEpB,kBAAkBoB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3BX,QAAQY,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BlB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMoB,UAAUC,IAAAA,iBAAU,EAACC,wDAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,IAAIG,MAAM;IAClB;IAEA,qBACE,6BAACjC;QACCW,aAAaA;QACbJ,mBAAmBuB,QAAQvB,iBAAiB;OAE3CG;AAGP;AAEA;;CAEC,GACD,SAASwB,kBAAkB,KAmB1B;IAnB0B,IAAA,EACzBC,iBAAiB,EACjBC,GAAG,EACHC,UAAU,EACVC,SAAS,EACT3B,WAAW,EACX4B,IAAI,EACJ,oDAAoD;IACpD,YAAY;IACZC,QAAQ,EAUT,GAnB0B;IAoBzB,MAAMV,UAAUC,IAAAA,iBAAU,EAACC,wDAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,IAAIG,MAAM;IAClB;IAEA,MAAM,EAAEQ,OAAO,EAAEC,sBAAsB,EAAEH,MAAMI,QAAQ,EAAE,GAAGb;IAE5D,yDAAyD;IACzD,IAAIc,YAAYP,WAAWQ,GAAG,CAACL;IAE/B,mEAAmE;IACnE,IACEF,aACA,0DAA0D;IAC1DA,UAAUQ,OAAO,KAAK,MACtB;QACA,IAAI,CAACF,WAAW;YACd,8CAA8C;YAC9C,yJAAyJ;YACzJA,YAAY;gBACVG,QAAQC,0CAAW,CAACC,KAAK;gBACzBC,MAAM;gBACNC,aAAab,UAAUQ,OAAO;gBAC9BM,gBAAgB,IAAIC;YACtB;YAEAhB,WAAWiB,GAAG,CAACd,UAAUI;QAC3B,OAAO;YACL,IAAIA,UAAUG,MAAM,KAAKC,0CAAW,CAACO,gBAAgB,EAAE;gBACrD,6CAA6C;gBAC7CX,UAAUG,MAAM,GAAGC,0CAAW,CAACC,KAAK;gBACpC,mBAAmB;gBACnBL,UAAUO,WAAW,GAAGb,UAAUQ,OAAO;YAC3C;QACF;IACF;IAEA,oGAAoG;IACpG,IAAI,CAACF,aAAaA,UAAUG,MAAM,KAAKC,0CAAW,CAACO,gBAAgB,EAAE;QACnE;;KAEC,GACD,sBAAsB;QACtB,MAAMC,cAAcnG,eAAe;YAAC;eAAOsD;SAAY,EAAEgC;QAEzDC,YAAY;YACVG,QAAQC,0CAAW,CAACS,UAAU;YAC9BP,MAAMQ,IAAAA,kDAAwB,EAC5BC,IAAAA,wCAAmB,EACjB,IAAIC,IAAIxB,KAAKyB,SAASC,MAAM,GAC5BN,aACA1B,QAAQiC,OAAO,EACftB;YAGJU,aAAa;YACba,MACEpB,aAAaA,UAAUG,MAAM,KAAKC,0CAAW,CAACO,gBAAgB,GAC1DX,UAAUoB,IAAI,GACdjG;YACNqF,gBACER,aAAaA,UAAUG,MAAM,KAAKC,0CAAW,CAACO,gBAAgB,GAC1DX,UAAUQ,cAAc,GACxB,IAAIC;QACZ;QAEA;;KAEC,GACDhB,WAAWiB,GAAG,CAACd,UAAUI;IAC3B;IAEA,kGAAkG;IAClG,IAAI,CAACA,WAAW;QACd,MAAM,IAAIX,MAAM;IAClB;IAEA,kGAAkG;IAClG,IAAIW,UAAUO,WAAW,IAAIP,UAAUM,IAAI,EAAE;QAC3C,MAAM,IAAIjB,MAAM;IAClB;IAEA,6FAA6F;IAC7F,IAAIW,UAAUM,IAAI,EAAE;QAClB;;KAEC,GACD,8DAA8D;QAC9D,MAAM,CAACe,YAAYC,qBAAqB,GAAGC,IAAAA,UAAG,EAACvB,UAAUM,IAAI;QAE7D,sEAAsE;QACtEN,UAAUM,IAAI,GAAG;QAEjB,wGAAwG;QACxGkB,WAAW;YACTC,IAAAA,sBAAe,EAAC;gBACd3B,uBAAuBC,UAAUsB,YAAYC;YAC/C;QACF;QACA,yGAAyG;QACzGC,IAAAA,UAAG,EAACG,IAAAA,sCAAqB;IAC3B;IAEA,yIAAyI;IACzI,wFAAwF;IACxF,IAAI,CAAC1B,UAAUO,WAAW,EAAE;QAC1BgB,IAAAA,UAAG,EAACG,IAAAA,sCAAqB;IAC3B;IAEA,MAAMC,UACJ,4EAA4E;kBAC5E,6BAACC,kDAAmB,CAACC,QAAQ;QAC3BC,OAAO;YACLnC,MAAMA,IAAI,CAAC,EAAE,CAACJ,kBAAkB;YAChCE,YAAYO,UAAUQ,cAAc;YACpC,kDAAkD;YAClDhB,KAAKA;QACP;OAECQ,UAAUO,WAAW;IAG1B,iFAAiF;IACjF,OAAOoB;AACT;AAEA;;;CAGC,GACD,SAASI,gBAAgB,KAYxB;IAZwB,IAAA,EACvBjE,QAAQ,EACRkE,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,UAAU,EAOX,GAZwB;IAavB,IAAIA,YAAY;QACd,qBACE,6BAACC,eAAQ;YACPC,wBACE,4DACGJ,eACAC,gBACAF;WAIJlE;IAGP;IAEA,qBAAO,4DAAGA;AACZ;AAMe,SAAStD,kBAAkB,KAkCzC;IAlCyC,IAAA,EACxC+E,iBAAiB,EACjBxB,WAAW,EACX2B,SAAS,EACT7D,KAAK,EACLyG,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfT,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,UAAU,EACVO,QAAQ,EACRC,QAAQ,EACRC,cAAc,EACdC,MAAM,EAkBP,GAlCyC;IAmCxC,MAAM3D,UAAUC,IAAAA,iBAAU,EAACyC,kDAAmB;IAC9C,IAAI,CAAC1C,SAAS;QACZ,MAAM,IAAIG,MAAM;IAClB;IAEA,MAAM,EAAEI,UAAU,EAAEE,IAAI,EAAEH,GAAG,EAAE,GAAGN;IAElC,4CAA4C;IAC5C,IAAI4D,8BAA8BrD,WAAWQ,GAAG,CAACV;IACjD,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACuD,6BAA6B;QAChCA,8BAA8B,IAAIrC;QAClChB,WAAWiB,GAAG,CAACnB,mBAAmBuD;IACpC;IAEA,qCAAqC;IACrC,8IAA8I;IAC9I,MAAMC,cAAcpD,IAAI,CAAC,EAAE,CAACJ,kBAAkB,CAAC,EAAE;IAEjD,MAAMyD,mBAAmBtD,UAAU9E,OAAO;IAE1C,gIAAgI;IAChI,MAAMqI,2BAA2BC,IAAAA,gCAAe,EAACH;IAEjD;;GAEC,GACD,+DAA+D;IAC/D,MAAMI,oBAA+B;QAACJ;KAAY;IAElD,qBACE,4DACGF,QACAM,kBAAkBC,GAAG,CAAC,CAACC;QACtB,MAAMC,qBAAqBtI,IAAAA,2BAAY,EACrCqI,kBACAL;QAEF,MAAMO,wBAAwBL,IAAAA,gCAAe,EAACG;QAC9C,MAAMzD,WAAW4D,IAAAA,0CAAoB,EAACH;QAEtC,OACE;;;;;;;;UAQA,iBACA,6BAACI,8CAAe,CAAC5B,QAAQ;YACvB6B,KAAKF,IAAAA,0CAAoB,EAACH,kBAAkB;YAC5CvB,qBACE,6BAAC7C;gBAAsBlB,aAAaA;6BAClC,6BAAC4F,4BAAa;gBACZC,gBAAgB/H;gBAChByG,aAAaA;gBACbC,cAAcA;6BAEd,6BAACR;gBACCI,YAAYA;gBACZH,SAASA;gBACTC,eAAeA;gBACfC,gBAAgBA;6BAEhB,6BAAC2B,kCAAgB;gBACflB,UAAUA;gBACVC,gBAAgBA;6BAEhB,6BAACkB,kCAAgB,sBACf,6BAACxE;gBACCC,mBAAmBA;gBACnBC,KAAKA;gBACLG,MAAMA;gBACNF,YAAYqD;gBACZpD,WAAW4D,qBAAqB5D,YAAY;gBAC5C3B,aAAaA;gBACb6B,UAAUA;gBACVmE,UACEd,6BAA6BM;;WAU5Cf,gBACAC,iBACAC;IAGP;AAGN"}