{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["getDefineEnv", "getDefineEnvPlugin", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "isTurbopack", "allowedRevalidateHeaderKeys", "clientRouterFilters", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "previewModeId", "__NEXT_DEFINE_ENV", "Object", "keys", "process", "env", "reduce", "prev", "startsWith", "JSON", "stringify", "acc", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "useDeploymentIdServerActions", "deploymentId", "manualClientBasePath", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "output", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "assetPrefix", "undefined", "needsExperimentalReact", "options", "webpack", "DefinePlugin"], "mappings": ";;;;;;;;;;;;;;;IAwCgBA,YAAY;eAAZA;;IA2LAC,kBAAkB;eAAlBA;;;yBAjOQ;wCACe;AAEvC,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AA0BO,SAAST,aAAa,EAC3BU,WAAW,EACXC,2BAA2B,EAC3BC,mBAAmB,EACnBT,MAAM,EACNU,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EAClBC,aAAa,EACU;QAsHNnB,gBAKSA,iBAY0BA;IAtIpD,OAAO;QACL,+CAA+C;QAC/CoB,mBAAmB;QAEnB,GAAGC,OAAOC,IAAI,CAACC,QAAQC,GAAG,EAAEC,MAAM,CAChC,CAACC,MAAiCzB;YAChC,IAAIA,IAAI0B,UAAU,CAAC,iBAAiB;gBAClCD,IAAI,CAAC,CAAC,YAAY,EAAEzB,IAAI,CAAC,CAAC,GAAG2B,KAAKC,SAAS,CAACN,QAAQC,GAAG,CAACvB,IAAI;YAC9D;YACA,OAAOyB;QACT,GACA,CAAC,EACF;QACD,GAAGL,OAAOC,IAAI,CAACtB,OAAOwB,GAAG,EAAEC,MAAM,CAAC,CAACK,KAAK7B;YACtCF,qBAAqBC,QAAQC;YAE7B,OAAO;gBACL,GAAG6B,GAAG;gBACN,CAAC,CAAC,YAAY,EAAE7B,IAAI,CAAC,CAAC,EAAE2B,KAAKC,SAAS,CAAC7B,OAAOwB,GAAG,CAACvB,IAAI;YACxD;QACF,GAAG,CAAC,EAAE;QACN,GAAI,CAACc,eACD,CAAC,IACD;YACEgB,aAAaH,KAAKC,SAAS,CACzB;;;;aAIC,GACDN,QAAQC,GAAG,CAACQ,0BAA0B,IAAI;QAE9C,CAAC;QACL,qBAAqBJ,KAAKC,SAAS,CAACtB;QACpC,yBAAyBqB,KAAKC,SAAS,CAACtB;QACxC,6DAA6D;QAC7D,wBAAwBqB,KAAKC,SAAS,CAACnB,MAAM,gBAAgB;QAC7D,4BAA4BkB,KAAKC,SAAS,CACxCd,eAAe,SAASE,eAAe,WAAW;QAEpD,4BAA4BW,KAAKC,SAAS,CAAC;QAC3C,4CAA4CD,KAAKC,SAAS,CACxD7B,OAAOiC,YAAY,CAACC,4BAA4B;QAElD,kCAAkCN,KAAKC,SAAS,CAC9C7B,OAAOiC,YAAY,CAACE,YAAY,IAAI;QAEtC,6CACEP,KAAKC,SAAS,CAACjB;QACjB,sCAAsCgB,KAAKC,SAAS,CAACV;QACrD,iDAAiDS,KAAKC,SAAS,CAC7DrB;QAEF,0CAA0CoB,KAAKC,SAAS,CACtDX,sBAAsB,EAAE;QAE1B,8CAA8CU,KAAKC,SAAS,CAC1D7B,OAAOiC,YAAY,CAACG,oBAAoB;QAE1C,mDAAmDR,KAAKC,SAAS,CAC/D7B,OAAOiC,YAAY,CAACI,kBAAkB;QAExC,6CAA6CT,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB6B,YAAY;QAEnC,6CAA6CV,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB8B,aAAa;QAEpC,8CAA8CX,KAAKC,SAAS,CAC1D7B,OAAOiC,YAAY,CAACO,qBAAqB;QAE3C,0CAA0CZ,KAAKC,SAAS,CACtD7B,OAAOiC,YAAY,CAACQ,kBAAkB;QAExC,mCAAmCb,KAAKC,SAAS,CAAC7B,OAAO0C,WAAW;QACpE,mBAAmBd,KAAKC,SAAS,CAACf;QAClC,gCAAgCc,KAAKC,SAAS,CAC5CN,QAAQC,GAAG,CAACmB,gBAAgB;QAE9B,2FAA2F;QAC3F,GAAIjC,OAAQI,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+Ba,KAAKC,SAAS,CAAClB;QAChD,IACA,CAAC,CAAC;QACN,qCAAqCiB,KAAKC,SAAS,CAAC7B,OAAO4C,aAAa;QACxE,sCAAsChB,KAAKC,SAAS,CAClD7B,OAAO6C,aAAa,CAACC,aAAa;QAEpC,+CAA+ClB,KAAKC,SAAS,CAC3D7B,OAAO6C,aAAa,CAACE,qBAAqB;QAE5C,kCAAkCnB,KAAKC,SAAS,CAC9C7B,OAAOgD,eAAe,KAAK,OAAO,QAAQhD,OAAOgD,eAAe;QAElE,sCAAsCpB,KAAKC,SAAS,CAClD,6EAA6E;QAC7E7B,OAAOgD,eAAe,KAAK,OAAO,OAAOhD,OAAOgD,eAAe;QAEjE,qCAAqCpB,KAAKC,SAAS,CACjD,CAACnB,OAAOV,OAAOiD,aAAa;QAE9B,mCAAmCrB,KAAKC,SAAS,CAC/C7B,OAAOiC,YAAY,CAACiB,WAAW,IAAI,CAACxC;QAEtC,qCAAqCkB,KAAKC,SAAS,CACjD7B,OAAOiC,YAAY,CAACkB,iBAAiB,IAAI,CAACzC;QAE5C,yCAAyCkB,KAAKC,SAAS,CACrD7B,OAAOiC,YAAY,CAACmB,iBAAiB;QAEvC,iCAAiCxB,KAAKC,SAAS,CAAC;YAC9CwB,aAAarD,OAAOsD,MAAM,CAACD,WAAW;YACtCE,YAAYvD,OAAOsD,MAAM,CAACC,UAAU;YACpCC,MAAMxD,OAAOsD,MAAM,CAACE,IAAI;YACxBC,QAAQzD,OAAOsD,MAAM,CAACG,MAAM;YAC5BC,qBAAqB1D,OAAOsD,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAE3D,2BAAAA,iBAAAA,OAAQsD,MAAM,qBAAdtD,eAAgB2D,WAAW;YACxC,GAAIjD,MACA;gBACE,gEAAgE;gBAChEkD,SAAS5D,OAAOsD,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAE7D,kBAAAA,OAAOsD,MAAM,qBAAbtD,gBAAe6D,cAAc;gBAC7CC,QAAQ9D,OAAO8D,MAAM;YACvB,IACA,CAAC,CAAC;QACR;QACA,sCAAsClC,KAAKC,SAAS,CAAC7B,OAAO+D,QAAQ;QACpE,uCAAuCnC,KAAKC,SAAS,CACnD7B,OAAOiC,YAAY,CAAC+B,cAAc;QAEpC,mCAAmCpC,KAAKC,SAAS,CAAChB;QAClD,oCAAoCe,KAAKC,SAAS,CAAC7B,OAAO8D,MAAM;QAChE,mCAAmClC,KAAKC,SAAS,CAAC,CAAC,CAAC7B,OAAOiE,IAAI;QAC/D,mCAAmCrC,KAAKC,SAAS,EAAC7B,eAAAA,OAAOiE,IAAI,qBAAXjE,aAAa4D,OAAO;QACtE,mCAAmChC,KAAKC,SAAS,CAAC7B,OAAOkE,WAAW;QACpE,kDAAkDtC,KAAKC,SAAS,CAC9D7B,OAAOmE,0BAA0B;QAEnC,0DAA0DvC,KAAKC,SAAS,CACtE7B,OAAOiC,YAAY,CAACmC,iCAAiC;QAEvD,4CAA4CxC,KAAKC,SAAS,CACxD7B,OAAOqE,yBAAyB;QAElC,iDAAiDzC,KAAKC,SAAS,CAC7D7B,OAAOiC,YAAY,CAACqC,oBAAoB,IACtCtE,OAAOiC,YAAY,CAACqC,oBAAoB,CAACC,MAAM,GAAG;QAEtD,6CAA6C3C,KAAKC,SAAS,CACzD7B,OAAOiC,YAAY,CAACqC,oBAAoB;QAE1C,mCAAmC1C,KAAKC,SAAS,CAAC7B,OAAOwE,WAAW;QACpE,GAAIxD,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiBY,KAAKC,SAAS,CAAC;QAClC,IACA4C,SAAS;QACb,GAAIzD,0BACA;YACE,yCAAyCY,KAAKC,SAAS,CACrD6C,IAAAA,8CAAsB,EAAC1E;QAE3B,IACAyE,SAAS;IACf;AACF;AAEO,SAAS3E,mBAAmB6E,OAA+B;IAChE,OAAO,IAAIC,gBAAO,CAACC,YAAY,CAAChF,aAAa8E;AAC/C"}