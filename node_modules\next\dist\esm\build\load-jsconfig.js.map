{"version": 3, "sources": ["../../src/build/load-jsconfig.ts"], "names": ["path", "fs", "Log", "getTypeScriptConfiguration", "readFileSync", "isError", "hasNecessaryDependencies", "TSCONFIG_WARNED", "parseJsonFile", "filePath", "JSON5", "require", "contents", "trim", "parse", "err", "codeFrameColumns", "codeFrame", "String", "start", "line", "lineNumber", "column", "columnNumber", "message", "highlightCode", "Error", "loadJsConfig", "dir", "config", "typeScriptPath", "deps", "pkg", "file", "exportsRestrict", "resolved", "get", "tsConfigPath", "join", "typescript", "tsconfigPath", "useTypeScript", "Boolean", "existsSync", "implicit<PERSON><PERSON><PERSON>l", "jsConfig", "info", "ts", "Promise", "resolve", "tsConfig", "compilerOptions", "options", "dirname", "jsConfigPath", "resolvedBaseUrl", "baseUrl"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,KAAI;AAEnB,YAAYC,SAAS,eAAc;AACnC,SAASC,0BAA0B,QAAQ,+CAA8C;AACzF,SAASC,YAAY,QAAQ,KAAI;AACjC,OAAOC,aAAa,kBAAiB;AACrC,SAASC,wBAAwB,QAAQ,oCAAmC;AAE5E,IAAIC,kBAAkB;AAEtB,SAASC,cAAcC,QAAgB;IACrC,MAAMC,QAAQC,QAAQ;IACtB,MAAMC,WAAWR,aAAaK,UAAU;IAExC,6BAA6B;IAC7B,IAAIG,SAASC,IAAI,OAAO,IAAI;QAC1B,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOH,MAAMI,KAAK,CAACF;IACrB,EAAE,OAAOG,KAAK;QACZ,IAAI,CAACV,QAAQU,MAAM,MAAMA;QACzB,MAAM,EAAEC,gBAAgB,EAAE,GAAGL,QAAQ;QACrC,MAAMM,YAAYD,iBAChBE,OAAON,WACP;YACEO,OAAO;gBACLC,MAAM,AAACL,IAAwCM,UAAU,IAAI;gBAC7DC,QAAQ,AAACP,IAA0CQ,YAAY,IAAI;YACrE;QACF,GACA;YAAEC,SAAST,IAAIS,OAAO;YAAEC,eAAe;QAAK;QAE9C,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAEjB,SAAS,IAAI,EAAEQ,UAAU,CAAC;IAChE;AACF;AAEA,eAAe,eAAeU,aAC5BC,GAAW,EACXC,MAA0B;IAE1B,IAAIC;IACJ,IAAI;QACF,MAAMC,OAAO,MAAMzB,yBAAyBsB,KAAK;YAC/C;gBACEI,KAAK;gBACLC,MAAM;gBACNC,iBAAiB;YACnB;SACD;QACDJ,iBAAiBC,KAAKI,QAAQ,CAACC,GAAG,CAAC;IACrC,EAAE,OAAM,CAAC;IACT,MAAMC,eAAerC,KAAKsC,IAAI,CAACV,KAAKC,OAAOU,UAAU,CAACC,YAAY;IAClE,MAAMC,gBAAgBC,QAAQZ,kBAAkB7B,GAAG0C,UAAU,CAACN;IAE9D,IAAIO;IACJ,IAAIC;IACJ,mCAAmC;IACnC,IAAIJ,eAAe;QACjB,IACEZ,OAAOU,UAAU,CAACC,YAAY,KAAK,mBACnCjC,oBAAoB,OACpB;YACAA,kBAAkB;YAClBL,IAAI4C,IAAI,CAAC,CAAC,qBAAqB,EAAEjB,OAAOU,UAAU,CAACC,YAAY,CAAC,CAAC;QACnE;QAEA,MAAMO,KAAM,MAAMC,QAAQC,OAAO,CAC/BtC,QAAQmB;QAEV,MAAMoB,WAAW,MAAM/C,2BAA2B4C,IAAIV,cAAc;QACpEQ,WAAW;YAAEM,iBAAiBD,SAASE,OAAO;QAAC;QAC/CR,kBAAkB5C,KAAKqD,OAAO,CAAChB;IACjC;IAEA,MAAMiB,eAAetD,KAAKsC,IAAI,CAACV,KAAK;IACpC,IAAI,CAACa,iBAAiBxC,GAAG0C,UAAU,CAACW,eAAe;QACjDT,WAAWrC,cAAc8C;QACzBV,kBAAkB5C,KAAKqD,OAAO,CAACC;IACjC;IAEA,IAAIC;IACJ,IAAIV,UAAU;YACRA;QAAJ,KAAIA,4BAAAA,SAASM,eAAe,qBAAxBN,0BAA0BW,OAAO,EAAE;YACrCD,kBAAkBvD,KAAKiD,OAAO,CAACrB,KAAKiB,SAASM,eAAe,CAACK,OAAO;QACtE,OAAO;YACLD,kBAAkBX;QACpB;IACF;IAEA,OAAO;QACLH;QACAI;QACAU;IACF;AACF"}