{"version": 3, "sources": ["../../../src/server/lib/dev-bundler-service.ts"], "names": ["createRequestResponseMocks", "DevBundlerService", "constructor", "bundler", "handler", "ensurePage", "definition", "hotReloader", "logErrorWithOriginalStack", "args", "getFallbackErrorComponents", "buildFallbackError", "page", "clientOnly", "undefined", "getCompilationError", "errors", "getCompilationErrors", "revalidate", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "revalidateOpts", "mocked", "url", "headers", "req", "res", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "unstable_onlyGenerated", "Error"], "mappings": "AAIA,SAASA,0BAA0B,QAAQ,iBAAgB;AAE3D;;;CAGC,GACD,OAAO,MAAMC;IACXC,YACmBC,SACAC,QACjB;uBAFiBD;uBACAC;aAGZC,aAAyD,OAC9DC;YAEA,oDAAoD;YACpD,OAAO,MAAM,IAAI,CAACH,OAAO,CAACI,WAAW,CAACF,UAAU,CAACC;QACnD;aAEOE,4BACL,OAAO,GAAGC;YACR,OAAO,MAAM,IAAI,CAACN,OAAO,CAACK,yBAAyB,IAAIC;QACzD;IAZC;IAcH,MAAaC,6BAA6B;QACxC,MAAM,IAAI,CAACP,OAAO,CAACI,WAAW,CAACI,kBAAkB;QACjD,4DAA4D;QAC5D,8DAA8D;QAC9D,MAAM,IAAI,CAACR,OAAO,CAACI,WAAW,CAACF,UAAU,CAAC;YACxCO,MAAM;YACNC,YAAY;YACZP,YAAYQ;QACd;IACF;IAEA,MAAaC,oBAAoBH,IAAY,EAAE;QAC7C,MAAMI,SAAS,MAAM,IAAI,CAACb,OAAO,CAACI,WAAW,CAACU,oBAAoB,CAACL;QACnE,IAAI,CAACI,QAAQ;QAEb,wCAAwC;QACxC,OAAOA,MAAM,CAAC,EAAE;IAClB;IAEA,MAAaE,WAAW,EACtBC,OAAO,EACPC,iBAAiB,EACjBC,MAAMC,cAAc,EAKrB,EAAE;QACD,MAAMC,SAASvB,2BAA2B;YACxCwB,KAAKL;YACLM,SAASL;QACX;QAEA,MAAM,IAAI,CAAChB,OAAO,CAACmB,OAAOG,GAAG,EAAEH,OAAOI,GAAG;QACzC,MAAMJ,OAAOI,GAAG,CAACC,WAAW;QAE5B,IACEL,OAAOI,GAAG,CAACE,SAAS,CAAC,sBAAsB,iBAC3C,CAAEN,CAAAA,OAAOI,GAAG,CAACG,UAAU,KAAK,OAAOR,eAAeS,sBAAsB,AAAD,GACvE;YACA,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAET,OAAOI,GAAG,CAACG,UAAU,CAAC,CAAC;QAC7D;QAEA,OAAO,CAAC;IACV;AACF"}