{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "names": ["PageSignatureError", "fromNodeOutgoingHttpHeaders", "NextFetchEvent", "NextRequest", "NextResponse", "relativizeURL", "waitUntilSymbol", "NextURL", "stripInternalSearchParams", "normalizeRscURL", "NEXT_ROUTER_PREFETCH", "NEXT_ROUTER_STATE_TREE", "RSC", "NEXT_QUERY_PARAM_PREFIX", "ensureInstrumentationRegistered", "RequestAsyncStorageWrapper", "requestAsyncStorage", "NextRequestHint", "constructor", "params", "input", "init", "sourcePage", "page", "request", "respondWith", "waitUntil", "FLIGHT_PARAMETERS", "adapter", "isEdgeRendering", "self", "__BUILD_MANIFEST", "prerenderManifest", "__PRERENDER_MANIFEST", "JSON", "parse", "undefined", "url", "requestUrl", "headers", "nextConfig", "keys", "searchParams", "key", "value", "getAll", "startsWith", "normalizedKey", "substring", "length", "delete", "val", "append", "buildId", "isDataReq", "pathname", "requestHeaders", "flightHeaders", "Map", "param", "toString", "toLowerCase", "get", "set", "normalizeUrl", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "body", "geo", "ip", "method", "signal", "Object", "defineProperty", "enumerable", "globalThis", "__incrementalCache", "IncrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "event", "response", "cookiesFromResponse", "isMiddleware", "wrap", "req", "renderOpts", "onUpdateCookies", "cookies", "previewProps", "previewModeEncryptionKey", "previewModeSigningKey", "handler", "Response", "TypeError", "rewrite", "rewriteUrl", "forceLocale", "host", "nextUrl", "String", "relativizedRewrite", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "redirect", "redirectURL", "finalResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "join", "Promise", "all", "fetchMetrics"], "mappings": "AAGA,SAASA,kBAAkB,QAAQ,UAAS;AAC5C,SAASC,2BAA2B,QAAQ,UAAS;AACrD,SAASC,cAAc,QAAQ,+BAA8B;AAC7D,SAASC,WAAW,QAAQ,2BAA0B;AACtD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,aAAa,QAAQ,+CAA8C;AAC5E,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,OAAO,QAAQ,aAAY;AACpC,SAASC,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,eAAe,QAAQ,0CAAyC;AACzE,SACEC,oBAAoB,EACpBC,sBAAsB,EACtBC,GAAG,QACE,6CAA4C;AACnD,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,SAASC,+BAA+B,QAAQ,YAAW;AAC3D,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,mBAAmB,QAAQ,yDAAwD;AAE5F,MAAMC,wBAAwBd;IAI5Be,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,IAAIxB,mBAAmB;YAAEuB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAG,cAAc;QACZ,MAAM,IAAIzB,mBAAmB;YAAEuB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAI,YAAY;QACV,MAAM,IAAI1B,mBAAmB;YAAEuB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;AACF;AAEA,MAAMK,oBAAoB;IACxB;QAACf;KAAI;IACL;QAACD;KAAuB;IACxB;QAACD;KAAqB;CACvB;AASD,OAAO,eAAekB,QACpBT,MAAsB;IAEtB,MAAML;IAEN,yCAAyC;IACzC,MAAMe,kBAAkB,OAAOC,KAAKC,gBAAgB,KAAK;IACzD,MAAMC,oBACJ,OAAOF,KAAKG,oBAAoB,KAAK,WACjCC,KAAKC,KAAK,CAACL,KAAKG,oBAAoB,IACpCG;IAENjB,OAAOK,OAAO,CAACa,GAAG,GAAG5B,gBAAgBU,OAAOK,OAAO,CAACa,GAAG;IAEvD,MAAMC,aAAa,IAAI/B,QAAQY,OAAOK,OAAO,CAACa,GAAG,EAAE;QACjDE,SAASpB,OAAOK,OAAO,CAACe,OAAO;QAC/BC,YAAYrB,OAAOK,OAAO,CAACgB,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAMC,OAAO;WAAIH,WAAWI,YAAY,CAACD,IAAI;KAAG;IAChD,KAAK,MAAME,OAAOF,KAAM;QACtB,MAAMG,QAAQN,WAAWI,YAAY,CAACG,MAAM,CAACF;QAE7C,IACEA,QAAQ9B,2BACR8B,IAAIG,UAAU,CAACjC,0BACf;YACA,MAAMkC,gBAAgBJ,IAAIK,SAAS,CAACnC,wBAAwBoC,MAAM;YAClEX,WAAWI,YAAY,CAACQ,MAAM,CAACH;YAE/B,KAAK,MAAMI,OAAOP,MAAO;gBACvBN,WAAWI,YAAY,CAACU,MAAM,CAACL,eAAeI;YAChD;YACAb,WAAWI,YAAY,CAACQ,MAAM,CAACP;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAMU,UAAUf,WAAWe,OAAO;IAClCf,WAAWe,OAAO,GAAG;IAErB,MAAMC,YAAYnC,OAAOK,OAAO,CAACe,OAAO,CAAC,gBAAgB;IAEzD,IAAIe,aAAahB,WAAWiB,QAAQ,KAAK,UAAU;QACjDjB,WAAWiB,QAAQ,GAAG;IACxB;IAEA,MAAMC,iBAAiBvD,4BAA4BkB,OAAOK,OAAO,CAACe,OAAO;IACzE,MAAMkB,gBAAgB,IAAIC;IAC1B,oDAAoD;IACpD,IAAI,CAAC7B,iBAAiB;QACpB,KAAK,MAAM8B,SAAShC,kBAAmB;YACrC,MAAMgB,MAAMgB,MAAMC,QAAQ,GAAGC,WAAW;YACxC,MAAMjB,QAAQY,eAAeM,GAAG,CAACnB;YACjC,IAAIC,OAAO;gBACTa,cAAcM,GAAG,CAACpB,KAAKa,eAAeM,GAAG,CAACnB;gBAC1Ca,eAAeN,MAAM,CAACP;YACxB;QACF;IACF;IAEA,MAAMqB,eAAeC,QAAQC,GAAG,CAACC,kCAAkC,GAC/D,IAAIC,IAAIjD,OAAOK,OAAO,CAACa,GAAG,IAC1BC;IAEJ,MAAMd,UAAU,IAAIP,gBAAgB;QAClCM,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOZ,0BAA0BwD,cAAc,MAAMJ,QAAQ;QAC7DvC,MAAM;YACJgD,MAAMlD,OAAOK,OAAO,CAAC6C,IAAI;YACzBC,KAAKnD,OAAOK,OAAO,CAAC8C,GAAG;YACvB/B,SAASiB;YACTe,IAAIpD,OAAOK,OAAO,CAAC+C,EAAE;YACrBC,QAAQrD,OAAOK,OAAO,CAACgD,MAAM;YAC7BhC,YAAYrB,OAAOK,OAAO,CAACgB,UAAU;YACrCiC,QAAQtD,OAAOK,OAAO,CAACiD,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAInB,WAAW;QACboB,OAAOC,cAAc,CAACnD,SAAS,YAAY;YACzCoD,YAAY;YACZhC,OAAO;QACT;IACF;IAEA,IACE,CAAC,AAACiC,WAAmBC,kBAAkB,IACvC,AAAC3D,OAAe4D,gBAAgB,EAChC;QACEF,WAAmBC,kBAAkB,GAAG,IAAI,AAC5C3D,OACA4D,gBAAgB,CAAC;YACjBC,QAAQ;YACRC,YAAY;YACZC,aAAajB,QAAQC,GAAG,CAACiB,QAAQ,KAAK;YACtCC,qBAAqBnB,QAAQC,GAAG,CAACmB,6BAA6B;YAC9DC,KAAKrB,QAAQC,GAAG,CAACiB,QAAQ,KAAK;YAC9B3B,gBAAgBrC,OAAOK,OAAO,CAACe,OAAO;YACtCgD,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAAS;wBACPC,eAAe;oBACjB;gBACF;YACF;QACF;IACF;IAEA,MAAMC,QAAQ,IAAI7F,eAAe;QAAEsB;QAASD,MAAMJ,OAAOI,IAAI;IAAC;IAC9D,IAAIyE;IACJ,IAAIC;IAEJ,8DAA8D;IAC9D,MAAMC,eACJ/E,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;IACnD,IAAI2E,cAAc;QAChBF,WAAW,MAAMjF,2BAA2BoF,IAAI,CAC9CnF,qBACA;YACEoF,KAAK5E;YACL6E,YAAY;gBACVC,iBAAiB,CAACC;oBAChBN,sBAAsBM;gBACxB;gBACA,2EAA2E;gBAC3EC,cAAcxE,CAAAA,qCAAAA,kBAAmB6D,OAAO,KAAI;oBAC1CC,eAAe;oBACfW,0BAA0B;oBAC1BC,uBAAuB;gBACzB;YACF;QACF,GACA,IAAMvF,OAAOwF,OAAO,CAACnF,SAASuE;IAElC,OAAO;QACLC,WAAW,MAAM7E,OAAOwF,OAAO,CAACnF,SAASuE;IAC3C;IAEA,yCAAyC;IACzC,IAAIC,YAAY,CAAEA,CAAAA,oBAAoBY,QAAO,GAAI;QAC/C,MAAM,IAAIC,UAAU;IACtB;IAEA,IAAIb,YAAYC,qBAAqB;QACnCD,SAASzD,OAAO,CAACwB,GAAG,CAAC,cAAckC;IACrC;IAEA;;;;;GAKC,GACD,MAAMa,UAAUd,4BAAAA,SAAUzD,OAAO,CAACuB,GAAG,CAAC;IACtC,IAAIkC,YAAYc,SAAS;QACvB,MAAMC,aAAa,IAAIxG,QAAQuG,SAAS;YACtCE,aAAa;YACbzE,SAASpB,OAAOK,OAAO,CAACe,OAAO;YAC/BC,YAAYrB,OAAOK,OAAO,CAACgB,UAAU;QACvC;QAEA,IAAI,CAACyB,QAAQC,GAAG,CAACC,kCAAkC,EAAE;YACnD,IAAI4C,WAAWE,IAAI,KAAKzF,QAAQ0F,OAAO,CAACD,IAAI,EAAE;gBAC5CF,WAAW1D,OAAO,GAAGA,WAAW0D,WAAW1D,OAAO;gBAClD2C,SAASzD,OAAO,CAACwB,GAAG,CAAC,wBAAwBoD,OAAOJ;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAMK,qBAAqB/G,cACzB8G,OAAOJ,aACPI,OAAO7E;QAGT,IACEgB,aACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACEW,CAAAA,QAAQC,GAAG,CAACmD,0CAA0C,IACtDD,mBAAmBE,KAAK,CAAC,gBAAe,GAE1C;YACAtB,SAASzD,OAAO,CAACwB,GAAG,CAAC,oBAAoBqD;QAC3C;IACF;IAEA;;;;GAIC,GACD,MAAMG,WAAWvB,4BAAAA,SAAUzD,OAAO,CAACuB,GAAG,CAAC;IACvC,IAAIkC,YAAYuB,YAAY,CAAC1F,iBAAiB;QAC5C,MAAM2F,cAAc,IAAIjH,QAAQgH,UAAU;YACxCP,aAAa;YACbzE,SAASpB,OAAOK,OAAO,CAACe,OAAO;YAC/BC,YAAYrB,OAAOK,OAAO,CAACgB,UAAU;QACvC;QAEA;;;KAGC,GACDwD,WAAW,IAAIY,SAASZ,SAAS3B,IAAI,EAAE2B;QAEvC,IAAI,CAAC/B,QAAQC,GAAG,CAACC,kCAAkC,EAAE;YACnD,IAAIqD,YAAYP,IAAI,KAAKzF,QAAQ0F,OAAO,CAACD,IAAI,EAAE;gBAC7CO,YAAYnE,OAAO,GAAGA,WAAWmE,YAAYnE,OAAO;gBACpD2C,SAASzD,OAAO,CAACwB,GAAG,CAAC,YAAYoD,OAAOK;YAC1C;QACF;QAEA;;;;KAIC,GACD,IAAIlE,WAAW;YACb0C,SAASzD,OAAO,CAACW,MAAM,CAAC;YACxB8C,SAASzD,OAAO,CAACwB,GAAG,CAClB,qBACA1D,cAAc8G,OAAOK,cAAcL,OAAO7E;QAE9C;IACF;IAEA,MAAMmF,gBAAgBzB,WAAWA,WAAW5F,aAAasH,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BF,cAAclF,OAAO,CAACuB,GAAG,CACzD;IAEF,MAAM8D,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAAChF,KAAKC,MAAM,IAAIa,cAAe;YACxCgE,cAAclF,OAAO,CAACwB,GAAG,CAAC,CAAC,qBAAqB,EAAEpB,IAAI,CAAC,EAAEC;YACzDgF,mBAAmBC,IAAI,CAAClF;QAC1B;QAEA,IAAIiF,mBAAmB3E,MAAM,GAAG,GAAG;YACjCwE,cAAclF,OAAO,CAACwB,GAAG,CACvB,iCACA4D,4BAA4B,MAAMC,mBAAmBE,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACL9B,UAAUyB;QACV/F,WAAWqG,QAAQC,GAAG,CAACjC,KAAK,CAACzF,gBAAgB;QAC7C2H,cAAczG,QAAQyG,YAAY;IACpC;AACF"}