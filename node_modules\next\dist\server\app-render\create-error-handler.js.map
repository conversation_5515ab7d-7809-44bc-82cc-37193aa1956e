{"version": 3, "sources": ["../../../src/server/app-render/create-error-handler.tsx"], "names": ["createErrorHandler", "_source", "dev", "isNextExport", "errorLogger", "capturedErrors", "allCapturedErrors", "err", "push", "digest", "DYNAMIC_ERROR_CODE", "isNotFoundError", "NEXT_DYNAMIC_NO_SSR_CODE", "isRedirectError", "isAbortError", "formatServerError", "message", "includes", "span", "getTracer", "getActiveScopeSpan", "recordException", "setStatus", "code", "SpanStatusCode", "ERROR", "catch", "process", "env", "NODE_ENV", "logAppDirError", "require", "console", "error", "stringHash", "stack", "toString"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;oCAhBmB;mEACZ;mCACW;0BACF;0BACA;4BACS;wBACC;8BACb;;;;;;AAStB,SAASA,mBAAmB,EACjC;;GAEC,GACDC,OAAO,EACPC,GAAG,EACHC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,iBAAiB,EAQlB;IACC,OAAO,CAACC;YA0BFA;QAzBJ,IAAID,mBAAmBA,kBAAkBE,IAAI,CAACD;QAE9C,IACEA,OACCA,CAAAA,IAAIE,MAAM,KAAKC,sCAAkB,IAChCC,IAAAA,yBAAe,EAACJ,QAChBA,IAAIE,MAAM,KAAKG,oCAAwB,IACvCC,IAAAA,yBAAe,EAACN,IAAG,GACrB;YACA,OAAOA,IAAIE,MAAM;QACnB;QAEA,8DAA8D;QAC9D,IAAIK,IAAAA,0BAAY,EAACP,MAAM;QAEvB,yEAAyE;QACzE,IAAIL,KAAK;YACPa,IAAAA,oCAAiB,EAACR;QACpB;QACA,kCAAkC;QAClC,8BAA8B;QAC9B,+CAA+C;QAC/C,IACE,CACEJ,CAAAA,iBACAI,wBAAAA,eAAAA,IAAKS,OAAO,qBAAZT,aAAcU,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMC,OAAOC,IAAAA,iBAAS,IAAGC,kBAAkB;YAC3C,IAAIF,MAAM;gBACRA,KAAKG,eAAe,CAACd;gBACrBW,KAAKI,SAAS,CAAC;oBACbC,MAAMC,sBAAc,CAACC,KAAK;oBAC1BT,SAAST,IAAIS,OAAO;gBACtB;YACF;YAEA,IAAIZ,aAAa;gBACfA,YAAYG,KAAKmB,KAAK,CAAC,KAAO;YAChC,OAAO;gBACL,kEAAkE;gBAClE,mCAAmC;gBACnC,mEAAmE;gBACnE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;oBACzC,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;oBACVD,eAAevB;gBACjB;gBACA,IAAIoB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;oBACzCG,QAAQC,KAAK,CAAC1B;gBAChB;YACF;QACF;QAEAF,eAAeG,IAAI,CAACD;QACpB,+EAA+E;QAC/E,OAAO2B,IAAAA,mBAAU,EAAC3B,IAAIS,OAAO,GAAGT,IAAI4B,KAAK,GAAI5B,CAAAA,IAAIE,MAAM,IAAI,EAAC,GAAI2B,QAAQ;IAC1E;AACF"}