{"version": 3, "sources": ["../../../src/build/webpack-build/impl.ts"], "names": ["red", "formatWebpackMessages", "nonNullable", "COMPILER_NAMES", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "APP_CLIENT_INTERNALS", "PHASE_PRODUCTION_BUILD", "runCompiler", "Log", "getBaseWebpackConfig", "loadProjectInfo", "TelemetryPlugin", "NextBuildContext", "resumePluginState", "getPluginState", "createEntrypoints", "loadConfig", "trace", "WEBPACK_LAYERS", "TraceEntryPointsPlugin", "origDebug", "debug", "isTelemetryPlugin", "plugin", "isTraceEntryPointsPlugin", "webpackBuildImpl", "compilerName", "result", "warnings", "errors", "stats", "webpackBuildStart", "nextBuildSpan", "dir", "config", "runWebpackSpan", "<PERSON><PERSON><PERSON><PERSON>", "entrypoints", "traceAsyncFn", "buildId", "envFiles", "loadedEnvFiles", "isDev", "rootDir", "pageExtensions", "pagesDir", "appDir", "pages", "mappedPages", "appPaths", "mappedAppPages", "previewMode", "previewProps", "rootPaths", "mappedRootPaths", "hasInstrumentationHook", "commonWebpackOptions", "isServer", "rewrites", "originalRewrites", "originalRedirects", "reactProductionProfiling", "noMangling", "clientRouterFilters", "previewModeId", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "configs", "info", "dev", "Promise", "all", "middlewareMatchers", "compilerType", "client", "server", "edgeServer", "clientConfig", "serverConfig", "edgeConfig", "optimization", "minimize", "minimizer", "length", "warn", "process", "hrtime", "clientResult", "serverResult", "edgeServerResult", "inputFileSystem", "start", "Date", "now", "pluginState", "key", "injectedClientEntries", "value", "clientEntry", "entry", "import", "layer", "appPagesBrowser", "dependOn", "purge", "concat", "filter", "traceFn", "telemetryPlugin", "plugins", "find", "traceEntryPointsPlugin", "webpackBuildEnd", "error", "Boolean", "join", "console", "indexOf", "page_name_regex", "parsed", "exec", "page_name", "groups", "Error", "err", "code", "buildSpinner", "stopAndPersist", "event", "duration", "buildTraceContext", "worker<PERSON>ain", "workerData", "Object", "assign", "buildContext", "entriesTrace", "chunksTrace", "entryNameMap", "depModArray", "entryEntries", "entryNameFilesMap"], "mappings": "AACA,SAASA,GAAG,QAAQ,uBAAsB;AAC1C,OAAOC,2BAA2B,yDAAwD;AAC1F,SAASC,WAAW,QAAQ,yBAAwB;AAEpD,SACEC,cAAc,EACdC,oCAAoC,EACpCC,oBAAoB,EACpBC,sBAAsB,QACjB,6BAA4B;AACnC,SAASC,WAAW,QAAQ,cAAa;AACzC,YAAYC,SAAS,gBAAe;AACpC,OAAOC,wBAAwBC,eAAe,QAAQ,oBAAmB;AAEzE,SAASC,eAAe,QAAQ,sCAAqC;AACrE,SACEC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,QACT,mBAAkB;AACzB,SAASC,iBAAiB,QAAQ,aAAY;AAC9C,OAAOC,gBAAgB,sBAAqB;AAC5C,SAASC,KAAK,QAAQ,cAAa;AACnC,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,sBAAsB,QAAQ,mDAAkD;AAIzF,OAAOC,eAAe,2BAA0B;AAEhD,MAAMC,QAAQD,UAAU;AAcxB,SAASE,kBAAkBC,MAAe;IACxC,OAAOA,kBAAkBZ;AAC3B;AAEA,SAASa,yBACPD,MAAe;IAEf,OAAOA,kBAAkBJ;AAC3B;AAEA,OAAO,eAAeM,iBACpBC,YAA4C;QA+MT,uBAIJ;IA7M/B,IAAIC,SAAgC;QAClCC,UAAU,EAAE;QACZC,QAAQ,EAAE;QACVC,OAAO,EAAE;IACX;IACA,IAAIC;IACJ,MAAMC,gBAAgBpB,iBAAiBoB,aAAa;IACpD,MAAMC,MAAMrB,iBAAiBqB,GAAG;IAChC,MAAMC,SAAStB,iBAAiBsB,MAAM;IAEtC,MAAMC,iBAAiBH,cAAcI,UAAU,CAAC;IAChD,MAAMC,cAAc,MAAML,cACvBI,UAAU,CAAC,sBACXE,YAAY,CAAC,IACZvB,kBAAkB;YAChBwB,SAAS3B,iBAAiB2B,OAAO;YACjCL,QAAQA;YACRM,UAAU5B,iBAAiB6B,cAAc;YACzCC,OAAO;YACPC,SAASV;YACTW,gBAAgBV,OAAOU,cAAc;YACrCC,UAAUjC,iBAAiBiC,QAAQ;YACnCC,QAAQlC,iBAAiBkC,MAAM;YAC/BC,OAAOnC,iBAAiBoC,WAAW;YACnCC,UAAUrC,iBAAiBsC,cAAc;YACzCC,aAAavC,iBAAiBwC,YAAY;YAC1CC,WAAWzC,iBAAiB0C,eAAe;YAC3CC,wBAAwB3C,iBAAiB2C,sBAAsB;QACjE;IAGJ,MAAMC,uBAAuB;QAC3BC,UAAU;QACVlB,SAAS3B,iBAAiB2B,OAAO;QACjCL,QAAQA;QACRY,QAAQlC,iBAAiBkC,MAAM;QAC/BD,UAAUjC,iBAAiBiC,QAAQ;QACnCa,UAAU9C,iBAAiB8C,QAAQ;QACnCC,kBAAkB/C,iBAAiB+C,gBAAgB;QACnDC,mBAAmBhD,iBAAiBgD,iBAAiB;QACrDC,0BAA0BjD,iBAAiBiD,wBAAwB;QACnEC,YAAYlD,iBAAiBkD,UAAU;QACvCC,qBAAqBnD,iBAAiBmD,mBAAmB;QACzDC,eAAepD,iBAAiBoD,aAAa;QAC7CC,6BAA6BrD,iBAAiBqD,2BAA2B;QACzEC,qBAAqBtD,iBAAiBsD,mBAAmB;IAC3D;IAEA,MAAMC,UAAU,MAAMhC,eACnBC,UAAU,CAAC,2BACXE,YAAY,CAAC;QACZ,MAAM8B,OAAO,MAAM1D,gBAAgB;YACjCuB;YACAC,QAAQsB,qBAAqBtB,MAAM;YACnCmC,KAAK;QACP;QACA,OAAOC,QAAQC,GAAG,CAAC;YACjB9D,qBAAqBwB,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBgB,oBAAoBnC,YAAYmC,kBAAkB;gBAClDrC;gBACAsC,cAActE,eAAeuE,MAAM;gBACnCrC,aAAaA,YAAYqC,MAAM;gBAC/B,GAAGN,IAAI;YACT;YACA3D,qBAAqBwB,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBrB;gBACAqC,oBAAoBnC,YAAYmC,kBAAkB;gBAClDC,cAActE,eAAewE,MAAM;gBACnCtC,aAAaA,YAAYsC,MAAM;gBAC/B,GAAGP,IAAI;YACT;YACA3D,qBAAqBwB,KAAK;gBACxB,GAAGuB,oBAAoB;gBACvBrB;gBACAqC,oBAAoBnC,YAAYmC,kBAAkB;gBAClDC,cAActE,eAAeyE,UAAU;gBACvCvC,aAAaA,YAAYuC,UAAU;gBACnC,GAAGR,IAAI;YACT;SACD;IACH;IAEF,MAAMS,eAAeV,OAAO,CAAC,EAAE;IAC/B,MAAMW,eAAeX,OAAO,CAAC,EAAE;IAC/B,MAAMY,aAAaZ,OAAO,CAAC,EAAE;IAE7B,IACEU,aAAaG,YAAY,IACxBH,CAAAA,aAAaG,YAAY,CAACC,QAAQ,KAAK,QACrCJ,aAAaG,YAAY,CAACE,SAAS,IAClCL,aAAaG,YAAY,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,GACpD;QACA3E,IAAI4E,IAAI,CACN,CAAC,iIAAiI,CAAC;IAEvI;IAEArD,oBAAoBsD,QAAQC,MAAM;IAElCjE,MAAM,CAAC,iBAAiB,CAAC,EAAEK;IAC3B,+EAA+E;IAC/E,MAAMS,eAAeG,YAAY,CAAC;QAChC,qDAAqD;QACrD,8DAA8D;QAC9D,IAAIiD,eAA4C;QAEhD,uEAAuE;QACvE,yEAAyE;QACzE,IAAIC,eACF;QACF,IAAIC,mBAEO;QAEX,IAAIC;QAEJ,IAAI,CAAChE,gBAAgBA,iBAAiB,UAAU;YAC9CL,MAAM;YACN,MAAMsE,QAAQC,KAAKC,GAAG;YACrB,CAACL,cAAcE,gBAAgB,GAAG,MAAMnF,YAAYuE,cAAc;gBACjE3C;gBACAuD;YACF;YACArE,MAAM,CAAC,yBAAyB,EAAEuE,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC1D;QAEA,IAAI,CAACjE,gBAAgBA,iBAAiB,eAAe;YACnDL,MAAM;YACN,MAAMsE,QAAQC,KAAKC,GAAG;YACrB,CAACJ,kBAAkBC,gBAAgB,GAAGX,aACnC,MAAMxE,YAAYwE,YAAY;gBAAE5C;gBAAgBuD;YAAgB,KAChE;gBAAC;aAAK;YACVrE,MAAM,CAAC,8BAA8B,EAAEuE,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC/D;QAEA,wCAAwC;QACxC,IAAI,EAACH,gCAAAA,aAAc3D,MAAM,CAACsD,MAAM,KAAI,EAACM,oCAAAA,iBAAkB5D,MAAM,CAACsD,MAAM,GAAE;YACpE,MAAMW,cAAchF;YACpB,IAAK,MAAMiF,OAAOD,YAAYE,qBAAqB,CAAE;gBACnD,MAAMC,QAAQH,YAAYE,qBAAqB,CAACD,IAAI;gBACpD,MAAMG,cAAcrB,aAAasB,KAAK;gBACtC,IAAIJ,QAAQ1F,sBAAsB;oBAChC6F,WAAW,CAAC9F,qCAAqC,GAAG;wBAClDgG,QAAQ;4BACN,6HAA6H;4BAC7H,oFAAoF;+BACjFF,WAAW,CAAC9F,qCAAqC,CAACgG,MAAM;4BAC3DH;yBACD;wBACDI,OAAOnF,eAAeoF,eAAe;oBACvC;gBACF,OAAO;oBACLJ,WAAW,CAACH,IAAI,GAAG;wBACjBQ,UAAU;4BAACnG;yBAAqC;wBAChDgG,QAAQH;wBACRI,OAAOnF,eAAeoF,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,CAAC5E,gBAAgBA,iBAAiB,UAAU;gBAC9CL,MAAM;gBACN,MAAMsE,QAAQC,KAAKC,GAAG;gBACrB,CAACN,cAAcG,gBAAgB,GAAG,MAAMnF,YAAYsE,cAAc;oBACjE1C;oBACAuD;gBACF;gBACArE,MAAM,CAAC,yBAAyB,EAAEuE,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;YAC1D;QACF;QAEAD,gBAAgBc,KAAK;QAErB7E,SAAS;YACPC,UAAU,AAAC,EAAE,CACV6E,MAAM,CACLlB,gCAAAA,aAAc3D,QAAQ,EACtB4D,gCAAAA,aAAc5D,QAAQ,EACtB6D,oCAAAA,iBAAkB7D,QAAQ,EAE3B8E,MAAM,CAACxG;YACV2B,QAAQ,AAAC,EAAE,CACR4E,MAAM,CACLlB,gCAAAA,aAAc1D,MAAM,EACpB2D,gCAAAA,aAAc3D,MAAM,EACpB4D,oCAAAA,iBAAkB5D,MAAM,EAEzB6E,MAAM,CAACxG;YACV4B,OAAO;gBACLyD,gCAAAA,aAAczD,KAAK;gBACnB0D,gCAAAA,aAAc1D,KAAK;gBACnB2D,oCAAAA,iBAAkB3D,KAAK;aACxB;QACH;IACF;IACAH,SAASK,cACNI,UAAU,CAAC,2BACXuE,OAAO,CAAC,IAAM1G,sBAAsB0B,QAAQ;IAE/Cf,iBAAiBgG,eAAe,IAAG,wBAAA,AACjC/B,aACAgC,OAAO,qBAF0B,sBAExBC,IAAI,CAACxF;IAEhB,MAAMyF,0BAAyB,wBAAA,AAC7BjC,aACA+B,OAAO,qBAFsB,sBAEpBC,IAAI,CAACtF;IAEhB,MAAMwF,kBAAkB3B,QAAQC,MAAM,CAACvD;IAEvC,IAAIJ,OAAOE,MAAM,CAACsD,MAAM,GAAG,GAAG;QAC5B,8DAA8D;QAC9D,0DAA0D;QAC1D,IAAIxD,OAAOE,MAAM,CAACsD,MAAM,GAAG,GAAG;YAC5BxD,OAAOE,MAAM,CAACsD,MAAM,GAAG;QACzB;QACA,IAAI8B,QAAQtF,OAAOE,MAAM,CAAC6E,MAAM,CAACQ,SAASC,IAAI,CAAC;QAE/CC,QAAQH,KAAK,CAACjH,IAAI;QAElB,IACEiH,MAAMI,OAAO,CAAC,wBAAwB,CAAC,KACvCJ,MAAMI,OAAO,CAAC,uCAAuC,CAAC,GACtD;YACA,MAAMC,kBAAkB;YACxB,MAAMC,SAASD,gBAAgBE,IAAI,CAACP;YACpC,MAAMQ,YAAYF,UAAUA,OAAOG,MAAM,IAAIH,OAAOG,MAAM,CAACD,SAAS;YACpE,MAAM,IAAIE,MACR,CAAC,sFAAsF,EAAEF,UAAU,oFAAoF,CAAC;QAE5L;QAEAL,QAAQH,KAAK,CAACA;QACdG,QAAQH,KAAK;QAEb,IACEA,MAAMI,OAAO,CAAC,wBAAwB,CAAC,KACvCJ,MAAMI,OAAO,CAAC,uBAAuB,CAAC,GACtC;YACA,MAAMO,MAAM,IAAID,MACd;YAEFC,IAAIC,IAAI,GAAG;YACX,MAAMD;QACR;QACA,MAAMA,MAAM,IAAID,MAAM;QACtBC,IAAIC,IAAI,GAAG;QACX,MAAMD;IACR,OAAO;QACL,IAAIjG,OAAOC,QAAQ,CAACuD,MAAM,GAAG,GAAG;YAC9B3E,IAAI4E,IAAI,CAAC;YACTgC,QAAQhC,IAAI,CAACzD,OAAOC,QAAQ,CAAC8E,MAAM,CAACQ,SAASC,IAAI,CAAC;YAClDC,QAAQhC,IAAI;QACd,OAAO,IAAI,CAAC1D,cAAc;gBACxBd;aAAAA,iCAAAA,iBAAiBkH,YAAY,qBAA7BlH,+BAA+BmH,cAAc;YAC7CvH,IAAIwH,KAAK,CAAC;QACZ;QAEA,OAAO;YACLC,UAAUjB,eAAe,CAAC,EAAE;YAC5BkB,iBAAiB,EAAEnB,0CAAAA,uBAAwBmB,iBAAiB;YAC5DpC,aAAahF;QACf;IACF;AACF;AAEA,sDAAsD;AACtD,OAAO,eAAeqH,WAAWC,UAGhC;IACC,0EAA0E;IAC1EC,OAAOC,MAAM,CAAC1H,kBAAkBwH,WAAWG,YAAY;IAEvD,sBAAsB;IACtB1H,kBAAkBD,iBAAiBkF,WAAW;IAE9C,iDAAiD;IACjDlF,iBAAiBsB,MAAM,GAAG,MAAMlB,WAC9BV,wBACAM,iBAAiBqB,GAAG;IAEtBrB,iBAAiBoB,aAAa,GAAGf,MAAM;IAEvC,MAAMU,SAAS,MAAMF,iBAAiB2G,WAAW1G,YAAY;IAC7D,MAAM,EAAE8G,YAAY,EAAEC,WAAW,EAAE,GAAG9G,OAAOuG,iBAAiB,IAAI,CAAC;IACnE,IAAIM,cAAc;QAChB,MAAM,EAAEE,YAAY,EAAEC,WAAW,EAAE,GAAGH;QACtC,IAAIG,aAAa;YACfhH,OAAOuG,iBAAiB,CAAEM,YAAY,CAAEG,WAAW,GAAGA;QACxD;QACA,IAAID,cAAc;YAChB,MAAME,eAAeF;YACrB/G,OAAOuG,iBAAiB,CAAEM,YAAY,CAAEE,YAAY,GAAGE;QACzD;IACF;IACA,IAAIH,+BAAAA,YAAaI,iBAAiB,EAAE;QAClC,MAAMA,oBAAoBJ,YAAYI,iBAAiB;QACvDlH,OAAOuG,iBAAiB,CAAEO,WAAW,CAAEI,iBAAiB,GAAGA;IAC7D;IACA,OAAOlH;AACT"}