/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=Bot,Maximize2,Minimize2,Send,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bot,Maximize2,Minimize2,Send,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bot: () => (/* reexport safe */ _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Maximize2: () => (/* reexport safe */ _icons_maximize_2_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Minimize2: () => (/* reexport safe */ _icons_minimize_2_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Send: () => (/* reexport safe */ _icons_send_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bot.js */ \"./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _icons_maximize_2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/maximize-2.js */ \"./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _icons_minimize_2_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/minimize-2.js */ \"./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _icons_send_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/send.js */ \"./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3QsTWF4aW1pemUyLE1pbmltaXplMixTZW5kLFVzZXIsWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQytDO0FBQ2E7QUFDQTtBQUNYO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXNrLW1hbmFnZW1lbnQtYXBwLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/YmZkZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGx1Y2lkZS1yZWFjdCB2MC4yOTIuMCAtIElTQ1xuICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3QgfSBmcm9tIFwiLi9pY29ucy9ib3QuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYXhpbWl6ZTIgfSBmcm9tIFwiLi9pY29ucy9tYXhpbWl6ZS0yLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWluaW1pemUyIH0gZnJvbSBcIi4vaWNvbnMvbWluaW1pemUtMi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNlbmQgfSBmcm9tIFwiLi9pY29ucy9zZW5kLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlciB9IGZyb20gXCIuL2ljb25zL3VzZXIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYIH0gZnJvbSBcIi4vaWNvbnMveC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bot,Maximize2,Minimize2,Send,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Calendar,Edit,MoreVertical,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Calendar,Edit,MoreVertical,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Calendar: () => (/* reexport safe */ _icons_calendar_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Edit: () => (/* reexport safe */ _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MoreVertical: () => (/* reexport safe */ _icons_more_vertical_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_calendar_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/calendar.js */ \"./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/pen-square.js */ \"./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _icons_more_vertical_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/more-vertical.js */ \"./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/trash-2.js */ \"./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DYWxlbmRhcixFZGl0LE1vcmVWZXJ0aWNhbCxUcmFzaDIhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ3lEO0FBQ0Y7QUFDVyIsInNvdXJjZXMiOlsid2VicGFjazovL3Rhc2stbWFuYWdlbWVudC1hcHAvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9jZDAxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbHVjaWRlLXJlYWN0IHYwLjI5Mi4wIC0gSVNDXG4gKi8gXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGVuZGFyIH0gZnJvbSBcIi4vaWNvbnMvY2FsZW5kYXIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFZGl0IH0gZnJvbSBcIi4vaWNvbnMvcGVuLXNxdWFyZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1vcmVWZXJ0aWNhbCB9IGZyb20gXCIuL2ljb25zL21vcmUtdmVydGljYWwuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmFzaDIgfSBmcm9tIFwiLi9pY29ucy90cmFzaC0yLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Calendar,Edit,MoreVertical,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Check,GripVertical,Plus,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Check,GripVertical,Plus,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Check: () => (/* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   GripVertical: () => (/* reexport safe */ _icons_grip_vertical_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Plus: () => (/* reexport safe */ _icons_plus_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ \"./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _icons_grip_vertical_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/grip-vertical.js */ \"./node_modules/lucide-react/dist/esm/icons/grip-vertical.js\");\n/* harmony import */ var _icons_plus_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/plus.js */ \"./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVjayxHcmlwVmVydGljYWwsUGx1cyxYIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNtRDtBQUNlO0FBQ2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFzay1tYW5hZ2VtZW50LWFwcC8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2RlYzMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBsdWNpZGUtcmVhY3QgdjAuMjkyLjAgLSBJU0NcbiAqLyBcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hlY2sgfSBmcm9tIFwiLi9pY29ucy9jaGVjay5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEdyaXBWZXJ0aWNhbCB9IGZyb20gXCIuL2ljb25zL2dyaXAtdmVydGljYWwuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbHVzIH0gZnJvbSBcIi4vaWNvbnMvcGx1cy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Check,GripVertical,Plus,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=CheckSquare,LogOut,MessageCircle,Plus,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CheckSquare,LogOut,MessageCircle,Plus,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckSquare: () => (/* reexport safe */ _icons_check_square_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MessageCircle: () => (/* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Plus: () => (/* reexport safe */ _icons_plus_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_square_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check-square.js */ \"./node_modules/lucide-react/dist/esm/icons/check-square.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_plus_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/plus.js */ \"./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVja1NxdWFyZSxMb2dPdXQsTWVzc2FnZUNpcmNsZSxQbHVzLFVzZXIhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDZ0U7QUFDVjtBQUNjO0FBQ25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFzay1tYW5hZ2VtZW50LWFwcC8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2RlMzQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBsdWNpZGUtcmVhY3QgdjAuMjkyLjAgLSBJU0NcbiAqLyBcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hlY2tTcXVhcmUgfSBmcm9tIFwiLi9pY29ucy9jaGVjay1zcXVhcmUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMb2dPdXQgfSBmcm9tIFwiLi9pY29ucy9sb2ctb3V0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWVzc2FnZUNpcmNsZSB9IGZyb20gXCIuL2ljb25zL21lc3NhZ2UtY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGx1cyB9IGZyb20gXCIuL2ljb25zL3BsdXMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=CheckSquare,LogOut,MessageCircle,Plus,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Eye,EyeOff,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*********************************************************************************************************!*\
  !*** __barrel_optimize__?names=Eye,EyeOff,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EyeOff: () => (/* reexport safe */ _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   LogIn: () => (/* reexport safe */ _icons_log_in_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/eye-off.js */ \"./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _icons_log_in_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/log-in.js */ \"./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeWUsRXllT2ZmLExvZ0luIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDK0M7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL3Rhc2stbWFuYWdlbWVudC1hcHAvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz84MGQ0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbHVjaWRlLXJlYWN0IHYwLjI5Mi4wIC0gSVNDXG4gKi8gXG5leHBvcnQgeyBkZWZhdWx0IGFzIEV5ZSB9IGZyb20gXCIuL2ljb25zL2V5ZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEV5ZU9mZiB9IGZyb20gXCIuL2ljb25zL2V5ZS1vZmYuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMb2dJbiB9IGZyb20gXCIuL2ljb25zL2xvZy1pbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Eye,EyeOff,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Eye,EyeOff,UserPlus!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Eye,EyeOff,UserPlus!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EyeOff: () => (/* reexport safe */ _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   UserPlus: () => (/* reexport safe */ _icons_user_plus_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/eye-off.js */ \"./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _icons_user_plus_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/user-plus.js */ \"./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeWUsRXllT2ZmLFVzZXJQbHVzIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDK0M7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL3Rhc2stbWFuYWdlbWVudC1hcHAvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz84NDdjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbHVjaWRlLXJlYWN0IHYwLjI5Mi4wIC0gSVNDXG4gKi8gXG5leHBvcnQgeyBkZWZhdWx0IGFzIEV5ZSB9IGZyb20gXCIuL2ljb25zL2V5ZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEV5ZU9mZiB9IGZyb20gXCIuL2ljb25zL2V5ZS1vZmYuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyUGx1cyB9IGZyb20gXCIuL2ljb25zL3VzZXItcGx1cy5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Eye,EyeOff,UserPlus!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Kanban,List!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************!*\
  !*** __barrel_optimize__?names=Kanban,List!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Kanban: () => (/* reexport safe */ _icons_kanban_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   List: () => (/* reexport safe */ _icons_list_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_kanban_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/kanban.js */ \"./node_modules/lucide-react/dist/esm/icons/kanban.js\");\n/* harmony import */ var _icons_list_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/list.js */ \"./node_modules/lucide-react/dist/esm/icons/list.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1LYW5iYW4sTGlzdCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNxRCIsInNvdXJjZXMiOlsid2VicGFjazovL3Rhc2stbWFuYWdlbWVudC1hcHAvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz82YTE4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbHVjaWRlLXJlYWN0IHYwLjI5Mi4wIC0gSVNDXG4gKi8gXG5leHBvcnQgeyBkZWZhdWx0IGFzIEthbmJhbiB9IGZyb20gXCIuL2ljb25zL2thbmJhbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExpc3QgfSBmcm9tIFwiLi9pY29ucy9saXN0LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Kanban,List!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Plus,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************!*\
  !*** __barrel_optimize__?names=Plus,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Plus: () => (/* reexport safe */ _icons_plus_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_plus_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/plus.js */ \"./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1QbHVzLFghPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDaUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXNrLW1hbmFnZW1lbnQtYXBwLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/OGViNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGx1Y2lkZS1yZWFjdCB2MC4yOTIuMCAtIElTQ1xuICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbHVzIH0gZnJvbSBcIi4vaWNvbnMvcGx1cy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Plus,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Save,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************!*\
  !*** __barrel_optimize__?names=Save,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Save: () => (/* reexport safe */ _icons_save_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_save_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/save.js */ \"./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1TYXZlLFghPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDaUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXNrLW1hbmFnZW1lbnQtYXBwLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/OGViOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGx1Y2lkZS1yZWFjdCB2MC4yOTIuMCAtIElTQ1xuICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTYXZlIH0gZnJvbSBcIi4vaWNvbnMvc2F2ZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Save,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.js */ \"./pages/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/AIAssistant.js":
/*!***********************************!*\
  !*** ./components/AIAssistant.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _lib_openrouter_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/openrouter-config */ \"./lib/openrouter-config.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Maximize2_Minimize2_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Maximize2,Minimize2,Send,User,X!=!lucide-react */ \"__barrel_optimize__?names=Bot,Maximize2,Minimize2,Send,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_store__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_store__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction AIAssistant({ isOpen, onClose }) {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            type: \"assistant\",\n            content: 'Hi! I\\'m your task assistant. I can help you create, move, and manage your tasks. Try saying \"Create a new task called Design Homepage\" or \"Move task X to Done\".'\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { tasks, addTask, updateTask, deleteTask, findTaskByName } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useTaskStore)();\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleTaskCommand = async (command)=>{\n        try {\n            switch(command.action){\n                case \"create\":\n                    const newTask = await addTask({\n                        title: command.taskName,\n                        description: \"\",\n                        status: command.column || \"todo\",\n                        priority: \"medium\"\n                    });\n                    return `Task \"${command.taskName}\" created successfully in the ${command.column === \"todo\" ? \"To Do\" : command.column === \"inprogress\" ? \"In Progress\" : \"Done\"} column.`;\n                case \"move\":\n                    const taskToMove = findTaskByName(command.taskName);\n                    if (!taskToMove) {\n                        return `I couldn't find a task with the name \"${command.taskName}\". Please check the task name and try again.`;\n                    }\n                    await updateTask(taskToMove.id, {\n                        status: command.column\n                    });\n                    return `Task \"${taskToMove.title}\" moved to ${command.column === \"todo\" ? \"To Do\" : command.column === \"inprogress\" ? \"In Progress\" : \"Done\"} successfully.`;\n                case \"delete\":\n                    const taskToDelete = findTaskByName(command.taskName);\n                    if (!taskToDelete) {\n                        return `I couldn't find a task with the name \"${command.taskName}\". Please check the task name and try again.`;\n                    }\n                    await deleteTask(taskToDelete.id);\n                    return `Task \"${taskToDelete.title}\" deleted successfully.`;\n                default:\n                    return await handleGeneralQuery(command.message);\n            }\n        } catch (error) {\n            console.error(\"Error executing task command:\", error);\n            return \"Sorry, I encountered an error while processing your request. Please try again.\";\n        }\n    };\n    const handleGeneralQuery = async (message)=>{\n        const taskCount = tasks.length;\n        const todoCount = tasks.filter((t)=>t.status === \"todo\").length;\n        const inProgressCount = tasks.filter((t)=>t.status === \"inprogress\").length;\n        const doneCount = tasks.filter((t)=>t.status === \"done\").length;\n        const lowerMessage = message.toLowerCase();\n        if (lowerMessage.includes(\"how many\") || lowerMessage.includes(\"count\")) {\n            return `You currently have ${taskCount} total tasks: ${todoCount} in To Do, ${inProgressCount} in Progress, and ${doneCount} completed.`;\n        }\n        if (lowerMessage.includes(\"what should i\") || lowerMessage.includes(\"focus\") || lowerMessage.includes(\"priority\")) {\n            const highPriorityTasks = tasks.filter((t)=>t.priority === \"high\" && t.status !== \"done\");\n            if (highPriorityTasks.length > 0) {\n                return `You should focus on these high-priority tasks: ${highPriorityTasks.map((t)=>t.title).join(\", \")}`;\n            }\n            const todoTasks = tasks.filter((t)=>t.status === \"todo\");\n            if (todoTasks.length > 0) {\n                return `Consider starting with: ${todoTasks.slice(0, 3).map((t)=>t.title).join(\", \")}`;\n            }\n            return \"Great job! You don't have any pending tasks right now.\";\n        }\n        if (lowerMessage.includes(\"help\") || lowerMessage.includes(\"what can you do\")) {\n            return 'I can help you with:\\n• Creating tasks: \"Create a new task called [name]\"\\n• Moving tasks: \"Move task [name] to [To Do/In Progress/Done]\"\\n• Deleting tasks: \"Delete task [name]\"\\n• Getting task summaries and priorities\\n• Answering questions about your tasks';\n        }\n        return \"I'm here to help with your tasks! You can ask me to create, move, or delete tasks, or ask about your current task status.\";\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!input.trim() || loading) return;\n        const userMessage = {\n            id: Date.now(),\n            type: \"user\",\n            content: input.trim()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setLoading(true);\n        try {\n            const command = (0,_lib_openrouter_config__WEBPACK_IMPORTED_MODULE_3__.parseTaskCommand)(input.trim(), tasks);\n            const response = await handleTaskCommand(command);\n            const assistantMessage = {\n                id: Date.now() + 1,\n                type: \"assistant\",\n                content: response\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error processing message:\", error);\n            const errorMessage = {\n                id: Date.now() + 1,\n                type: \"assistant\",\n                content: \"Sorry, I encountered an error. Please try again.\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-xl z-50 transition-all duration-300 ${isMinimized ? \"w-80 h-16\" : \"w-80 h-96\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-blue-50 rounded-t-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Maximize2_Minimize2_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                className: \"h-5 w-5 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-gray-900\",\n                                children: \"AI Assistant\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMinimized(!isMinimized),\n                                className: \"text-gray-400 hover:text-gray-600 p-1\",\n                                children: isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Maximize2_Minimize2_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Maximize2, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                    lineNumber: 150,\n                                    columnNumber: 28\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Maximize2_Minimize2_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Minimize2, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                    lineNumber: 150,\n                                    columnNumber: 64\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-gray-400 hover:text-gray-600 p-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Maximize2_Minimize2_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.X, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4 h-64 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex ${message.type === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `flex items-start space-x-2 max-w-[80%] ${message.type === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${message.type === \"user\" ? \"bg-blue-600\" : \"bg-gray-200\"}`,\n                                                    children: message.type === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Maximize2_Minimize2_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.User, {\n                                                        className: \"h-3 w-3 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Maximize2_Minimize2_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                                        className: \"h-3 w-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `px-3 py-2 rounded-lg text-sm ${message.type === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-900\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"whitespace-pre-wrap\",\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)),\n                                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Maximize2_Minimize2_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                                    className: \"h-3 w-3 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-100 px-3 py-2 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.1s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"0.2s\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                                lineNumber: 198,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    placeholder: \"Ask me to create, move, or manage tasks...\",\n                                    className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                    disabled: loading\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading || !input.trim(),\n                                    className: \"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Maximize2_Minimize2_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Send, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AIAssistant.js\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AIAssistant.js\n");

/***/ }),

/***/ "./components/AddTaskModal.js":
/*!************************************!*\
  !*** ./components/AddTaskModal.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddTaskModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,X!=!lucide-react */ \"__barrel_optimize__?names=Plus,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_store__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_store__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction AddTaskModal({ isOpen, onClose }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        status: \"todo\",\n        priority: \"medium\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { addTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useTaskStore)();\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            await addTask(formData);\n            setFormData({\n                title: \"\",\n                description: \"\",\n                status: \"todo\",\n                priority: \"medium\"\n            });\n            onClose();\n        } catch (error) {\n            console.error(\"Failed to add task:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Add New Task\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"title\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Task Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"title\",\n                                    name: \"title\",\n                                    required: true,\n                                    value: formData.title,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    placeholder: \"Enter task title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"description\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"description\",\n                                    name: \"description\",\n                                    rows: 3,\n                                    value: formData.description,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    placeholder: \"Enter task description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"status\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"status\",\n                                    name: \"status\",\n                                    value: formData.status,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"todo\",\n                                            children: \"To Do\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inprogress\",\n                                            children: \"In Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"done\",\n                                            children: \"Done\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"priority\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Priority\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"priority\",\n                                    name: \"priority\",\n                                    value: formData.priority,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"low\",\n                                            children: \"Low\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"medium\",\n                                            children: \"Medium\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"high\",\n                                            children: \"High\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Plus, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Task\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\AddTaskModal.js\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0FkZFRhc2tNb2RhbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFpQztBQUNXO0FBQ0w7QUFFeEIsU0FBU0ksYUFBYSxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRTtJQUN0RCxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR1IsK0NBQVFBLENBQUM7UUFDdkNTLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxRQUFRO1FBQ1JDLFVBQVU7SUFDWjtJQUNBLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHZCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLEVBQUVlLE9BQU8sRUFBRSxHQUFHZCx3REFBWUE7SUFFaEMsTUFBTWUsZUFBZSxDQUFDQztRQUNwQlQsWUFBWTtZQUNWLEdBQUdELFFBQVE7WUFDWCxDQUFDVSxFQUFFQyxNQUFNLENBQUNDLElBQUksQ0FBQyxFQUFFRixFQUFFQyxNQUFNLENBQUNFLEtBQUs7UUFDakM7SUFDRjtJQUVBLE1BQU1DLGVBQWUsT0FBT0o7UUFDMUJBLEVBQUVLLGNBQWM7UUFDaEJSLFdBQVc7UUFFWCxJQUFJO1lBQ0YsTUFBTUMsUUFBUVI7WUFDZEMsWUFBWTtnQkFDVkMsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsUUFBUTtnQkFDUkMsVUFBVTtZQUNaO1lBQ0FOO1FBQ0YsRUFBRSxPQUFPaUIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsdUJBQXVCQTtRQUN2QyxTQUFVO1lBQ1JULFdBQVc7UUFDYjtJQUNGO0lBRUEsSUFBSSxDQUFDVCxRQUFRLE9BQU87SUFFcEIscUJBQ0UsOERBQUNvQjtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBc0M7Ozs7OztzQ0FDcEQsOERBQUNFOzRCQUNDQyxTQUFTdkI7NEJBQ1RvQixXQUFVO3NDQUVWLDRFQUFDeEIseUVBQUNBO2dDQUFDd0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSWpCLDhEQUFDSTtvQkFBS0MsVUFBVVY7b0JBQWNLLFdBQVU7O3NDQUN0Qyw4REFBQ0Q7OzhDQUNDLDhEQUFDTztvQ0FBTUMsU0FBUTtvQ0FBUVAsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEYsOERBQUNRO29DQUNDQyxNQUFLO29DQUNMQyxJQUFHO29DQUNIakIsTUFBSztvQ0FDTGtCLFFBQVE7b0NBQ1JqQixPQUFPYixTQUFTRSxLQUFLO29DQUNyQjZCLFVBQVV0QjtvQ0FDVlUsV0FBVTtvQ0FDVmEsYUFBWTs7Ozs7Ozs7Ozs7O3NDQUloQiw4REFBQ2Q7OzhDQUNDLDhEQUFDTztvQ0FBTUMsU0FBUTtvQ0FBY1AsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHdEYsOERBQUNjO29DQUNDSixJQUFHO29DQUNIakIsTUFBSztvQ0FDTHNCLE1BQU07b0NBQ05yQixPQUFPYixTQUFTRyxXQUFXO29DQUMzQjRCLFVBQVV0QjtvQ0FDVlUsV0FBVTtvQ0FDVmEsYUFBWTs7Ozs7Ozs7Ozs7O3NDQUloQiw4REFBQ2Q7OzhDQUNDLDhEQUFDTztvQ0FBTUMsU0FBUTtvQ0FBU1AsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHakYsOERBQUNnQjtvQ0FDQ04sSUFBRztvQ0FDSGpCLE1BQUs7b0NBQ0xDLE9BQU9iLFNBQVNJLE1BQU07b0NBQ3RCMkIsVUFBVXRCO29DQUNWVSxXQUFVOztzREFFViw4REFBQ2lCOzRDQUFPdkIsT0FBTTtzREFBTzs7Ozs7O3NEQUNyQiw4REFBQ3VCOzRDQUFPdkIsT0FBTTtzREFBYTs7Ozs7O3NEQUMzQiw4REFBQ3VCOzRDQUFPdkIsT0FBTTtzREFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUl6Qiw4REFBQ0s7OzhDQUNDLDhEQUFDTztvQ0FBTUMsU0FBUTtvQ0FBV1AsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHbkYsOERBQUNnQjtvQ0FDQ04sSUFBRztvQ0FDSGpCLE1BQUs7b0NBQ0xDLE9BQU9iLFNBQVNLLFFBQVE7b0NBQ3hCMEIsVUFBVXRCO29DQUNWVSxXQUFVOztzREFFViw4REFBQ2lCOzRDQUFPdkIsT0FBTTtzREFBTTs7Ozs7O3NEQUNwQiw4REFBQ3VCOzRDQUFPdkIsT0FBTTtzREFBUzs7Ozs7O3NEQUN2Qiw4REFBQ3VCOzRDQUFPdkIsT0FBTTtzREFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUl6Qiw4REFBQ0s7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FDQ08sTUFBSztvQ0FDTE4sU0FBU3ZCO29DQUNUb0IsV0FBVTs4Q0FDWDs7Ozs7OzhDQUdELDhEQUFDRTtvQ0FDQ08sTUFBSztvQ0FDTFMsVUFBVS9CO29DQUNWYSxXQUFVOzhDQUVUYix3QkFDQyw4REFBQ1k7d0NBQUlDLFdBQVU7Ozs7OzZEQUVmOzswREFDRSw4REFBQ3ZCLDRFQUFJQTtnREFBQ3VCLFdBQVU7Ozs7OzswREFDaEIsOERBQUNtQjswREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU3hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFzay1tYW5hZ2VtZW50LWFwcC8uL2NvbXBvbmVudHMvQWRkVGFza01vZGFsLmpzP2ZjOTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUYXNrU3RvcmUgfSBmcm9tICcuLi9saWIvc3RvcmUnO1xuaW1wb3J0IHsgWCwgUGx1cyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkZFRhc2tNb2RhbCh7IGlzT3Blbiwgb25DbG9zZSB9KSB7XG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGUoe1xuICAgIHRpdGxlOiAnJyxcbiAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgc3RhdHVzOiAndG9kbycsXG4gICAgcHJpb3JpdHk6ICdtZWRpdW0nXG4gIH0pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IHsgYWRkVGFzayB9ID0gdXNlVGFza1N0b3JlKCk7XG5cbiAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKGUpID0+IHtcbiAgICBzZXRGb3JtRGF0YSh7XG4gICAgICAuLi5mb3JtRGF0YSxcbiAgICAgIFtlLnRhcmdldC5uYW1lXTogZS50YXJnZXQudmFsdWVcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGFkZFRhc2soZm9ybURhdGEpO1xuICAgICAgc2V0Rm9ybURhdGEoe1xuICAgICAgICB0aXRsZTogJycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICAgICAgc3RhdHVzOiAndG9kbycsXG4gICAgICAgIHByaW9yaXR5OiAnbWVkaXVtJ1xuICAgICAgfSk7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBhZGQgdGFzazonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgcC02IHctZnVsbCBtYXgtdy1tZCBteC00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5BZGQgTmV3IFRhc2s8L2gyPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJ0aXRsZVwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgIFRhc2sgVGl0bGVcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICBpZD1cInRpdGxlXCJcbiAgICAgICAgICAgICAgbmFtZT1cInRpdGxlXCJcbiAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRpdGxlfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB0YXNrIHRpdGxlXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJkZXNjcmlwdGlvblwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgIERlc2NyaXB0aW9uXG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgIGlkPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICBuYW1lPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHRhc2sgZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cInN0YXR1c1wiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgIFN0YXR1c1xuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgaWQ9XCJzdGF0dXNcIlxuICAgICAgICAgICAgICBuYW1lPVwic3RhdHVzXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnN0YXR1c31cbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJ0b2RvXCI+VG8gRG88L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImlucHJvZ3Jlc3NcIj5JbiBQcm9ncmVzczwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiZG9uZVwiPkRvbmU8L29wdGlvbj5cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwicHJpb3JpdHlcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICBQcmlvcml0eVxuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgaWQ9XCJwcmlvcml0eVwiXG4gICAgICAgICAgICAgIG5hbWU9XCJwcmlvcml0eVwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wcmlvcml0eX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJsb3dcIj5Mb3c8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm1lZGl1bVwiPk1lZGl1bTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiaGlnaFwiPkhpZ2g8L29wdGlvbj5cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMyBwdC00XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHgtNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgdGV4dC1ncmF5LTcwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgcHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNCB3LTQgYm9yZGVyLWItMiBib3JkZXItd2hpdGVcIj48L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5BZGQgVGFzazwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvcm0+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVRhc2tTdG9yZSIsIlgiLCJQbHVzIiwiQWRkVGFza01vZGFsIiwiaXNPcGVuIiwib25DbG9zZSIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwic3RhdHVzIiwicHJpb3JpdHkiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImFkZFRhc2siLCJoYW5kbGVDaGFuZ2UiLCJlIiwidGFyZ2V0IiwibmFtZSIsInZhbHVlIiwiaGFuZGxlU3VibWl0IiwicHJldmVudERlZmF1bHQiLCJlcnJvciIsImNvbnNvbGUiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJmb3JtIiwib25TdWJtaXQiLCJsYWJlbCIsImh0bWxGb3IiLCJpbnB1dCIsInR5cGUiLCJpZCIsInJlcXVpcmVkIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsInRleHRhcmVhIiwicm93cyIsInNlbGVjdCIsIm9wdGlvbiIsImRpc2FibGVkIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/AddTaskModal.js\n");

/***/ }),

/***/ "./components/Auth/AuthPage.js":
/*!*************************************!*\
  !*** ./components/Auth/AuthPage.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoginForm */ \"./components/Auth/LoginForm.js\");\n/* harmony import */ var _SignupForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SignupForm */ \"./components/Auth/SignupForm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_LoginForm__WEBPACK_IMPORTED_MODULE_2__, _SignupForm__WEBPACK_IMPORTED_MODULE_3__]);\n([_LoginForm__WEBPACK_IMPORTED_MODULE_2__, _SignupForm__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction AuthPage() {\n    const [isLogin, setIsLogin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const toggleMode = ()=>{\n        setIsLogin(!isLogin);\n    };\n    return isLogin ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoginForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        onToggleMode: toggleMode\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\AuthPage.js\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SignupForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        onToggleMode: toggleMode\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\AuthPage.js\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0F1dGgvQXV0aFBhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBaUM7QUFDRztBQUNFO0FBRXZCLFNBQVNHO0lBQ3RCLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHTCwrQ0FBUUEsQ0FBQztJQUV2QyxNQUFNTSxhQUFhO1FBQ2pCRCxXQUFXLENBQUNEO0lBQ2Q7SUFFQSxPQUFPQSx3QkFDTCw4REFBQ0gsa0RBQVNBO1FBQUNNLGNBQWNEOzs7Ozs2QkFFekIsOERBQUNKLG1EQUFVQTtRQUFDSyxjQUFjRDs7Ozs7O0FBRTlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFzay1tYW5hZ2VtZW50LWFwcC8uL2NvbXBvbmVudHMvQXV0aC9BdXRoUGFnZS5qcz80YTA0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IExvZ2luRm9ybSBmcm9tICcuL0xvZ2luRm9ybSc7XG5pbXBvcnQgU2lnbnVwRm9ybSBmcm9tICcuL1NpZ251cEZvcm0nO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBdXRoUGFnZSgpIHtcbiAgY29uc3QgW2lzTG9naW4sIHNldElzTG9naW5dID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgY29uc3QgdG9nZ2xlTW9kZSA9ICgpID0+IHtcbiAgICBzZXRJc0xvZ2luKCFpc0xvZ2luKTtcbiAgfTtcblxuICByZXR1cm4gaXNMb2dpbiA/IChcbiAgICA8TG9naW5Gb3JtIG9uVG9nZ2xlTW9kZT17dG9nZ2xlTW9kZX0gLz5cbiAgKSA6IChcbiAgICA8U2lnbnVwRm9ybSBvblRvZ2dsZU1vZGU9e3RvZ2dsZU1vZGV9IC8+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJMb2dpbkZvcm0iLCJTaWdudXBGb3JtIiwiQXV0aFBhZ2UiLCJpc0xvZ2luIiwic2V0SXNMb2dpbiIsInRvZ2dsZU1vZGUiLCJvblRvZ2dsZU1vZGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Auth/AuthPage.js\n");

/***/ }),

/***/ "./components/Auth/LoginForm.js":
/*!**************************************!*\
  !*** ./components/Auth/LoginForm.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,LogIn!=!lucide-react */ \"__barrel_optimize__?names=Eye,EyeOff,LogIn!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction LoginForm({ onToggleMode }) {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        try {\n            await login(email, password);\n        } catch (error) {\n            setError(\"Failed to log in. Please check your credentials.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-bold text-gray-900\",\n                            children: \"Welcome back\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6 bg-white p-8 rounded-xl shadow-lg\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    required: true,\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    placeholder: \"Enter your password\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_3__.EyeOff, {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Eye, {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_LogIn_lucide_react__WEBPACK_IMPORTED_MODULE_3__.LogIn, {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Sign In\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Don't have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onToggleMode,\n                                        className: \"font-medium text-blue-600 hover:text-blue-500\",\n                                        children: \"Sign up\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\LoginForm.js\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Auth/LoginForm.js\n");

/***/ }),

/***/ "./components/Auth/SignupForm.js":
/*!***************************************!*\
  !*** ./components/Auth/SignupForm.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignupForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,UserPlus!=!lucide-react */ \"__barrel_optimize__?names=Eye,EyeOff,UserPlus!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction SignupForm({ onToggleMode }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { signup } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        if (formData.password !== formData.confirmPassword) {\n            setError(\"Passwords do not match\");\n            return;\n        }\n        if (formData.password.length < 6) {\n            setError(\"Password must be at least 6 characters\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await signup(formData.email, formData.password, formData.displayName);\n        } catch (error) {\n            setError(\"Failed to create account. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-bold text-gray-900\",\n                            children: \"Create account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"Join us to manage your tasks\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6 bg-white p-8 rounded-xl shadow-lg\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"displayName\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"displayName\",\n                                            name: \"displayName\",\n                                            type: \"text\",\n                                            required: true,\n                                            value: formData.displayName,\n                                            onChange: handleChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                            placeholder: \"Enter your full name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            required: true,\n                                            value: formData.email,\n                                            onChange: handleChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    required: true,\n                                                    value: formData.password,\n                                                    onChange: handleChange,\n                                                    className: \"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                                    placeholder: \"Enter your password\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__.EyeOff, {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Eye, {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"confirmPassword\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: \"password\",\n                                            required: true,\n                                            value: formData.confirmPassword,\n                                            onChange: handleChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                            placeholder: \"Confirm your password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__.UserPlus, {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Create Account\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Already have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onToggleMode,\n                                        className: \"font-medium text-green-600 hover:text-green-500\",\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\SignupForm.js\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Auth/SignupForm.js\n");

/***/ }),

/***/ "./components/EditTaskModal.js":
/*!*************************************!*\
  !*** ./components/EditTaskModal.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditTaskModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _barrel_optimize_names_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Save,X!=!lucide-react */ \"__barrel_optimize__?names=Save,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_store__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_store__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction EditTaskModal({ task, isOpen, onClose }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: task.title || \"\",\n        description: task.description || \"\",\n        status: task.status || \"todo\",\n        priority: task.priority || \"medium\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { updateTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useTaskStore)();\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            await updateTask(task.id, formData);\n            onClose();\n        } catch (error) {\n            console.error(\"Failed to update task:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Edit Task\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"title\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Task Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"title\",\n                                    name: \"title\",\n                                    required: true,\n                                    value: formData.title,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    placeholder: \"Enter task title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"description\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    id: \"description\",\n                                    name: \"description\",\n                                    rows: 3,\n                                    value: formData.description,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    placeholder: \"Enter task description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"status\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"status\",\n                                    name: \"status\",\n                                    value: formData.status,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"todo\",\n                                            children: \"To Do\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inprogress\",\n                                            children: \"In Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"done\",\n                                            children: \"Done\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"priority\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Priority\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"priority\",\n                                    name: \"priority\",\n                                    value: formData.priority,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"low\",\n                                            children: \"Low\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"medium\",\n                                            children: \"Medium\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"high\",\n                                            children: \"High\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Save, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Save Changes\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\EditTaskModal.js\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/EditTaskModal.js\n");

/***/ }),

/***/ "./components/KanbanBoard.js":
/*!***********************************!*\
  !*** ./components/KanbanBoard.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KanbanBoard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/core */ \"@dnd-kit/core\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/sortable */ \"@dnd-kit/sortable\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _KanbanColumn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./KanbanColumn */ \"./components/KanbanColumn.js\");\n/* harmony import */ var _TaskCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TaskCard */ \"./components/TaskCard.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_store__WEBPACK_IMPORTED_MODULE_4__, _KanbanColumn__WEBPACK_IMPORTED_MODULE_5__, _TaskCard__WEBPACK_IMPORTED_MODULE_6__]);\n([_lib_store__WEBPACK_IMPORTED_MODULE_4__, _KanbanColumn__WEBPACK_IMPORTED_MODULE_5__, _TaskCard__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst columns = [\n    {\n        id: \"todo\",\n        title: \"To Do\",\n        color: \"bg-gray-50 border-gray-200\"\n    },\n    {\n        id: \"inprogress\",\n        title: \"In Progress\",\n        color: \"bg-blue-50 border-blue-200\"\n    },\n    {\n        id: \"done\",\n        title: \"Done\",\n        color: \"bg-green-50 border-green-200\"\n    }\n];\nfunction KanbanBoard() {\n    const { tasks, moveTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    const [activeId, setActiveId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.PointerSensor), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.KeyboardSensor, {\n        coordinateGetter: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.sortableKeyboardCoordinates\n    }));\n    const getTasksByStatus = (status)=>{\n        return tasks.filter((task)=>task.status === status);\n    };\n    const findContainer = (id)=>{\n        if (id in tasks) {\n            return id;\n        }\n        const task = tasks.find((task)=>task.id === id);\n        return task ? task.status : null;\n    };\n    const handleDragStart = (event)=>{\n        const { active } = event;\n        setActiveId(active.id);\n    };\n    const handleDragOver = (event)=>{\n        const { active, over } = event;\n        const overId = over?.id;\n        if (!overId) return;\n        const activeContainer = findContainer(active.id);\n        const overContainer = findContainer(overId);\n        if (!activeContainer || !overContainer || activeContainer === overContainer) {\n            return;\n        }\n    };\n    const handleDragEnd = async (event)=>{\n        const { active, over } = event;\n        if (!over) {\n            setActiveId(null);\n            return;\n        }\n        const activeId = active.id;\n        const overId = over.id;\n        const activeTask = tasks.find((task)=>task.id === activeId);\n        if (!activeTask) {\n            setActiveId(null);\n            return;\n        }\n        let newStatus = activeTask.status;\n        if (columns.some((col)=>col.id === overId)) {\n            newStatus = overId;\n        } else {\n            const overTask = tasks.find((task)=>task.id === overId);\n            if (overTask) {\n                newStatus = overTask.status;\n            }\n        }\n        if (newStatus !== activeTask.status) {\n            try {\n                await moveTask(activeId, newStatus);\n            } catch (error) {\n                console.error(\"Failed to move task:\", error);\n            }\n        }\n        setActiveId(null);\n    };\n    const activeTask = activeId ? tasks.find((task)=>task.id === activeId) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Task Board\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Drag and drop tasks between columns\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DndContext, {\n                sensors: sensors,\n                collisionDetection: _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.closestCorners,\n                onDragStart: handleDragStart,\n                onDragOver: handleDragOver,\n                onDragEnd: handleDragEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 h-full\",\n                        children: columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.SortableContext, {\n                                items: getTasksByStatus(column.id).map((task)=>task.id),\n                                strategy: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.verticalListSortingStrategy,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KanbanColumn__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    id: column.id,\n                                    title: column.title,\n                                    color: column.color,\n                                    tasks: getTasksByStatus(column.id)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this)\n                            }, column.id, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DragOverlay, {\n                        children: activeTask ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            task: activeTask,\n                            isDragging: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 143,\n                            columnNumber: 25\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/KanbanBoard.js\n");

/***/ }),

/***/ "./components/KanbanColumn.js":
/*!************************************!*\
  !*** ./components/KanbanColumn.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KanbanColumn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @dnd-kit/core */ \"@dnd-kit/core\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/sortable */ \"@dnd-kit/sortable\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _TaskCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TaskCard */ \"./components/TaskCard.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_TaskCard__WEBPACK_IMPORTED_MODULE_3__]);\n_TaskCard__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction KanbanColumn({ id, title, color, tasks }) {\n    const { setNodeRef } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDroppable)({\n        id: id\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col h-full rounded-lg border-2 border-dashed ${color} p-4`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full\",\n                        children: tasks.length\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: setNodeRef,\n                className: \"flex-1 space-y-3 min-h-[200px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__.SortableContext, {\n                        items: tasks.map((task)=>task.id),\n                        strategy: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__.verticalListSortingStrategy,\n                        children: tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                task: task\n                            }, task.id, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    tasks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-32 text-gray-400 text-sm\",\n                        children: \"Drop tasks here\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/KanbanColumn.js\n");

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_LogOut_MessageCircle_Plus_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,LogOut,MessageCircle,Plus,User!=!lucide-react */ \"__barrel_optimize__?names=CheckSquare,LogOut,MessageCircle,Plus,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _AddTaskModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AddTaskModal */ \"./components/AddTaskModal.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _lib_store__WEBPACK_IMPORTED_MODULE_3__, _AddTaskModal__WEBPACK_IMPORTED_MODULE_4__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _lib_store__WEBPACK_IMPORTED_MODULE_3__, _AddTaskModal__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction Sidebar({ onToggleAI }) {\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { getTaskCount } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useTaskStore)();\n    const [showAddTask, setShowAddTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const taskCount = getTaskCount();\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n        } catch (error) {\n            console.error(\"Failed to log out:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-white border-r border-gray-200 h-full flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_LogOut_MessageCircle_Plus_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User, {\n                                        className: \"h-5 w-5 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: user?.displayName || \"User\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: user?.email\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Tasks Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_LogOut_MessageCircle_Plus_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__.CheckSquare, {\n                                                className: \"h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 42,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: taskCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 43,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddTask(true),\n                                        className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_LogOut_MessageCircle_Plus_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Plus, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 52,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Task\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 53,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onToggleAI,\n                                        className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_LogOut_MessageCircle_Plus_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__.MessageCircle, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 60,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Talk to Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 61,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_LogOut_MessageCircle_Plus_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__.LogOut, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            showAddTask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddTaskModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showAddTask,\n                onClose: ()=>setShowAddTask(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n");

/***/ }),

/***/ "./components/TaskCard.js":
/*!********************************!*\
  !*** ./components/TaskCard.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TaskCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/sortable */ \"@dnd-kit/sortable\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/utilities */ \"@dnd-kit/utilities\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Edit,MoreVertical,Trash2!=!lucide-react */ \"__barrel_optimize__?names=Calendar,Edit,MoreVertical,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/utils */ \"./lib/utils.js\");\n/* harmony import */ var _EditTaskModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./EditTaskModal */ \"./components/EditTaskModal.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_store__WEBPACK_IMPORTED_MODULE_4__, _lib_utils__WEBPACK_IMPORTED_MODULE_5__, _EditTaskModal__WEBPACK_IMPORTED_MODULE_6__]);\n([_lib_store__WEBPACK_IMPORTED_MODULE_4__, _lib_utils__WEBPACK_IMPORTED_MODULE_5__, _EditTaskModal__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction TaskCard({ task, isDragging = false }) {\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEdit, setShowEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { deleteTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    const { attributes, listeners, setNodeRef, transform, transition, isDragging: isSortableDragging } = (0,_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__.useSortable)({\n        id: task.id\n    });\n    const style = {\n        transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_3__.CSS.Transform.toString(transform),\n        transition,\n        opacity: isDragging || isSortableDragging ? 0.5 : 1\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"bg-red-100 text-red-800 border-red-200\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n            case \"low\":\n                return \"bg-green-100 text-green-800 border-green-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    const handleDelete = async ()=>{\n        if (window.confirm(\"Are you sure you want to delete this task?\")) {\n            try {\n                await deleteTask(task.id);\n            } catch (error) {\n                console.error(\"Failed to delete task:\", error);\n            }\n        }\n        setShowMenu(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: setNodeRef,\n                style: style,\n                ...attributes,\n                ...listeners,\n                className: \"bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow cursor-grab active:cursor-grabbing relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900 text-sm leading-tight flex-1 pr-2\",\n                                children: task.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            setShowMenu(!showMenu);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600 p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__.MoreVertical, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setShowEdit(true);\n                                                    setShowMenu(false);\n                                                },\n                                                className: \"flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Edit, {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Edit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDelete();\n                                                },\n                                                className: \"flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Trash2, {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Delete\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-xs mb-3 line-clamp-2\",\n                        children: task.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`,\n                                children: task.priority\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            task.createdAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 text-xs text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calendar, {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(task.createdAt.toDate?.() || task.createdAt)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            showEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditTaskModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                task: task,\n                isOpen: showEdit,\n                onClose: ()=>setShowEdit(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TaskCard.js\n");

/***/ }),

/***/ "./components/TodoList.js":
/*!********************************!*\
  !*** ./components/TodoList.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TodoList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _barrel_optimize_names_Check_GripVertical_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,GripVertical,Plus,X!=!lucide-react */ \"__barrel_optimize__?names=Check,GripVertical,Plus,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/core */ \"@dnd-kit/core\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @dnd-kit/sortable */ \"@dnd-kit/sortable\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @dnd-kit/utilities */ \"@dnd-kit/utilities\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_store__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_store__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\nfunction TodoItem({ task, onToggleComplete, onDelete }) {\n    const { attributes, listeners, setNodeRef, transform, transition } = (0,_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_4__.useSortable)({\n        id: task.id\n    });\n    const style = {\n        transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_5__.CSS.Transform.toString(transform),\n        transition\n    };\n    const isCompleted = task.status === \"done\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        style: style,\n        className: \"flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:shadow-sm transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ...attributes,\n                ...listeners,\n                className: \"cursor-grab active:cursor-grabbing\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_GripVertical_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.GripVertical, {\n                    className: \"h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onToggleComplete(task),\n                className: `flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${isCompleted ? \"bg-green-500 border-green-500 text-white\" : \"border-gray-300 hover:border-green-400\"}`,\n                children: isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_GripVertical_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Check, {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                    lineNumber: 57,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: `text-sm ${isCompleted ? \"line-through text-gray-500\" : \"text-gray-900\"}`,\n                        children: task.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: `text-xs mt-1 ${isCompleted ? \"text-gray-400\" : \"text-gray-600\"}`,\n                        children: task.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onDelete(task),\n                className: \"flex-shrink-0 text-gray-400 hover:text-red-500 transition-colors\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_GripVertical_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.X, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\nfunction TodoList() {\n    const { tasks, addTask, updateTask, deleteTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useTaskStore)();\n    const [newTaskTitle, setNewTaskTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_3__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_3__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_3__.PointerSensor), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_3__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_3__.KeyboardSensor, {\n        coordinateGetter: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_4__.sortableKeyboardCoordinates\n    }));\n    const todoTasks = tasks.filter((task)=>task.status !== \"inprogress\");\n    const handleAddTask = async (e)=>{\n        e.preventDefault();\n        if (!newTaskTitle.trim()) return;\n        setLoading(true);\n        try {\n            await addTask({\n                title: newTaskTitle.trim(),\n                description: \"\",\n                status: \"todo\",\n                priority: \"medium\"\n            });\n            setNewTaskTitle(\"\");\n        } catch (error) {\n            console.error(\"Failed to add task:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleToggleComplete = async (task)=>{\n        try {\n            const newStatus = task.status === \"done\" ? \"todo\" : \"done\";\n            await updateTask(task.id, {\n                status: newStatus\n            });\n        } catch (error) {\n            console.error(\"Failed to update task:\", error);\n        }\n    };\n    const handleDelete = async (task)=>{\n        if (window.confirm(\"Are you sure you want to delete this task?\")) {\n            try {\n                await deleteTask(task.id);\n            } catch (error) {\n                console.error(\"Failed to delete task:\", error);\n            }\n        }\n    };\n    const handleDragEnd = (event)=>{\n        const { active, over } = event;\n        if (active.id !== over?.id) {\n            const oldIndex = todoTasks.findIndex((task)=>task.id === active.id);\n            const newIndex = todoTasks.findIndex((task)=>task.id === over.id);\n            const reorderedTasks = (0,_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_4__.arrayMove)(todoTasks, oldIndex, newIndex);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"Todo List\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Simple task management with drag-and-drop reordering\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleAddTask,\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: newTaskTitle,\n                            onChange: (e)=>setNewTaskTitle(e.target.value),\n                            placeholder: \"Add a new task...\",\n                            className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading || !newTaskTitle.trim(),\n                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_GripVertical_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Plus, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Add\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: todoTasks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12 text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No tasks yet. Add one above to get started!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                        lineNumber: 181,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                    lineNumber: 180,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_3__.DndContext, {\n                    sensors: sensors,\n                    collisionDetection: _dnd_kit_core__WEBPACK_IMPORTED_MODULE_3__.closestCenter,\n                    onDragEnd: handleDragEnd,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_4__.SortableContext, {\n                        items: todoTasks.map((task)=>task.id),\n                        strategy: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_4__.verticalListSortingStrategy,\n                        children: todoTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TodoItem, {\n                                task: task,\n                                onToggleComplete: handleToggleComplete,\n                                onDelete: handleDelete\n                            }, task.id, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                                lineNumber: 191,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                        lineNumber: 189,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TodoList.js\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TodoList.js\n");

/***/ }),

/***/ "./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/firebase-config */ \"./lib/firebase-config.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__, _lib_store__WEBPACK_IMPORTED_MODULE_4__]);\n([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__, _lib_store__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { setUser: setStoreUser, loadTasks } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, async (user)=>{\n            setUser(user);\n            setStoreUser(user);\n            if (user) {\n                await loadTasks(user.uid);\n            }\n            setLoading(false);\n        });\n        return unsubscribe;\n    }, [\n        setStoreUser,\n        loadTasks\n    ]);\n    const signup = async (email, password, displayName)=>{\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.updateProfile)(userCredential.user, {\n            displayName\n        });\n        return userCredential;\n    };\n    const login = async (email, password)=>{\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n    };\n    const logout = async ()=>{\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth);\n    };\n    const value = {\n        user,\n        signup,\n        login,\n        logout,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.js\n");

/***/ }),

/***/ "./lib/firebase-config.js":
/*!********************************!*\
  !*** ./lib/firebase-config.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst firebaseConfig = {\n    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n};\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase-config.js\n");

/***/ }),

/***/ "./lib/firebase-utils.js":
/*!*******************************!*\
  !*** ./lib/firebase-utils.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTask: () => (/* binding */ createTask),\n/* harmony export */   deleteTask: () => (/* binding */ deleteTask),\n/* harmony export */   getUserTasks: () => (/* binding */ getUserTasks),\n/* harmony export */   updateTask: () => (/* binding */ updateTask)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase-config */ \"./lib/firebase-config.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase_config__WEBPACK_IMPORTED_MODULE_1__]);\n([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase_config__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst createTask = async (userId, taskData)=>{\n    try {\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\"), {\n            ...taskData,\n            userId,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n        return {\n            id: docRef.id,\n            ...taskData\n        };\n    } catch (error) {\n        console.error(\"Error creating task:\", error);\n        throw error;\n    }\n};\nconst updateTask = async (taskId, updates)=>{\n    try {\n        const taskRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\", taskId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(taskRef, {\n            ...updates,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n        return {\n            id: taskId,\n            ...updates\n        };\n    } catch (error) {\n        console.error(\"Error updating task:\", error);\n        throw error;\n    }\n};\nconst deleteTask = async (taskId)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\", taskId));\n        return taskId;\n    } catch (error) {\n        console.error(\"Error deleting task:\", error);\n        throw error;\n    }\n};\nconst getUserTasks = async (userId)=>{\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"userId\", \"==\", userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"createdAt\", \"desc\"));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const tasks = [];\n        querySnapshot.forEach((doc)=>{\n            tasks.push({\n                id: doc.id,\n                ...doc.data()\n            });\n        });\n        return tasks;\n    } catch (error) {\n        console.error(\"Error fetching tasks:\", error);\n        throw error;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase-utils.js\n");

/***/ }),

/***/ "./lib/openrouter-config.js":
/*!**********************************!*\
  !*** ./lib/openrouter-config.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChatCompletion: () => (/* binding */ createChatCompletion),\n/* harmony export */   openRouterConfig: () => (/* binding */ openRouterConfig),\n/* harmony export */   parseTaskCommand: () => (/* binding */ parseTaskCommand)\n/* harmony export */ });\nconst OPENROUTER_API_URL = \"https://openrouter.ai/api/v1/chat/completions\";\nconst openRouterConfig = {\n    apiKey: process.env.OPENROUTER_API_KEY,\n    apiUrl: OPENROUTER_API_URL,\n    model: \"anthropic/claude-3-sonnet\",\n    headers: {\n        \"Authorization\": `Bearer ${process.env.OPENROUTER_API_KEY}`,\n        \"Content-Type\": \"application/json\",\n        \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n        \"X-Title\": \"Task Management App\"\n    }\n};\nconst createChatCompletion = async (messages, systemPrompt = \"\")=>{\n    try {\n        const response = await fetch(OPENROUTER_API_URL, {\n            method: \"POST\",\n            headers: openRouterConfig.headers,\n            body: JSON.stringify({\n                model: openRouterConfig.model,\n                messages: [\n                    ...systemPrompt ? [\n                        {\n                            role: \"system\",\n                            content: systemPrompt\n                        }\n                    ] : [],\n                    ...messages\n                ],\n                temperature: 0.7,\n                max_tokens: 1000\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`OpenRouter API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return data.choices[0].message.content;\n    } catch (error) {\n        console.error(\"Error calling OpenRouter API:\", error);\n        throw error;\n    }\n};\nconst parseTaskCommand = (userInput, currentTasks)=>{\n    const input = userInput.toLowerCase().trim();\n    if (input.includes(\"create\") && input.includes(\"task\")) {\n        const taskNameMatch = input.match(/create.*task.*called\\s+[\"']?([^\"']+)[\"']?/i) || input.match(/create.*[\"']?([^\"']+)[\"']?.*task/i);\n        if (taskNameMatch) {\n            return {\n                action: \"create\",\n                taskName: taskNameMatch[1].trim(),\n                column: \"todo\"\n            };\n        }\n    }\n    if (input.includes(\"move\") || input.includes(\"change\")) {\n        const taskMatch = input.match(/task\\s+[\"']?([^\"']+)[\"']?/i);\n        const columnMatch = input.match(/(to do|todo|in progress|progress|done|completed)/i);\n        if (taskMatch && columnMatch) {\n            const columnMap = {\n                \"to do\": \"todo\",\n                \"todo\": \"todo\",\n                \"in progress\": \"inprogress\",\n                \"progress\": \"inprogress\",\n                \"done\": \"done\",\n                \"completed\": \"done\"\n            };\n            return {\n                action: \"move\",\n                taskName: taskMatch[1].trim(),\n                column: columnMap[columnMatch[1].toLowerCase()]\n            };\n        }\n    }\n    if (input.includes(\"delete\") || input.includes(\"remove\")) {\n        const taskMatch = input.match(/(?:delete|remove).*task.*[\"']?([^\"']+)[\"']?/i) || input.match(/(?:delete|remove).*[\"']?([^\"']+)[\"']?.*task/i);\n        if (taskMatch) {\n            return {\n                action: \"delete\",\n                taskName: taskMatch[1].trim()\n            };\n        }\n    }\n    return {\n        action: \"chat\",\n        message: userInput\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/openrouter-config.js\n");

/***/ }),

/***/ "./lib/store.js":
/*!**********************!*\
  !*** ./lib/store.js ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTaskStore: () => (/* binding */ useTaskStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\n/* harmony import */ var _firebase_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase-utils */ \"./lib/firebase-utils.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__, _firebase_utils__WEBPACK_IMPORTED_MODULE_1__]);\n([zustand__WEBPACK_IMPORTED_MODULE_0__, _firebase_utils__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst useTaskStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        tasks: [],\n        user: null,\n        loading: false,\n        error: null,\n        setUser: (user)=>set({\n                user\n            }),\n        setLoading: (loading)=>set({\n                loading\n            }),\n        setError: (error)=>set({\n                error\n            }),\n        loadTasks: async (userId)=>{\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                const tasks = await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.getUserTasks)(userId);\n                set({\n                    tasks,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.message,\n                    loading: false\n                });\n            }\n        },\n        addTask: async (taskData)=>{\n            const { user } = get();\n            if (!user) return;\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                const newTask = await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.createTask)(user.uid, taskData);\n                set((state)=>({\n                        tasks: [\n                            newTask,\n                            ...state.tasks\n                        ],\n                        loading: false\n                    }));\n                return newTask;\n            } catch (error) {\n                set({\n                    error: error.message,\n                    loading: false\n                });\n                throw error;\n            }\n        },\n        updateTask: async (taskId, updates)=>{\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.updateTask)(taskId, updates);\n                set((state)=>({\n                        tasks: state.tasks.map((task)=>task.id === taskId ? {\n                                ...task,\n                                ...updates\n                            } : task),\n                        loading: false\n                    }));\n            } catch (error) {\n                set({\n                    error: error.message,\n                    loading: false\n                });\n                throw error;\n            }\n        },\n        deleteTask: async (taskId)=>{\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.deleteTask)(taskId);\n                set((state)=>({\n                        tasks: state.tasks.filter((task)=>task.id !== taskId),\n                        loading: false\n                    }));\n            } catch (error) {\n                set({\n                    error: error.message,\n                    loading: false\n                });\n                throw error;\n            }\n        },\n        moveTask: async (taskId, newColumn)=>{\n            const { updateTask } = get();\n            await updateTask(taskId, {\n                status: newColumn\n            });\n        },\n        getTasksByStatus: (status)=>{\n            const { tasks } = get();\n            return tasks.filter((task)=>task.status === status);\n        },\n        getTaskCount: ()=>{\n            const { tasks } = get();\n            return tasks.length;\n        },\n        findTaskByName: (name)=>{\n            const { tasks } = get();\n            return tasks.find((task)=>task.title.toLowerCase().includes(name.toLowerCase()));\n        }\n    }));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/store.js\n");

/***/ }),

/***/ "./lib/utils.js":
/*!**********************!*\
  !*** ./lib/utils.js ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getStatusLabel: () => (/* binding */ getStatusLabel)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nconst generateId = ()=>{\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n};\nconst formatDate = (date)=>{\n    if (!date) return \"\";\n    const d = new Date(date);\n    return d.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\"\n    });\n};\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"todo\":\n            return \"bg-gray-100 text-gray-800 border-gray-200\";\n        case \"inprogress\":\n            return \"bg-blue-100 text-blue-800 border-blue-200\";\n        case \"done\":\n            return \"bg-green-100 text-green-800 border-green-200\";\n        default:\n            return \"bg-gray-100 text-gray-800 border-gray-200\";\n    }\n};\nconst getStatusLabel = (status)=>{\n    switch(status){\n        case \"todo\":\n            return \"To Do\";\n        case \"inprogress\":\n            return \"In Progress\";\n        case \"done\":\n            return \"Done\";\n        default:\n            return \"Unknown\";\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/utils.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBK0I7QUFDd0I7QUFFeEMsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFDRSw4REFBQ0gsK0RBQVlBO2tCQUNYLDRFQUFDRTtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBRzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFzay1tYW5hZ2VtZW50LWFwcC8uL3BhZ2VzL19hcHAuanM/ZTBhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _components_Auth_AuthPage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Auth/AuthPage */ \"./components/Auth/AuthPage.js\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var _components_KanbanBoard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/KanbanBoard */ \"./components/KanbanBoard.js\");\n/* harmony import */ var _components_TodoList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/TodoList */ \"./components/TodoList.js\");\n/* harmony import */ var _components_AIAssistant__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/AIAssistant */ \"./components/AIAssistant.js\");\n/* harmony import */ var _barrel_optimize_names_Kanban_List_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Kanban,List!=!lucide-react */ \"__barrel_optimize__?names=Kanban,List!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _components_Auth_AuthPage__WEBPACK_IMPORTED_MODULE_3__, _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__, _components_KanbanBoard__WEBPACK_IMPORTED_MODULE_5__, _components_TodoList__WEBPACK_IMPORTED_MODULE_6__, _components_AIAssistant__WEBPACK_IMPORTED_MODULE_7__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _components_Auth_AuthPage__WEBPACK_IMPORTED_MODULE_3__, _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__, _components_KanbanBoard__WEBPACK_IMPORTED_MODULE_5__, _components_TodoList__WEBPACK_IMPORTED_MODULE_6__, _components_AIAssistant__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"kanban\");\n    const [showAI, setShowAI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_AuthPage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n            lineNumber: 27,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onToggleAI: ()=>setShowAI(!showAI)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Task Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveView(\"kanban\"),\n                                            className: `flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${activeView === \"kanban\" ? \"bg-blue-100 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-100\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Kanban_List_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Kanban, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Kanban\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveView(\"todo\"),\n                                            className: `flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${activeView === \"todo\" ? \"bg-blue-100 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-100\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Kanban_List_lucide_react__WEBPACK_IMPORTED_MODULE_8__.List, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Todo List\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: activeView === \"kanban\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_KanbanBoard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                            lineNumber: 68,\n                            columnNumber: 38\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TodoList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                            lineNumber: 68,\n                            columnNumber: 56\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIAssistant__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: showAI,\n                onClose: ()=>setShowAI(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\index.js\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@dnd-kit/core":
/*!********************************!*\
  !*** external "@dnd-kit/core" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@dnd-kit/core");

/***/ }),

/***/ "@dnd-kit/sortable":
/*!************************************!*\
  !*** external "@dnd-kit/sortable" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@dnd-kit/sortable");

/***/ }),

/***/ "@dnd-kit/utilities":
/*!*************************************!*\
  !*** external "@dnd-kit/utilities" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@dnd-kit/utilities");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "firebase/app":
/*!*******************************!*\
  !*** external "firebase/app" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ "firebase/auth":
/*!********************************!*\
  !*** external "firebase/auth" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ "firebase/firestore":
/*!*************************************!*\
  !*** external "firebase/firestore" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();