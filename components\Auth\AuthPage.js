import { useState } from 'react';
import LoginForm from './LoginForm';
import SignupForm from './SignupForm';
import FirebaseSetupCheck from '../FirebaseSetupCheck';

export default function AuthPage() {
  const [isLogin, setIsLogin] = useState(true);

  const toggleMode = () => {
    setIsLogin(!isLogin);
  };

  return (
    <div>
      <div className="fixed top-4 left-4 right-4 z-50">
        <FirebaseSetupCheck />
      </div>
      {isLogin ? (
        <LoginForm onToggleMode={toggleMode} />
      ) : (
        <SignupForm onToggleMode={toggleMode} />
      )}
    </div>
  );
}
