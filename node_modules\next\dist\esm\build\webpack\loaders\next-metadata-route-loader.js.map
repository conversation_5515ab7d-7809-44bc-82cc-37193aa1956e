{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-route-loader.ts"], "names": ["fs", "path", "imageExtMimeTypeMap", "cacheHeader", "none", "longCache", "revalidate", "getFilenameAndExtension", "resourcePath", "filename", "basename", "name", "ext", "split", "getContentType", "getStaticAssetRouteCode", "fileBaseName", "cache", "process", "env", "NODE_ENV", "code", "JSON", "stringify", "promises", "readFile", "toString", "getDynamicTextRouteCode", "getDynamicImageRouteCode", "getDynamicSiteMapRouteCode", "page", "staticGenerationCode", "includes", "nextMetadataRouterLoader", "isDynamic", "getOptions"], "mappings": "AACA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,SAASC,mBAAmB,QAAQ,yBAAwB;AAE5D,MAAMC,cAAc;IAClBC,MAAM;IACNC,WAAW;IACXC,YAAY;AACd;AAOA,OAAO,SAASC,wBAAwBC,YAAoB;IAC1D,MAAMC,WAAWR,KAAKS,QAAQ,CAACF;IAC/B,MAAM,CAACG,MAAMC,IAAI,GAAGH,SAASI,KAAK,CAAC,KAAK;IACxC,OAAO;QAAEF;QAAMC;IAAI;AACrB;AAEA,SAASE,eAAeN,YAAoB;IAC1C,IAAI,EAAEG,IAAI,EAAEC,GAAG,EAAE,GAAGL,wBAAwBC;IAC5C,IAAII,QAAQ,OAAOA,MAAM;IAEzB,IAAID,SAAS,aAAaC,QAAQ,OAAO,OAAO;IAChD,IAAID,SAAS,WAAW,OAAO;IAC/B,IAAIA,SAAS,UAAU,OAAO;IAC9B,IAAIA,SAAS,YAAY,OAAO;IAEhC,IAAIC,QAAQ,SAASA,QAAQ,UAAUA,QAAQ,SAASA,QAAQ,OAAO;QACrE,OAAOV,mBAAmB,CAACU,IAAI;IACjC;IACA,OAAO;AACT;AAEA,mHAAmH;AACnH,eAAeG,wBACbP,YAAoB,EACpBQ,YAAoB;IAEpB,MAAMC,QACJD,iBAAiB,YACb,uCACAE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBjB,YAAYC,IAAI,GAChBD,YAAYE,SAAS;IAC3B,MAAMgB,OAAO,CAAC;;;oBAGI,EAAEC,KAAKC,SAAS,CAACT,eAAeN,eAAe;2BACxC,EAAEc,KAAKC,SAAS,CACvC,AAAC,CAAA,MAAMvB,GAAGwB,QAAQ,CAACC,QAAQ,CAACjB,aAAY,EAAGkB,QAAQ,CAAC,WACpD;;;;;;;uBAOmB,EAAEJ,KAAKC,SAAS,CAACN,OAAO;;;;;;AAM/C,CAAC;IACC,OAAOI;AACT;AAEA,SAASM,wBAAwBnB,YAAoB;IACnD,OAAO,CAAC;;oBAEU,EAAEc,KAAKC,SAAS,CAACf,cAAc;;;oBAG/B,EAAEc,KAAKC,SAAS,CAACT,eAAeN,eAAe;iBAClD,EAAEc,KAAKC,SAAS,CAAChB,wBAAwBC,cAAcG,IAAI,EAAE;;;;;;;;;uBASvD,EAAEW,KAAKC,SAAS,CAACpB,YAAYG,UAAU,EAAE;;;;AAIhE,CAAC;AACD;AAEA,iCAAiC;AACjC,SAASsB,yBAAyBpB,YAAoB;IACpD,OAAO,CAAC;;0BAEgB,EAAEc,KAAKC,SAAS,CAACf,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BzD,CAAC;AACD;AAEA,SAASqB,2BAA2BrB,YAAoB,EAAEsB,IAAY;IACpE,IAAIC,uBAAuB;IAE3B,IACEb,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBU,KAAKE,QAAQ,CAAC,sBACd;QACAD,uBAAuB,CAAC;;;;;;;;;;IAUxB,CAAC;IACH;IAEA,MAAMV,OAAO,CAAC;;0BAEU,EAAEC,KAAKC,SAAS,CAACf,cAAc;;;;;;oBAMrC,EAAEc,KAAKC,SAAS,CAACT,eAAeN,eAAe;iBAClD,EAAEc,KAAKC,SAAS,CAAChB,wBAAwBC,cAAcG,IAAI,EAAE;;AAE9E,EAAE,GAAG,wCAAwC,IAAG;cAClC,EAAEW,KAAKC,SAAS,CAACf,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA8BtB,EAAEc,KAAKC,SAAS,CAACpB,YAAYG,UAAU,EAAE;;;;;AAKhE,EAAEyB,qBAAqB;AACvB,CAAC;IACC,OAAOV;AACT;AACA,gEAAgE;AAChE,gFAAgF;AAChF,oDAAoD;AACpD,MAAMY,2BACJ;IACE,MAAM,EAAEzB,YAAY,EAAE,GAAG,IAAI;IAC7B,MAAM,EAAEsB,IAAI,EAAEI,SAAS,EAAE,GAAG,IAAI,CAACC,UAAU;IAC3C,MAAM,EAAExB,MAAMK,YAAY,EAAE,GAAGT,wBAAwBC;IAEvD,IAAIa,OAAO;IACX,IAAIa,cAAc,KAAK;QACrB,IAAIlB,iBAAiB,YAAYA,iBAAiB,YAAY;YAC5DK,OAAOM,wBAAwBnB;QACjC,OAAO,IAAIQ,iBAAiB,WAAW;YACrCK,OAAOQ,2BAA2BrB,cAAcsB;QAClD,OAAO;YACLT,OAAOO,yBAAyBpB;QAClC;IACF,OAAO;QACLa,OAAO,MAAMN,wBAAwBP,cAAcQ;IACrD;IAEA,OAAOK;AACT;AAEF,eAAeY,yBAAwB"}