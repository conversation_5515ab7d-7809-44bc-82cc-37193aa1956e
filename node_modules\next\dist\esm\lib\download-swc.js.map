{"version": 3, "sources": ["../../src/lib/download-swc.ts"], "names": ["fs", "path", "Log", "tar", "WritableStream", "require", "getRegistry", "getCacheDirectory", "MAX_VERSIONS_TO_CACHE", "extractBinary", "outputDirectory", "pkgName", "tarFileName", "cacheDirectory", "process", "env", "extractFromTar", "x", "file", "join", "cwd", "strip", "existsSync", "info", "promises", "mkdir", "recursive", "tempFile", "Date", "now", "registry", "downloadUrl", "fetch", "then", "res", "ok", "body", "error", "Error", "status", "cacheWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "close", "rename", "cacheFiles", "readdir", "length", "sort", "a", "b", "localeCompare", "i", "unlink", "catch", "downloadNativeNextSwc", "version", "bindingsDirectory", "triplesABI", "triple", "substring", "downloadWasmSwc", "wasmDirectory", "variant"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,YAAYC,SAAS,sBAAqB;AAC1C,OAAOC,SAAS,yBAAwB;AACxC,MAAM,EAAEC,cAAc,EAAE,GAAGC,QAAQ;AAGnC,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,iBAAiB,QAAQ,gCAA+B;AAEjE,MAAMC,wBAAwB;AAE9B,eAAeC,cACbC,eAAuB,EACvBC,OAAe,EACfC,WAAmB;IAEnB,MAAMC,iBAAiBN,kBACrB,YACAO,QAAQC,GAAG,CAAC,gBAAgB;IAG9B,MAAMC,iBAAiB,IACrBb,IAAIc,CAAC,CAAC;YACJC,MAAMjB,KAAKkB,IAAI,CAACN,gBAAgBD;YAChCQ,KAAKV;YACLW,OAAO;QACT;IAEF,IAAI,CAACrB,GAAGsB,UAAU,CAACrB,KAAKkB,IAAI,CAACN,gBAAgBD,eAAe;QAC1DV,IAAIqB,IAAI,CAAC,CAAC,wBAAwB,EAAEZ,QAAQ,GAAG,CAAC;QAChD,MAAMX,GAAGwB,QAAQ,CAACC,KAAK,CAACZ,gBAAgB;YAAEa,WAAW;QAAK;QAC1D,MAAMC,WAAW1B,KAAKkB,IAAI,CACxBN,gBACA,CAAC,EAAED,YAAY,MAAM,EAAEgB,KAAKC,GAAG,GAAG,CAAC;QAGrC,MAAMC,WAAWxB;QAEjB,MAAMyB,cAAc,CAAC,EAAED,SAAS,EAAEnB,QAAQ,GAAG,EAAEC,YAAY,CAAC;QAE5D,MAAMoB,MAAMD,aAAaE,IAAI,CAAC,CAACC;YAC7B,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAE,GAAGF;YACrB,IAAI,CAACC,MAAM,CAACC,MAAM;gBAChBlC,IAAImC,KAAK,CAAC,CAAC,oCAAoC,EAAEN,YAAY,CAAC;YAChE;YAEA,IAAI,CAACI,IAAI;gBACP,MAAM,IAAIG,MAAM,CAAC,2BAA2B,EAAEJ,IAAIK,MAAM,CAAC,CAAC;YAC5D;YACA,IAAI,CAACH,MAAM;gBACT,MAAM,IAAIE,MAAM;YAClB;YACA,MAAME,mBAAmBxC,GAAGyC,iBAAiB,CAACd;YAC9C,OAAOS,KAAKM,MAAM,CAChB,IAAItC,eAAe;gBACjBuC,OAAMC,KAAK;oBACTJ,iBAAiBG,KAAK,CAACC;gBACzB;gBACAC;oBACEL,iBAAiBK,KAAK;gBACxB;YACF;QAEJ;QACA,MAAM7C,GAAGwB,QAAQ,CAACsB,MAAM,CAACnB,UAAU1B,KAAKkB,IAAI,CAACN,gBAAgBD;IAC/D;IACA,MAAMI;IAEN,MAAM+B,aAAa,MAAM/C,GAAGwB,QAAQ,CAACwB,OAAO,CAACnC;IAE7C,IAAIkC,WAAWE,MAAM,GAAGzC,uBAAuB;QAC7CuC,WAAWG,IAAI,CAAC,CAACC,GAAGC;YAClB,IAAID,EAAEF,MAAM,GAAGG,EAAEH,MAAM,EAAE,OAAO,CAAC;YACjC,OAAOE,EAAEE,aAAa,CAACD;QACzB;QAEA,iCAAiC;QACjC,IAAK,IAAIE,IAAI,GAAGA,KAAKA,IAAIP,WAAWE,MAAM,GAAGzC,sBAAuB;YAClE,MAAMR,GAAGwB,QAAQ,CACd+B,MAAM,CAACtD,KAAKkB,IAAI,CAACN,gBAAgBkC,UAAU,CAACO,EAAE,GAC9CE,KAAK,CAAC,KAAO;QAClB;IACF;AACF;AAEA,OAAO,eAAeC,sBACpBC,OAAe,EACfC,iBAAyB,EACzBC,UAAyB;IAEzB,KAAK,MAAMC,UAAUD,WAAY;QAC/B,MAAMjD,UAAU,CAAC,UAAU,EAAEkD,OAAO,CAAC;QACrC,MAAMjD,cAAc,CAAC,EAAED,QAAQmD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;QAC5D,MAAMhD,kBAAkBT,KAAKkB,IAAI,CAACwC,mBAAmBhD;QAErD,IAAIX,GAAGsB,UAAU,CAACZ,kBAAkB;YAClC,mDAAmD;YACnD,0CAA0C;YAC1C;QACF;QAEA,MAAMV,GAAGwB,QAAQ,CAACC,KAAK,CAACf,iBAAiB;YAAEgB,WAAW;QAAK;QAC3D,MAAMjB,cAAcC,iBAAiBC,SAASC;IAChD;AACF;AAEA,OAAO,eAAemD,gBACpBL,OAAe,EACfM,aAAqB,EACrBC,UAA4B,QAAQ;IAEpC,MAAMtD,UAAU,CAAC,eAAe,EAAEsD,QAAQ,CAAC;IAC3C,MAAMrD,cAAc,CAAC,EAAED,QAAQmD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;IAC5D,MAAMhD,kBAAkBT,KAAKkB,IAAI,CAAC6C,eAAerD;IAEjD,IAAIX,GAAGsB,UAAU,CAACZ,kBAAkB;QAClC,mDAAmD;QACnD,0CAA0C;QAC1C;IACF;IAEA,MAAMV,GAAGwB,QAAQ,CAACC,KAAK,CAACf,iBAAiB;QAAEgB,WAAW;IAAK;IAC3D,MAAMjB,cAAcC,iBAAiBC,SAASC;AAChD"}