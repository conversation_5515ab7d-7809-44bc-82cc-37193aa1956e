{"version": 3, "sources": ["../../../src/export/routes/app-page.ts"], "names": ["generatePrefetchRsc", "exportAppPage", "ExportedAppPageFiles", "HTML", "FLIGHT", "META", "POSTPONED", "req", "path", "res", "pathname", "htmlFilepath", "renderOpts", "fileWriter", "headers", "RSC", "toLowerCase", "NEXT_URL", "NEXT_ROUTER_PREFETCH", "supportsDynamicHTML", "isPrefetch", "isRevalidate", "prefetchRenderResult", "lazyRenderAppPage", "prefetchRscData", "toUnchunkedString", "store", "staticPrefetchBailout", "replace", "page", "query", "debugOutput", "isDynamicError", "isAppPrefetch", "revalidate", "result", "html", "metadata", "flightData", "pageData", "postponed", "Error", "staticBailoutInfo", "description", "err", "stack", "message", "substring", "indexOf", "console", "warn", "fetchTags", "NEXT_CACHE_TAGS_HEADER", "meta", "status", "undefined", "JSON", "stringify", "hasNextSupport", "hasEmptyPrelude", "Boolean", "hasPostponed", "isDynamicUsageError"], "mappings": ";;;;;;;;;;;;;;;;;;;IA2BsBA,mBAAmB;eAAnBA;;IAoCAC,aAAa;eAAbA;;;kCAjDf;qCAC6B;2BACG;wBACR;8BACG;IAE3B;UAAWC,oBAAoB;IAApBA,qBAChBC,UAAAA;IADgBD,qBAEhBE,YAAAA;IAFgBF,qBAGhBG,UAAAA;IAHgBH,qBAIhBI,eAAAA;GAJgBJ,yBAAAA;AAOX,eAAeF,oBACpBO,GAAkB,EAClBC,IAAY,EACZC,GAAmB,EACnBC,QAAgB,EAChBC,YAAoB,EACpBC,UAAsB,EACtBC,UAAsB;IAEtBN,IAAIO,OAAO,CAACC,qBAAG,CAACC,WAAW,GAAG,GAAG;IACjCT,IAAIO,OAAO,CAACG,0BAAQ,CAACD,WAAW,GAAG,GAAGR;IACtCD,IAAIO,OAAO,CAACI,sCAAoB,CAACF,WAAW,GAAG,GAAG;IAElDJ,WAAWO,mBAAmB,GAAG;IACjCP,WAAWQ,UAAU,GAAG;IACxB,OAAOR,WAAWS,YAAY;IAE9B,MAAMC,uBAAuB,MAAMC,IAAAA,+BAAiB,EAClDhB,KACAE,KACAC,UACA,CAAC,GACDE;IAGF,MAAMY,kBAAkB,MAAMF,qBAAqBG,iBAAiB,CAAC;IAErE,IAAI,AAACb,WAAmBc,KAAK,CAACC,qBAAqB,EAAE;IAErD,MAAMd,WAlCG,UAoCPF,aAAaiB,OAAO,CAAC,WAAW,kBAChCJ;AAEJ;AAEO,eAAevB,cACpBM,GAAkB,EAClBE,GAAmB,EACnBoB,IAAY,EACZrB,IAAY,EACZE,QAAgB,EAChBoB,KAAyB,EACzBlB,UAAsB,EACtBD,YAAoB,EACpBoB,WAAoB,EACpBC,cAAuB,EACvBC,aAAsB,EACtBpB,UAAsB;IAEtB,6EAA6E;IAC7E,IAAIgB,SAAS,eAAe;QAC1BnB,WAAW;IACb;IAEA,IAAI;QACF,IAAIuB,eAAe;YACjB,MAAMjC,oBACJO,KACAC,MACAC,KACAC,UACAC,cACAC,YACAC;YAGF,OAAO;gBAAEqB,YAAY;YAAE;QACzB;QAEA,MAAMC,SAAS,MAAMZ,IAAAA,+BAAiB,EACpChB,KACAE,KACAC,UACAoB,OACAlB;QAEF,MAAMwB,OAAOD,OAAOV,iBAAiB;QACrC,MAAM,EAAEY,QAAQ,EAAE,GAAGF;QACrB,MAAMG,aAAaD,SAASE,QAAQ;QACpC,MAAML,aAAaG,SAASH,UAAU,IAAI;QAC1C,MAAMM,YAAYH,SAASG,SAAS;QAEpC,IAAIN,eAAe,GAAG;YACpB,IAAIF,gBAAgB;gBAClB,MAAM,IAAIS,MACR,CAAC,+DAA+D,EAAEjC,KAAK,CAAC,CAAC;YAE7E;YAEA,IAAI,CAAC,AAACI,WAAmBc,KAAK,CAACC,qBAAqB,EAAE;gBACpD,MAAM3B,oBACJO,KACAC,MACAC,KACAC,UACAC,cACAC,YACAC;YAEJ;YAEA,MAAM,EAAE6B,oBAAoB,CAAC,CAAC,EAAE,GAAGL;YAEnC,IAAIH,eAAe,KAAKH,gBAAeW,qCAAAA,kBAAmBC,WAAW,GAAE;gBACrE,MAAMC,MAAM,IAAIH,MACd,CAAC,iDAAiD,EAAEjC,KAAK,UAAU,EAAEkC,kBAAkBC,WAAW,CAAC,CAAC;gBAGtG,4DAA4D;gBAC5D,MAAM,EAAEE,KAAK,EAAE,GAAGH;gBAClB,IAAIG,OAAO;oBACTD,IAAIC,KAAK,GAAGD,IAAIE,OAAO,GAAGD,MAAME,SAAS,CAACF,MAAMG,OAAO,CAAC;gBAC1D;gBAEAC,QAAQC,IAAI,CAACN;YACf;YAEA,OAAO;gBAAEV,YAAY;YAAE;QACzB;QAEA,IAAIpB;QACJ,IAAIuB,SAASc,SAAS,EAAE;YACtBrC,UAAU;gBAAE,CAACsC,iCAAsB,CAAC,EAAEf,SAASc,SAAS;YAAC;QAC3D;QAEA,iCAAiC;QACjC,MAAMtC,WArID,QAuIHF,cACAyB,QAAQ,IACR;QAGF,0CAA0C;QAC1C,MAAMiB,OAAsB;YAC1BC,QAAQC;YACRzC;YACA0B;QACF;QAEA,MAAM3B,WAjJD,QAmJHF,aAAaiB,OAAO,CAAC,WAAW,UAChC4B,KAAKC,SAAS,CAACJ,MAAM,MAAM;QAG7B,qCAAqC;QACrC,MAAMxC,WAzJC,UA2JLF,aAAaiB,OAAO,CAAC,WAAW,SAChCU;QAGF,OAAO;YACL,iEAAiE;YACjED,UAAUqB,sBAAc,GAAGL,OAAOE;YAClCI,iBAAiBC,QAAQpB,cAAcJ,SAAS;YAChDyB,cAAcD,QAAQpB;YACtBN;QACF;IACF,EAAE,OAAOU,KAAU;QACjB,IAAI,CAACkB,IAAAA,wCAAmB,EAAClB,MAAM;YAC7B,MAAMA;QACR;QAEA,OAAO;YAAEV,YAAY;QAAE;IACzB;AACF"}