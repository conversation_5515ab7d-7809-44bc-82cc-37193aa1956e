{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts"], "names": ["handleExternalUrl", "navigateReducer", "state", "mutable", "url", "pendingPush", "previousTree", "tree", "mpaNavigation", "canonicalUrl", "scrollableSegments", "undefined", "handleMutable", "generateSegmentsFromPatch", "flightRouterPatch", "segments", "segment", "parallelRoutes", "Object", "keys", "length", "parallelRouteKey", "parallelRoute", "entries", "childSegment", "push", "addRefetchToLeafSegments", "newCache", "currentCache", "flightSegmentPath", "treePatch", "data", "appliedPatch", "status", "CacheStates", "READY", "subTreeData", "Map", "segmentPathsToFill", "map", "segmentPaths", "res", "fillCacheWithDataProperty", "bailOptimistic", "action", "isExternalUrl", "navigateType", "cache", "forceOptimisticNavigation", "shouldScroll", "pathname", "hash", "href", "createHrefFromUrl", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchCache", "isForCurrentTree", "JSON", "stringify", "toString", "prefetchValues", "get", "kind", "PrefetchKind", "TEMPORARY", "split", "optimisticTree", "createOptimisticTree", "temporaryCacheNode", "fetchResponse", "createRecordFromThenable", "fetchServerResponse", "nextUrl", "buildId", "optimisticFlightSegmentPath", "slice", "flat", "patchedTree", "hashFragment", "set", "Promise", "resolve", "prefetchTime", "Date", "now", "treeAtTimeOfPrefetch", "lastUsedTime", "process", "env", "NODE_ENV", "AUTO", "newPrefetchValue", "prefetchEntryCacheStatus", "getPrefetchEntryCacheStatus", "prefetchQueue", "bump", "flightData", "canonicalUrlOverride", "postponed", "readRecordValue", "currentTree", "flightDataPath", "flightSegmentPathWithLeadingEmpty", "newTree", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "applied", "applyFlightData", "PrefetchCacheEntryStatus", "reusable", "stale", "hardNavigate", "shouldHardNavigate", "invalidateCacheBelowFlightSegmentPath", "subSegment", "scrollableSegmentPath"], "mappings": ";;;;;;;;;;;;;;;IAkCgBA,iBAAiB;eAAjBA;;IAwEAC,eAAe;eAAfA;;;+CA1GY;qCAMQ;0CAEK;iCACT;mCACE;uDACoB;2CACZ;sCACL;6CACO;oCACT;6CACS;oCAQf;+BACC;iCACE;6CAIzB;oCAC4B;iCACL;AAEvB,SAASD,kBACdE,KAA2B,EAC3BC,OAAgB,EAChBC,GAAW,EACXC,WAAoB;IAEpBF,QAAQG,YAAY,GAAGJ,MAAMK,IAAI;IACjCJ,QAAQK,aAAa,GAAG;IACxBL,QAAQM,YAAY,GAAGL;IACvBD,QAAQE,WAAW,GAAGA;IACtBF,QAAQO,kBAAkB,GAAGC;IAE7B,OAAOC,IAAAA,4BAAa,EAACV,OAAOC;AAC9B;AAEA,SAASU,0BACPC,iBAAoC;IAEpC,MAAMC,WAAgC,EAAE;IACxC,MAAM,CAACC,SAASC,eAAe,GAAGH;IAElC,IAAII,OAAOC,IAAI,CAACF,gBAAgBG,MAAM,KAAK,GAAG;QAC5C,OAAO;YAAC;gBAACJ;aAAQ;SAAC;IACpB;IAEA,KAAK,MAAM,CAACK,kBAAkBC,cAAc,IAAIJ,OAAOK,OAAO,CAC5DN,gBACC;QACD,KAAK,MAAMO,gBAAgBX,0BAA0BS,eAAgB;YACnE,mEAAmE;YACnE,IAAIN,YAAY,IAAI;gBAClBD,SAASU,IAAI,CAAC;oBAACJ;uBAAqBG;iBAAa;YACnD,OAAO;gBACLT,SAASU,IAAI,CAAC;oBAACT;oBAASK;uBAAqBG;iBAAa;YAC5D;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASW,yBACPC,QAAmB,EACnBC,YAAuB,EACvBC,iBAAoC,EACpCC,SAA4B,EAC5BC,IAAqD;IAErD,IAAIC,eAAe;IAEnBL,SAASM,MAAM,GAAGC,0CAAW,CAACC,KAAK;IACnCR,SAASS,WAAW,GAAGR,aAAaQ,WAAW;IAC/CT,SAASV,cAAc,GAAG,IAAIoB,IAAIT,aAAaX,cAAc;IAE7D,MAAMqB,qBAAqBzB,0BAA0BiB,WAAWS,GAAG,CACjE,CAACvB,UAAY;eAAIa;eAAsBb;SAAQ;IAGjD,KAAK,MAAMwB,gBAAgBF,mBAAoB;QAC7C,MAAMG,MAAMC,IAAAA,oDAAyB,EACnCf,UACAC,cACAY,cACAT;QAEF,IAAI,EAACU,uBAAAA,IAAKE,cAAc,GAAE;YACxBX,eAAe;QACjB;IACF;IAEA,OAAOA;AACT;AACO,SAAS/B,gBACdC,KAA2B,EAC3B0C,MAAsB;IAEtB,MAAM,EACJxC,GAAG,EACHyC,aAAa,EACbC,YAAY,EACZC,KAAK,EACL5C,OAAO,EACP6C,yBAAyB,EACzBC,YAAY,EACb,GAAGL;IACJ,MAAM,EAAEM,QAAQ,EAAEC,IAAI,EAAE,GAAG/C;IAC3B,MAAMgD,OAAOC,IAAAA,oCAAiB,EAACjD;IAC/B,MAAMC,cAAcyC,iBAAiB;IACrC,wFAAwF;IACxFQ,IAAAA,sCAAkB,EAACpD,MAAMqD,aAAa;IAEtC,MAAMC,mBACJC,KAAKC,SAAS,CAACvD,QAAQG,YAAY,MAAMmD,KAAKC,SAAS,CAACxD,MAAMK,IAAI;IAEpE,IAAIiD,kBAAkB;QACpB,OAAO5C,IAAAA,4BAAa,EAACV,OAAOC;IAC9B;IAEA,IAAI0C,eAAe;QACjB,OAAO7C,kBAAkBE,OAAOC,SAASC,IAAIuD,QAAQ,IAAItD;IAC3D;IAEA,IAAIuD,iBAAiB1D,MAAMqD,aAAa,CAACM,GAAG,CAACR,IAAAA,oCAAiB,EAACjD,KAAK;IAEpE,IACE4C,6BACAY,CAAAA,kCAAAA,eAAgBE,IAAI,MAAKC,gCAAY,CAACC,SAAS,EAC/C;QACA,MAAMjD,WAAWmC,SAASe,KAAK,CAAC;QAChC,wDAAwD;QACxDlD,SAASU,IAAI,CAAC;QAEd,wBAAwB;QACxB,kGAAkG;QAClG,MAAMyC,iBAAiBC,IAAAA,0CAAoB,EAACpD,UAAUb,MAAMK,IAAI,EAAE;QAElE,8DAA8D;QAC9D,MAAM6D,qBAAgC;YACpC,GAAGrB,KAAK;QACV;QAEA,mDAAmD;QACnD,+DAA+D;QAC/DqB,mBAAmBnC,MAAM,GAAGC,0CAAW,CAACC,KAAK;QAC7CiC,mBAAmBhC,WAAW,GAAGlC,MAAM6C,KAAK,CAACX,WAAW;QACxDgC,mBAAmBnD,cAAc,GAAG,IAAIoB,IAAInC,MAAM6C,KAAK,CAAC9B,cAAc;QAEtE,IAAIc,OAAyD;QAE7D,MAAMsC,gBAAgB;YACpB,IAAI,CAACtC,MAAM;gBACTA,OAAOuC,IAAAA,kDAAwB,EAC7BC,IAAAA,wCAAmB,EAACnE,KAAK8D,gBAAgBhE,MAAMsE,OAAO,EAAEtE,MAAMuE,OAAO;YAEzE;YACA,OAAO1C;QACT;QAEA,0EAA0E;QAC1E,6DAA6D;QAC7D,MAAM2C,8BAA8B3D,SACjC4D,KAAK,CAAC,GACNpC,GAAG,CAAC,CAACvB,UAAY;gBAAC;gBAAYA;aAAQ,EACtC4D,IAAI;QAEP,wGAAwG;QACxG,0HAA0H;QAC1H,MAAMnC,MAAMC,IAAAA,oDAAyB,EACnC0B,oBACAlE,MAAM6C,KAAK,EACX2B,6BACAL,eACA;QAGF,gFAAgF;QAChF,IAAI,EAAC5B,uBAAAA,IAAKE,cAAc,GAAE;YACxBxC,QAAQG,YAAY,GAAGJ,MAAMK,IAAI;YACjCJ,QAAQ0E,WAAW,GAAGX;YACtB/D,QAAQE,WAAW,GAAGA;YACtBF,QAAQ2E,YAAY,GAAG3B;YACvBhD,QAAQ8C,YAAY,GAAGA;YACvB9C,QAAQO,kBAAkB,GAAG,EAAE;YAC/BP,QAAQ4C,KAAK,GAAGqB;YAChBjE,QAAQM,YAAY,GAAG2C;YAEvBlD,MAAMqD,aAAa,CAACwB,GAAG,CAAC1B,IAAAA,oCAAiB,EAACjD,KAAK,QAAQ;gBACrD2B,MAAMA,OAAOuC,IAAAA,kDAAwB,EAACU,QAAQC,OAAO,CAAClD,SAAS;gBAC/D,iEAAiE;gBACjE+B,MAAMC,gCAAY,CAACC,SAAS;gBAC5BkB,cAAcC,KAAKC,GAAG;gBACtBC,sBAAsBnF,MAAMK,IAAI;gBAChC+E,cAAcH,KAAKC,GAAG;YACxB;YAEA,OAAOxE,IAAAA,4BAAa,EAACV,OAAOC;QAC9B;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAACyD,gBAAgB;QACnB,MAAM7B,OAAOuC,IAAAA,kDAAwB,EACnCC,IAAAA,wCAAmB,EACjBnE,KACAF,MAAMK,IAAI,EACVL,MAAMsE,OAAO,EACbtE,MAAMuE,OAAO,EACb,8EAA8E;QAC9E,0DAA0D;QAC1Dc,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB1B,gCAAY,CAAC2B,IAAI,GAAG/E;QAIjE,MAAMgF,mBAAmB;YACvB5D,MAAMuC,IAAAA,kDAAwB,EAACU,QAAQC,OAAO,CAAClD;YAC/C,iEAAiE;YACjE+B,MACEyB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrB1B,gCAAY,CAAC2B,IAAI,GACjB3B,gCAAY,CAACC,SAAS;YAC5BkB,cAAcC,KAAKC,GAAG;YACtBC,sBAAsBnF,MAAMK,IAAI;YAChC+E,cAAc;QAChB;QAEApF,MAAMqD,aAAa,CAACwB,GAAG,CAAC1B,IAAAA,oCAAiB,EAACjD,KAAK,QAAQuF;QACvD/B,iBAAiB+B;IACnB;IAEA,MAAMC,2BAA2BC,IAAAA,wDAA2B,EAACjC;IAE7D,0DAA0D;IAC1D,MAAM,EAAEyB,oBAAoB,EAAEtD,IAAI,EAAE,GAAG6B;IAEvCkC,8BAAa,CAACC,IAAI,CAAChE;IAEnB,0FAA0F;IAC1F,MAAM,CAACiE,YAAYC,sBAAsBC,UAAU,GAAGC,IAAAA,gCAAe,EAACpE;IAEtE,iCAAiC;IACjC,IAAI,CAAC6B,eAAe0B,YAAY,EAAE;QAChC,gGAAgG;QAChG1B,eAAe0B,YAAY,GAAGH,KAAKC,GAAG;IACxC;IAEA,4DAA4D;IAC5D,IAAI,OAAOY,eAAe,UAAU;QAClC,OAAOhG,kBAAkBE,OAAOC,SAAS6F,YAAY3F;IACvD;IAEA,IAAI+F,cAAclG,MAAMK,IAAI;IAC5B,IAAIqB,eAAe1B,MAAM6C,KAAK;IAC9B,IAAIrC,qBAA0C,EAAE;IAChD,KAAK,MAAM2F,kBAAkBL,WAAY;QACvC,MAAMnE,oBAAoBwE,eAAe1B,KAAK,CAC5C,GACA,CAAC;QAEH,0DAA0D;QAC1D,MAAM7C,YAAYuE,eAAe1B,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;QAE7C,sBAAsB;QACtB,MAAM2B,oCAAoC;YAAC;eAAOzE;SAAkB;QAEpE,wEAAwE;QACxE,IAAI0E,UAAUC,IAAAA,wDAA2B,EACvC,sBAAsB;QACtBF,mCACAF,aACAtE;QAGF,kGAAkG;QAClG,6IAA6I;QAC7I,IAAIyE,YAAY,MAAM;YACpBA,UAAUC,IAAAA,wDAA2B,EACnC,sBAAsB;YACtBF,mCACAjB,sBACAvD;QAEJ;QAEA,IAAIyE,YAAY,MAAM;YACpB,IAAIE,IAAAA,wDAA2B,EAACL,aAAaG,UAAU;gBACrD,OAAOvG,kBAAkBE,OAAOC,SAASiD,MAAM/C;YACjD;YAEA,qEAAqE;YACrE,6DAA6D;YAC7D,IAAIqG,UAAUR,YACV,QACAS,IAAAA,gCAAe,EACb/E,cACAmB,OACAsD,gBACAzC,eAAeE,IAAI,KAAK,UACtB8B,6BAA6BgB,qDAAwB,CAACC,QAAQ;YAGtE,IACE,CAACH,WACDd,6BAA6BgB,qDAAwB,CAACE,KAAK,EAC3D;gBACAJ,UAAUhF,yBACRqB,OACAnB,cACAC,mBACAC,WACA,wCAAwC;gBACxC,IACEwC,IAAAA,kDAAwB,EACtBC,IAAAA,wCAAmB,EACjBnE,KACAgG,aACAlG,MAAMsE,OAAO,EACbtE,MAAMuE,OAAO;YAIvB;YAEA,MAAMsC,eAAeC,IAAAA,sCAAkB,EACrC,sBAAsB;YACtBV,mCACAF;YAGF,IAAIW,cAAc;gBAChBhE,MAAMd,MAAM,GAAGC,0CAAW,CAACC,KAAK;gBAChC,mDAAmD;gBACnDY,MAAMX,WAAW,GAAGR,aAAaQ,WAAW;gBAE5C6E,IAAAA,4EAAqC,EACnClE,OACAnB,cACAC;gBAEF,8EAA8E;gBAC9E1B,QAAQ4C,KAAK,GAAGA;YAClB,OAAO,IAAI2D,SAAS;gBAClBvG,QAAQ4C,KAAK,GAAGA;YAClB;YAEAnB,eAAemB;YACfqD,cAAcG;YAEd,KAAK,MAAMW,cAAcrG,0BAA0BiB,WAAY;gBAC7D,MAAMqF,wBAAwB;uBAAItF;uBAAsBqF;iBAAW;gBACnE,kFAAkF;gBAClF,IACEC,qBAAqB,CAACA,sBAAsB/F,MAAM,GAAG,EAAE,KACvD,eACA;oBACAV,mBAAmBe,IAAI,CAAC0F;gBAC1B;YACF;QACF;IACF;IAEAhH,QAAQG,YAAY,GAAGJ,MAAMK,IAAI;IACjCJ,QAAQ0E,WAAW,GAAGuB;IACtBjG,QAAQM,YAAY,GAAGwF,uBACnB5C,IAAAA,oCAAiB,EAAC4C,wBAClB7C;IACJjD,QAAQE,WAAW,GAAGA;IACtBF,QAAQO,kBAAkB,GAAGA;IAC7BP,QAAQ2E,YAAY,GAAG3B;IACvBhD,QAAQ8C,YAAY,GAAGA;IAEvB,OAAOrC,IAAAA,4BAAa,EAACV,OAAOC;AAC9B"}