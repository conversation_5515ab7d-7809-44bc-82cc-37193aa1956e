{"version": 3, "sources": ["../../../src/shared/lib/app-router-context.shared-runtime.ts"], "names": ["AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "CacheStates", "LAZY_INITIALIZED", "DATA_FETCH", "READY", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;IA6GaA,gBAAgB;eAAhBA;;IAGAC,mBAAmB;eAAnBA;;IAKAC,yBAAyB;eAAzBA;;IAYAC,eAAe;eAAfA;;;;gEArHK;IAKX;UAAKC,WAAW;IAAXA,YACVC,sBAAmB;IADTD,YAEVE,gBAAa;IAFHF,YAGVG,WAAAA;GAHUH,gBAAAA;AA4FL,MAAMJ,mBAAmBQ,cAAK,CAACC,aAAa,CACjD;AAEK,MAAMR,sBAAsBO,cAAK,CAACC,aAAa,CAInD;AACI,MAAMP,4BAA4BM,cAAK,CAACC,aAAa,CAUzD;AAEI,MAAMN,kBAAkBK,cAAK,CAACC,aAAa,CAAkB;AAEpE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCZ,iBAAiBa,WAAW,GAAG;IAC/BZ,oBAAoBY,WAAW,GAAG;IAClCX,0BAA0BW,WAAW,GAAG;IACxCV,gBAAgBU,WAAW,GAAG;AAChC"}