{"version": 3, "sources": ["../../src/lib/download-swc.ts"], "names": ["downloadNativeNextSwc", "downloadWasmSwc", "WritableStream", "require", "MAX_VERSIONS_TO_CACHE", "extractBinary", "outputDirectory", "pkgName", "tarFileName", "cacheDirectory", "getCacheDirectory", "process", "env", "extractFromTar", "tar", "x", "file", "path", "join", "cwd", "strip", "fs", "existsSync", "Log", "info", "promises", "mkdir", "recursive", "tempFile", "Date", "now", "registry", "getRegistry", "downloadUrl", "fetch", "then", "res", "ok", "body", "error", "Error", "status", "cacheWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "close", "rename", "cacheFiles", "readdir", "length", "sort", "a", "b", "localeCompare", "i", "unlink", "catch", "version", "bindingsDirectory", "triplesABI", "triple", "substring", "wasmDirectory", "variant"], "mappings": ";;;;;;;;;;;;;;;IAsFsBA,qBAAqB;eAArBA;;IAqBAC,eAAe;eAAfA;;;2DA3GP;6DACE;6DACI;4DACL;6BAIY;mCACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJlC,MAAM,EAAEC,cAAc,EAAE,GAAGC,QAAQ;AAMnC,MAAMC,wBAAwB;AAE9B,eAAeC,cACbC,eAAuB,EACvBC,OAAe,EACfC,WAAmB;IAEnB,MAAMC,iBAAiBC,IAAAA,oCAAiB,EACtC,YACAC,QAAQC,GAAG,CAAC,gBAAgB;IAG9B,MAAMC,iBAAiB,IACrBC,YAAG,CAACC,CAAC,CAAC;YACJC,MAAMC,aAAI,CAACC,IAAI,CAACT,gBAAgBD;YAChCW,KAAKb;YACLc,OAAO;QACT;IAEF,IAAI,CAACC,WAAE,CAACC,UAAU,CAACL,aAAI,CAACC,IAAI,CAACT,gBAAgBD,eAAe;QAC1De,KAAIC,IAAI,CAAC,CAAC,wBAAwB,EAAEjB,QAAQ,GAAG,CAAC;QAChD,MAAMc,WAAE,CAACI,QAAQ,CAACC,KAAK,CAACjB,gBAAgB;YAAEkB,WAAW;QAAK;QAC1D,MAAMC,WAAWX,aAAI,CAACC,IAAI,CACxBT,gBACA,CAAC,EAAED,YAAY,MAAM,EAAEqB,KAAKC,GAAG,GAAG,CAAC;QAGrC,MAAMC,WAAWC,IAAAA,wBAAW;QAE5B,MAAMC,cAAc,CAAC,EAAEF,SAAS,EAAExB,QAAQ,GAAG,EAAEC,YAAY,CAAC;QAE5D,MAAM0B,MAAMD,aAAaE,IAAI,CAAC,CAACC;YAC7B,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAE,GAAGF;YACrB,IAAI,CAACC,MAAM,CAACC,MAAM;gBAChBf,KAAIgB,KAAK,CAAC,CAAC,oCAAoC,EAAEN,YAAY,CAAC;YAChE;YAEA,IAAI,CAACI,IAAI;gBACP,MAAM,IAAIG,MAAM,CAAC,2BAA2B,EAAEJ,IAAIK,MAAM,CAAC,CAAC;YAC5D;YACA,IAAI,CAACH,MAAM;gBACT,MAAM,IAAIE,MAAM;YAClB;YACA,MAAME,mBAAmBrB,WAAE,CAACsB,iBAAiB,CAACf;YAC9C,OAAOU,KAAKM,MAAM,CAChB,IAAI1C,eAAe;gBACjB2C,OAAMC,KAAK;oBACTJ,iBAAiBG,KAAK,CAACC;gBACzB;gBACAC;oBACEL,iBAAiBK,KAAK;gBACxB;YACF;QAEJ;QACA,MAAM1B,WAAE,CAACI,QAAQ,CAACuB,MAAM,CAACpB,UAAUX,aAAI,CAACC,IAAI,CAACT,gBAAgBD;IAC/D;IACA,MAAMK;IAEN,MAAMoC,aAAa,MAAM5B,WAAE,CAACI,QAAQ,CAACyB,OAAO,CAACzC;IAE7C,IAAIwC,WAAWE,MAAM,GAAG/C,uBAAuB;QAC7C6C,WAAWG,IAAI,CAAC,CAACC,GAAGC;YAClB,IAAID,EAAEF,MAAM,GAAGG,EAAEH,MAAM,EAAE,OAAO,CAAC;YACjC,OAAOE,EAAEE,aAAa,CAACD;QACzB;QAEA,iCAAiC;QACjC,IAAK,IAAIE,IAAI,GAAGA,KAAKA,IAAIP,WAAWE,MAAM,GAAG/C,sBAAuB;YAClE,MAAMiB,WAAE,CAACI,QAAQ,CACdgC,MAAM,CAACxC,aAAI,CAACC,IAAI,CAACT,gBAAgBwC,UAAU,CAACO,EAAE,GAC9CE,KAAK,CAAC,KAAO;QAClB;IACF;AACF;AAEO,eAAe1D,sBACpB2D,OAAe,EACfC,iBAAyB,EACzBC,UAAyB;IAEzB,KAAK,MAAMC,UAAUD,WAAY;QAC/B,MAAMtD,UAAU,CAAC,UAAU,EAAEuD,OAAO,CAAC;QACrC,MAAMtD,cAAc,CAAC,EAAED,QAAQwD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;QAC5D,MAAMrD,kBAAkBW,aAAI,CAACC,IAAI,CAAC0C,mBAAmBrD;QAErD,IAAIc,WAAE,CAACC,UAAU,CAAChB,kBAAkB;YAClC,mDAAmD;YACnD,0CAA0C;YAC1C;QACF;QAEA,MAAMe,WAAE,CAACI,QAAQ,CAACC,KAAK,CAACpB,iBAAiB;YAAEqB,WAAW;QAAK;QAC3D,MAAMtB,cAAcC,iBAAiBC,SAASC;IAChD;AACF;AAEO,eAAeP,gBACpB0D,OAAe,EACfK,aAAqB,EACrBC,UAA4B,QAAQ;IAEpC,MAAM1D,UAAU,CAAC,eAAe,EAAE0D,QAAQ,CAAC;IAC3C,MAAMzD,cAAc,CAAC,EAAED,QAAQwD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;IAC5D,MAAMrD,kBAAkBW,aAAI,CAACC,IAAI,CAAC8C,eAAezD;IAEjD,IAAIc,WAAE,CAACC,UAAU,CAAChB,kBAAkB;QAClC,mDAAmD;QACnD,0CAA0C;QAC1C;IACF;IAEA,MAAMe,WAAE,CAACI,QAAQ,CAACC,KAAK,CAACpB,iBAAiB;QAAEqB,WAAW;IAAK;IAC3D,MAAMtB,cAAcC,iBAAiBC,SAASC;AAChD"}