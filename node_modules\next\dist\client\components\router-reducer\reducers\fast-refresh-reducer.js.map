{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/fast-refresh-reducer.ts"], "names": ["fastRefreshReducer", "fastRefreshReducerImpl", "state", "action", "cache", "mutable", "origin", "href", "canonicalUrl", "isForCurrentTree", "JSON", "stringify", "previousTree", "tree", "handleMutable", "data", "createRecordFromThenable", "fetchServerResponse", "URL", "nextUrl", "buildId", "flightData", "canonicalUrlOverride", "readRecordValue", "handleExternalUrl", "pushRef", "pendingPush", "currentTree", "currentCache", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "applied", "applyFlightData", "patchedTree", "fastRefreshReducerNoop", "_action", "process", "env", "NODE_ENV"], "mappings": ";;;;+BAmHaA;;;eAAAA;;;qCAnHuB;0CACK;iCACT;mCACE;6CACU;6CACA;iCAMV;+BACJ;iCACE;AAEhC,wFAAwF;AACxF,SAASC,uBACPC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAGH;IACnC,MAAMI,OAAOL,MAAMM,YAAY;IAE/B,MAAMC,mBACJC,KAAKC,SAAS,CAACN,QAAQO,YAAY,MAAMF,KAAKC,SAAS,CAACT,MAAMW,IAAI;IAEpE,IAAIJ,kBAAkB;QACpB,OAAOK,IAAAA,4BAAa,EAACZ,OAAOG;IAC9B;IAEA,IAAI,CAACD,MAAMW,IAAI,EAAE;QACf,uDAAuD;QACvD,wCAAwC;QACxCX,MAAMW,IAAI,GAAGC,IAAAA,kDAAwB,EACnCC,IAAAA,wCAAmB,EACjB,IAAIC,IAAIX,MAAMD,SACd;YAACJ,MAAMW,IAAI,CAAC,EAAE;YAAEX,MAAMW,IAAI,CAAC,EAAE;YAAEX,MAAMW,IAAI,CAAC,EAAE;YAAE;SAAU,EACxDX,MAAMiB,OAAO,EACbjB,MAAMkB,OAAO;IAGnB;IACA,MAAM,CAACC,YAAYC,qBAAqB,GAAGC,IAAAA,gCAAe,EAACnB,MAAMW,IAAI;IAErE,4DAA4D;IAC5D,IAAI,OAAOM,eAAe,UAAU;QAClC,OAAOG,IAAAA,kCAAiB,EACtBtB,OACAG,SACAgB,YACAnB,MAAMuB,OAAO,CAACC,WAAW;IAE7B;IAEA,2DAA2D;IAC3DtB,MAAMW,IAAI,GAAG;IAEb,IAAIY,cAAczB,MAAMW,IAAI;IAC5B,IAAIe,eAAe1B,MAAME,KAAK;IAE9B,KAAK,MAAMyB,kBAAkBR,WAAY;QACvC,oFAAoF;QACpF,IAAIQ,eAAeC,MAAM,KAAK,GAAG;YAC/B,oCAAoC;YACpCC,QAAQC,GAAG,CAAC;YACZ,OAAO9B;QACT;QAEA,2GAA2G;QAC3G,MAAM,CAAC+B,UAAU,GAAGJ;QACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;QACtB;YAAC;SAAG,EACJR,aACAM;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAIC,IAAAA,wDAA2B,EAACV,aAAaO,UAAU;YACrD,OAAOV,IAAAA,kCAAiB,EAACtB,OAAOG,SAASE,MAAML,MAAMuB,OAAO,CAACC,WAAW;QAC1E;QAEA,MAAMY,2BAA2BhB,uBAC7BiB,IAAAA,oCAAiB,EAACjB,wBAClBkB;QAEJ,IAAIlB,sBAAsB;YACxBjB,QAAQG,YAAY,GAAG8B;QACzB;QACA,MAAMG,UAAUC,IAAAA,gCAAe,EAACd,cAAcxB,OAAOyB;QAErD,IAAIY,SAAS;YACXpC,QAAQD,KAAK,GAAGA;YAChBwB,eAAexB;QACjB;QAEAC,QAAQO,YAAY,GAAGe;QACvBtB,QAAQsC,WAAW,GAAGT;QACtB7B,QAAQG,YAAY,GAAGD;QAEvBoB,cAAcO;IAChB;IACA,OAAOpB,IAAAA,4BAAa,EAACZ,OAAOG;AAC9B;AAEA,SAASuC,uBACP1C,KAA2B,EAC3B2C,OAA0B;IAE1B,OAAO3C;AACT;AAEO,MAAMF,qBACX8C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrBJ,yBACA3C"}