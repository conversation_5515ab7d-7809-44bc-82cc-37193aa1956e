"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./node_modules/lucide-react/dist/esm/icons/alert-circle.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/alert-circle.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlertCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */\n\n\n\nconst AlertCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"AlertCircle\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"line\", { x1: \"12\", x2: \"12\", y1: \"8\", y2: \"12\", key: \"1pkeuh\" }],\n  [\"line\", { x1: \"12\", x2: \"12.01\", y1: \"16\", y2: \"16\", key: \"4dfq90\" }]\n]);\n\n\n//# sourceMappingURL=alert-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2FsZXJ0LWNpcmNsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELG9CQUFvQixnRUFBZ0I7QUFDcEMsZUFBZSw0Q0FBNEM7QUFDM0QsYUFBYSxzREFBc0Q7QUFDbkUsYUFBYSwwREFBMEQ7QUFDdkU7O0FBRWtDO0FBQ2xDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYWxlcnQtY2lyY2xlLmpzPzFiNjUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBsdWNpZGUtcmVhY3QgdjAuMjkyLjAgLSBJU0NcbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgQWxlcnRDaXJjbGUgPSBjcmVhdGVMdWNpZGVJY29uKFwiQWxlcnRDaXJjbGVcIiwgW1xuICBbXCJjaXJjbGVcIiwgeyBjeDogXCIxMlwiLCBjeTogXCIxMlwiLCByOiBcIjEwXCIsIGtleTogXCIxbWdsYXlcIiB9XSxcbiAgW1wibGluZVwiLCB7IHgxOiBcIjEyXCIsIHgyOiBcIjEyXCIsIHkxOiBcIjhcIiwgeTI6IFwiMTJcIiwga2V5OiBcIjFwa2V1aFwiIH1dLFxuICBbXCJsaW5lXCIsIHsgeDE6IFwiMTJcIiwgeDI6IFwiMTIuMDFcIiwgeTE6IFwiMTZcIiwgeTI6IFwiMTZcIiwga2V5OiBcIjRkZnE5MFwiIH1dXG5dKTtcblxuZXhwb3J0IHsgQWxlcnRDaXJjbGUgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWxlcnQtY2lyY2xlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/alert-circle.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/check-circle.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check-circle.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CheckCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */\n\n\n\nconst CheckCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CheckCircle\", [\n  [\"path\", { d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\", key: \"g774vq\" }],\n  [\"path\", { d: \"m9 11 3 3L22 4\", key: \"1pflzl\" }]\n]);\n\n\n//# sourceMappingURL=check-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NoZWNrLWNpcmNsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELG9CQUFvQixnRUFBZ0I7QUFDcEMsYUFBYSx3REFBd0Q7QUFDckUsYUFBYSxvQ0FBb0M7QUFDakQ7O0FBRWtDO0FBQ2xDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hlY2stY2lyY2xlLmpzP2Y5ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBsdWNpZGUtcmVhY3QgdjAuMjkyLjAgLSBJU0NcbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgQ2hlY2tDaXJjbGUgPSBjcmVhdGVMdWNpZGVJY29uKFwiQ2hlY2tDaXJjbGVcIiwgW1xuICBbXCJwYXRoXCIsIHsgZDogXCJNMjIgMTEuMDhWMTJhMTAgMTAgMCAxIDEtNS45My05LjE0XCIsIGtleTogXCJnNzc0dnFcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwibTkgMTEgMyAzTDIyIDRcIiwga2V5OiBcIjFwZmx6bFwiIH1dXG5dKTtcblxuZXhwb3J0IHsgQ2hlY2tDaXJjbGUgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2hlY2stY2lyY2xlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/check-circle.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*****************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*****************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCircle: function() { return /* reexport safe */ _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   CheckCircle: function() { return /* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   ExternalLink: function() { return /* reexport safe */ _icons_external_link_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/alert-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/check-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_external_link_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/external-link.js */ \"./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydENpcmNsZSxDaGVja0NpcmNsZSxFeHRlcm5hbExpbmshPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNnRTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzgyMjUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBsdWNpZGUtcmVhY3QgdjAuMjkyLjAgLSBJU0NcbiAqLyBcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQWxlcnRDaXJjbGUgfSBmcm9tIFwiLi9pY29ucy9hbGVydC1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0NpcmNsZSB9IGZyb20gXCIuL2ljb25zL2NoZWNrLWNpcmNsZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEV4dGVybmFsTGluayB9IGZyb20gXCIuL2ljb25zL2V4dGVybmFsLWxpbmsuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "./components/Auth/AuthPage.js":
/*!*************************************!*\
  !*** ./components/Auth/AuthPage.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AuthPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoginForm */ \"./components/Auth/LoginForm.js\");\n/* harmony import */ var _SignupForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SignupForm */ \"./components/Auth/SignupForm.js\");\n/* harmony import */ var _FirebaseSetupCheck__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../FirebaseSetupCheck */ \"./components/FirebaseSetupCheck.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction AuthPage() {\n    _s();\n    const [isLogin, setIsLogin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const toggleMode = ()=>{\n        setIsLogin(!isLogin);\n    };\n    return isLogin ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoginForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        onToggleMode: toggleMode\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\AuthPage.js\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SignupForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        onToggleMode: toggleMode\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\AuthPage.js\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthPage, \"juHMKC6x2j1wnRvCiB5VrABnZyE=\");\n_c = AuthPage;\nvar _c;\n$RefreshReg$(_c, \"AuthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0F1dGgvQXV0aFBhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFpQztBQUNHO0FBQ0U7QUFDaUI7QUFFeEMsU0FBU0k7O0lBQ3RCLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHTiwrQ0FBUUEsQ0FBQztJQUV2QyxNQUFNTyxhQUFhO1FBQ2pCRCxXQUFXLENBQUNEO0lBQ2Q7SUFFQSxPQUFPQSx3QkFDTCw4REFBQ0osa0RBQVNBO1FBQUNPLGNBQWNEOzs7Ozs2QkFFekIsOERBQUNMLG1EQUFVQTtRQUFDTSxjQUFjRDs7Ozs7O0FBRTlCO0dBWndCSDtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL0F1dGgvQXV0aFBhZ2UuanM/NGEwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMb2dpbkZvcm0gZnJvbSAnLi9Mb2dpbkZvcm0nO1xuaW1wb3J0IFNpZ251cEZvcm0gZnJvbSAnLi9TaWdudXBGb3JtJztcbmltcG9ydCBGaXJlYmFzZVNldHVwQ2hlY2sgZnJvbSAnLi4vRmlyZWJhc2VTZXR1cENoZWNrJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXV0aFBhZ2UoKSB7XG4gIGNvbnN0IFtpc0xvZ2luLCBzZXRJc0xvZ2luXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIGNvbnN0IHRvZ2dsZU1vZGUgPSAoKSA9PiB7XG4gICAgc2V0SXNMb2dpbighaXNMb2dpbik7XG4gIH07XG5cbiAgcmV0dXJuIGlzTG9naW4gPyAoXG4gICAgPExvZ2luRm9ybSBvblRvZ2dsZU1vZGU9e3RvZ2dsZU1vZGV9IC8+XG4gICkgOiAoXG4gICAgPFNpZ251cEZvcm0gb25Ub2dnbGVNb2RlPXt0b2dnbGVNb2RlfSAvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiTG9naW5Gb3JtIiwiU2lnbnVwRm9ybSIsIkZpcmViYXNlU2V0dXBDaGVjayIsIkF1dGhQYWdlIiwiaXNMb2dpbiIsInNldElzTG9naW4iLCJ0b2dnbGVNb2RlIiwib25Ub2dnbGVNb2RlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Auth/AuthPage.js\n"));

/***/ }),

/***/ "./components/FirebaseSetupCheck.js":
/*!******************************************!*\
  !*** ./components/FirebaseSetupCheck.js ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FirebaseSetupCheck; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/firebase-config */ \"./lib/firebase-config.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,CheckCircle,ExternalLink!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction FirebaseSetupCheck() {\n    _s();\n    const [checks, setChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firebaseConfig: false,\n        authEnabled: false,\n        firestoreEnabled: false\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const runChecks = async ()=>{\n            const results = {\n                firebaseConfig: false,\n                authEnabled: false,\n                firestoreEnabled: false\n            };\n            // Check Firebase config\n            if (_lib_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth && _lib_firebase_config__WEBPACK_IMPORTED_MODULE_2__.db) {\n                results.firebaseConfig = true;\n            }\n            // Check Auth\n            try {\n                if (_lib_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth) {\n                    results.authEnabled = true;\n                }\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n            }\n            // Check Firestore\n            try {\n                if (_lib_firebase_config__WEBPACK_IMPORTED_MODULE_2__.db) {\n                    results.firestoreEnabled = true;\n                }\n            } catch (error) {\n                console.error(\"Firestore check failed:\", error);\n            }\n            setChecks(results);\n            setLoading(false);\n        };\n        runChecks();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-blue-800 text-sm\",\n                children: \"Checking Firebase setup...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    const allChecksPass = Object.values(checks).every((check)=>check);\n    if (allChecksPass) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                        className: \"h-5 w-5 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-800 text-sm font-medium\",\n                        children: \"Firebase is configured correctly!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.AlertCircle, {\n                    className: \"h-5 w-5 text-red-600 flex-shrink-0 mt-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-red-800 font-medium text-sm mb-2\",\n                            children: \"Firebase Setup Issues\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        checks.firebaseConfig ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.AlertCircle, {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: checks.firebaseConfig ? \"text-green-700\" : \"text-red-700\",\n                                            children: \"Firebase Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        checks.authEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.AlertCircle, {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: checks.authEnabled ? \"text-green-700\" : \"text-red-700\",\n                                            children: \"Authentication Service\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        checks.firestoreEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.AlertCircle, {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: checks.firestoreEnabled ? \"text-green-700\" : \"text-red-700\",\n                                            children: \"Firestore Database\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        !checks.authEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-800 text-sm mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Action Required:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" Enable Email/Password authentication in Firebase Console\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"text-yellow-700 text-xs space-y-1 ml-4 list-decimal\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Go to Firebase Console → Authentication\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Get started\" if not already done'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Go to \"Sign-in method\" tab'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Enable \"Email/Password\" provider'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Save changes\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://console.firebase.google.com/project/zatconss/authentication/providers\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"inline-flex items-center space-x-1 text-yellow-700 hover:text-yellow-800 text-xs mt-2 underline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Open Firebase Authentication\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.ExternalLink, {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this),\n                        !checks.firestoreEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-800 text-sm mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Action Required:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" Set up Firestore Database\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"text-yellow-700 text-xs space-y-1 ml-4 list-decimal\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Go to Firebase Console → Firestore Database\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Create database\"'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Choose \"Start in test mode\" for now'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Select a location for your database\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: 'Click \"Done\"'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://console.firebase.google.com/project/zatconss/firestore\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"inline-flex items-center space-x-1 text-yellow-700 hover:text-yellow-800 text-xs mt-2 underline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Open Firestore Database\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ExternalLink_lucide_react__WEBPACK_IMPORTED_MODULE_3__.ExternalLink, {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\FirebaseSetupCheck.js\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(FirebaseSetupCheck, \"yGf0DbBLJZelLLnQa05Zd9Y2xoE=\");\n_c = FirebaseSetupCheck;\nvar _c;\n$RefreshReg$(_c, \"FirebaseSetupCheck\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/FirebaseSetupCheck.js\n"));

/***/ })

});