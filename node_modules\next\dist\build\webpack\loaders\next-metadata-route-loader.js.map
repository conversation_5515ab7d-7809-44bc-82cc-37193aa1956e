{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-route-loader.ts"], "names": ["getFilenameAndExtension", "cacheHeader", "none", "longCache", "revalidate", "resourcePath", "filename", "path", "basename", "name", "ext", "split", "getContentType", "imageExtMimeTypeMap", "getStaticAssetRouteCode", "fileBaseName", "cache", "process", "env", "NODE_ENV", "code", "JSON", "stringify", "fs", "promises", "readFile", "toString", "getDynamicTextRouteCode", "getDynamicImageRouteCode", "getDynamicSiteMapRouteCode", "page", "staticGenerationCode", "includes", "nextMetadataRouterLoader", "isDynamic", "getOptions"], "mappings": ";;;;;;;;;;;;;;;IAgBgBA,uBAAuB;eAAvBA;;IAmNhB,OAAuC;eAAvC;;;2DAlOe;6DACE;0BACmB;;;;;;AAEpC,MAAMC,cAAc;IAClBC,MAAM;IACNC,WAAW;IACXC,YAAY;AACd;AAOO,SAASJ,wBAAwBK,YAAoB;IAC1D,MAAMC,WAAWC,aAAI,CAACC,QAAQ,CAACH;IAC/B,MAAM,CAACI,MAAMC,IAAI,GAAGJ,SAASK,KAAK,CAAC,KAAK;IACxC,OAAO;QAAEF;QAAMC;IAAI;AACrB;AAEA,SAASE,eAAeP,YAAoB;IAC1C,IAAI,EAAEI,IAAI,EAAEC,GAAG,EAAE,GAAGV,wBAAwBK;IAC5C,IAAIK,QAAQ,OAAOA,MAAM;IAEzB,IAAID,SAAS,aAAaC,QAAQ,OAAO,OAAO;IAChD,IAAID,SAAS,WAAW,OAAO;IAC/B,IAAIA,SAAS,UAAU,OAAO;IAC9B,IAAIA,SAAS,YAAY,OAAO;IAEhC,IAAIC,QAAQ,SAASA,QAAQ,UAAUA,QAAQ,SAASA,QAAQ,OAAO;QACrE,OAAOG,6BAAmB,CAACH,IAAI;IACjC;IACA,OAAO;AACT;AAEA,mHAAmH;AACnH,eAAeI,wBACbT,YAAoB,EACpBU,YAAoB;IAEpB,MAAMC,QACJD,iBAAiB,YACb,uCACAE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBlB,YAAYC,IAAI,GAChBD,YAAYE,SAAS;IAC3B,MAAMiB,OAAO,CAAC;;;oBAGI,EAAEC,KAAKC,SAAS,CAACV,eAAeP,eAAe;2BACxC,EAAEgB,KAAKC,SAAS,CACvC,AAAC,CAAA,MAAMC,WAAE,CAACC,QAAQ,CAACC,QAAQ,CAACpB,aAAY,EAAGqB,QAAQ,CAAC,WACpD;;;;;;;uBAOmB,EAAEL,KAAKC,SAAS,CAACN,OAAO;;;;;;AAM/C,CAAC;IACC,OAAOI;AACT;AAEA,SAASO,wBAAwBtB,YAAoB;IACnD,OAAO,CAAC;;oBAEU,EAAEgB,KAAKC,SAAS,CAACjB,cAAc;;;oBAG/B,EAAEgB,KAAKC,SAAS,CAACV,eAAeP,eAAe;iBAClD,EAAEgB,KAAKC,SAAS,CAACtB,wBAAwBK,cAAcI,IAAI,EAAE;;;;;;;;;uBASvD,EAAEY,KAAKC,SAAS,CAACrB,YAAYG,UAAU,EAAE;;;;AAIhE,CAAC;AACD;AAEA,iCAAiC;AACjC,SAASwB,yBAAyBvB,YAAoB;IACpD,OAAO,CAAC;;0BAEgB,EAAEgB,KAAKC,SAAS,CAACjB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BzD,CAAC;AACD;AAEA,SAASwB,2BAA2BxB,YAAoB,EAAEyB,IAAY;IACpE,IAAIC,uBAAuB;IAE3B,IACEd,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBW,KAAKE,QAAQ,CAAC,sBACd;QACAD,uBAAuB,CAAC;;;;;;;;;;IAUxB,CAAC;IACH;IAEA,MAAMX,OAAO,CAAC;;0BAEU,EAAEC,KAAKC,SAAS,CAACjB,cAAc;;;;;;oBAMrC,EAAEgB,KAAKC,SAAS,CAACV,eAAeP,eAAe;iBAClD,EAAEgB,KAAKC,SAAS,CAACtB,wBAAwBK,cAAcI,IAAI,EAAE;;AAE9E,EAAE,GAAG,wCAAwC,IAAG;cAClC,EAAEY,KAAKC,SAAS,CAACjB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA8BtB,EAAEgB,KAAKC,SAAS,CAACrB,YAAYG,UAAU,EAAE;;;;;AAKhE,EAAE2B,qBAAqB;AACvB,CAAC;IACC,OAAOX;AACT;AACA,gEAAgE;AAChE,gFAAgF;AAChF,oDAAoD;AACpD,MAAMa,2BACJ;IACE,MAAM,EAAE5B,YAAY,EAAE,GAAG,IAAI;IAC7B,MAAM,EAAEyB,IAAI,EAAEI,SAAS,EAAE,GAAG,IAAI,CAACC,UAAU;IAC3C,MAAM,EAAE1B,MAAMM,YAAY,EAAE,GAAGf,wBAAwBK;IAEvD,IAAIe,OAAO;IACX,IAAIc,cAAc,KAAK;QACrB,IAAInB,iBAAiB,YAAYA,iBAAiB,YAAY;YAC5DK,OAAOO,wBAAwBtB;QACjC,OAAO,IAAIU,iBAAiB,WAAW;YACrCK,OAAOS,2BAA2BxB,cAAcyB;QAClD,OAAO;YACLV,OAAOQ,yBAAyBvB;QAClC;IACF,OAAO;QACLe,OAAO,MAAMN,wBAAwBT,cAAcU;IACrD;IAEA,OAAOK;AACT;MAEF,WAAea"}