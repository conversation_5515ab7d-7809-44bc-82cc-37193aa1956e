{"version": 3, "sources": ["../../../../src/lib/metadata/generate/basic.tsx"], "names": ["React", "Meta", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MultiMeta", "ViewportMetaKeys", "resolveViewportLayout", "viewport", "resolved", "viewportKey_", "viewportKey", "value", "ViewportMeta", "name", "content", "themeColor", "map", "color", "media", "colorScheme", "BasicMeta", "metadata", "meta", "charSet", "title", "absolute", "description", "applicationName", "authors", "author", "url", "link", "rel", "href", "toString", "manifest", "generator", "keywords", "join", "referrer", "creator", "publisher", "robots", "basic", "googleBot", "abstract", "archives", "archive", "assets", "asset", "bookmarks", "bookmark", "category", "classification", "other", "Object", "entries", "Array", "isArray", "ItunesMeta", "itunes", "appId", "appArgument", "formatDetectionKeys", "FormatDetectionMeta", "formatDetection", "key", "AppleWebAppMeta", "appleWebApp", "capable", "startupImage", "statusBarStyle", "image", "VerificationMeta", "verification", "namePrefix", "contents", "google", "yahoo", "yandex", "me"], "mappings": "AAOA,OAAOA,WAAW,QAAO;AACzB,SAASC,IAAI,EAAEC,UAAU,EAAEC,SAAS,QAAQ,SAAQ;AACpD,SAASC,gBAAgB,QAAQ,eAAc;AAE/C,0DAA0D;AAC1D,SAASC,sBAAsBC,QAAkB;IAC/C,IAAIC,WAA0B;IAE9B,IAAID,YAAY,OAAOA,aAAa,UAAU;QAC5CC,WAAW;QACX,IAAK,MAAMC,gBAAgBJ,iBAAkB;YAC3C,MAAMK,cAAcD;YACpB,IAAIC,eAAeH,UAAU;gBAC3B,IAAII,QAAQJ,QAAQ,CAACG,YAAY;gBACjC,IAAI,OAAOC,UAAU,WAAWA,QAAQA,QAAQ,QAAQ;gBACxD,IAAIH,UAAUA,YAAY;gBAC1BA,YAAY,CAAC,EAAEH,gBAAgB,CAACK,YAAY,CAAC,CAAC,EAAEC,MAAM,CAAC;YACzD;QACF;IACF;IACA,OAAOH;AACT;AAEA,OAAO,SAASI,aAAa,EAAEL,QAAQ,EAAkC;IACvE,OAAOJ,WAAW;QAChBD,KAAK;YAAEW,MAAM;YAAYC,SAASR,sBAAsBC;QAAU;WAC9DA,SAASQ,UAAU,GACnBR,SAASQ,UAAU,CAACC,GAAG,CAAC,CAACD,aACvBb,KAAK;gBACHW,MAAM;gBACNC,SAASC,WAAWE,KAAK;gBACzBC,OAAOH,WAAWG,KAAK;YACzB,MAEF,EAAE;QACNhB,KAAK;YAAEW,MAAM;YAAgBC,SAASP,SAASY,WAAW;QAAC;KAC5D;AACH;AAEA,OAAO,SAASC,UAAU,EAAEC,QAAQ,EAAkC;QAoBhCA,oBAIFA,kBACGA;IAxBrC,OAAOlB,WAAW;sBAChB,oBAACmB;YAAKC,SAAQ;;QACdF,SAASG,KAAK,KAAK,QAAQH,SAASG,KAAK,CAACC,QAAQ,iBAChD,oBAACD,eAAOH,SAASG,KAAK,CAACC,QAAQ,IAC7B;QACJvB,KAAK;YAAEW,MAAM;YAAeC,SAASO,SAASK,WAAW;QAAC;QAC1DxB,KAAK;YAAEW,MAAM;YAAoBC,SAASO,SAASM,eAAe;QAAC;WAC/DN,SAASO,OAAO,GAChBP,SAASO,OAAO,CAACZ,GAAG,CAAC,CAACa,SAAW;gBAC/BA,OAAOC,GAAG,iBACR,oBAACC;oBAAKC,KAAI;oBAASC,MAAMJ,OAAOC,GAAG,CAACI,QAAQ;qBAC1C;gBACJhC,KAAK;oBAAEW,MAAM;oBAAUC,SAASe,OAAOhB,IAAI;gBAAC;aAC7C,IACD,EAAE;QACNQ,SAASc,QAAQ,iBACf,oBAACJ;YAAKC,KAAI;YAAWC,MAAMZ,SAASc,QAAQ,CAACD,QAAQ;aACnD;QACJhC,KAAK;YAAEW,MAAM;YAAaC,SAASO,SAASe,SAAS;QAAC;QACtDlC,KAAK;YAAEW,MAAM;YAAYC,OAAO,GAAEO,qBAAAA,SAASgB,QAAQ,qBAAjBhB,mBAAmBiB,IAAI,CAAC;QAAK;QAC/DpC,KAAK;YAAEW,MAAM;YAAYC,SAASO,SAASkB,QAAQ;QAAC;QACpDrC,KAAK;YAAEW,MAAM;YAAWC,SAASO,SAASmB,OAAO;QAAC;QAClDtC,KAAK;YAAEW,MAAM;YAAaC,SAASO,SAASoB,SAAS;QAAC;QACtDvC,KAAK;YAAEW,MAAM;YAAUC,OAAO,GAAEO,mBAAAA,SAASqB,MAAM,qBAAfrB,iBAAiBsB,KAAK;QAAC;QACvDzC,KAAK;YAAEW,MAAM;YAAaC,OAAO,GAAEO,oBAAAA,SAASqB,MAAM,qBAAfrB,kBAAiBuB,SAAS;QAAC;QAC9D1C,KAAK;YAAEW,MAAM;YAAYC,SAASO,SAASwB,QAAQ;QAAC;WAChDxB,SAASyB,QAAQ,GACjBzB,SAASyB,QAAQ,CAAC9B,GAAG,CAAC,CAAC+B,wBACrB,oBAAChB;gBAAKC,KAAI;gBAAWC,MAAMc;kBAE7B,EAAE;WACF1B,SAAS2B,MAAM,GACf3B,SAAS2B,MAAM,CAAChC,GAAG,CAAC,CAACiC,sBAAU,oBAAClB;gBAAKC,KAAI;gBAASC,MAAMgB;kBACxD,EAAE;WACF5B,SAAS6B,SAAS,GAClB7B,SAAS6B,SAAS,CAAClC,GAAG,CAAC,CAACmC,yBACtB,oBAACpB;gBAAKC,KAAI;gBAAYC,MAAMkB;kBAE9B,EAAE;QACNjD,KAAK;YAAEW,MAAM;YAAYC,SAASO,SAAS+B,QAAQ;QAAC;QACpDlD,KAAK;YAAEW,MAAM;YAAkBC,SAASO,SAASgC,cAAc;QAAC;WAC5DhC,SAASiC,KAAK,GACdC,OAAOC,OAAO,CAACnC,SAASiC,KAAK,EAAEtC,GAAG,CAAC,CAAC,CAACH,MAAMC,QAAQ,GACjDZ,KAAK;gBACHW;gBACAC,SAAS2C,MAAMC,OAAO,CAAC5C,WAAWA,QAAQwB,IAAI,CAAC,OAAOxB;YACxD,MAEF,EAAE;KACP;AACH;AAEA,OAAO,SAAS6C,WAAW,EAAEC,MAAM,EAA0C;IAC3E,IAAI,CAACA,QAAQ,OAAO;IACpB,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE,GAAGF;IAC/B,IAAI9C,UAAU,CAAC,OAAO,EAAE+C,MAAM,CAAC;IAC/B,IAAIC,aAAa;QACfhD,WAAW,CAAC,eAAe,EAAEgD,YAAY,CAAC;IAC5C;IACA,qBAAO,oBAACxC;QAAKT,MAAK;QAAmBC,SAASA;;AAChD;AAEA,MAAMiD,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AACD,OAAO,SAASC,oBAAoB,EAClCC,eAAe,EAGhB;IACC,IAAI,CAACA,iBAAiB,OAAO;IAC7B,IAAInD,UAAU;IACd,KAAK,MAAMoD,OAAOH,oBAAqB;QACrC,IAAIG,OAAOD,iBAAiB;YAC1B,IAAInD,SAASA,WAAW;YACxBA,WAAW,CAAC,EAAEoD,IAAI,GAAG,CAAC;QACxB;IACF;IACA,qBAAO,oBAAC5C;QAAKT,MAAK;QAAmBC,SAASA;;AAChD;AAEA,OAAO,SAASqD,gBAAgB,EAC9BC,WAAW,EAGZ;IACC,IAAI,CAACA,aAAa,OAAO;IAEzB,MAAM,EAAEC,OAAO,EAAE7C,KAAK,EAAE8C,YAAY,EAAEC,cAAc,EAAE,GAAGH;IAEzD,OAAOjE,WAAW;QAChBkE,UACInE,KAAK;YAAEW,MAAM;YAAgCC,SAAS;QAAM,KAC5D;QACJZ,KAAK;YAAEW,MAAM;YAA8BC,SAASU;QAAM;QAC1D8C,eACIA,aAAatD,GAAG,CAAC,CAACwD,sBAChB,oBAACzC;gBACCE,MAAMuC,MAAM1C,GAAG;gBACfZ,OAAOsD,MAAMtD,KAAK;gBAClBc,KAAI;kBAGR;QACJuC,iBACIrE,KAAK;YACHW,MAAM;YACNC,SAASyD;QACX,KACA;KACL;AACH;AAEA,OAAO,SAASE,iBAAiB,EAC/BC,YAAY,EAGb;IACC,IAAI,CAACA,cAAc,OAAO;IAE1B,OAAOvE,WAAW;QAChBC,UAAU;YACRuE,YAAY;YACZC,UAAUF,aAAaG,MAAM;QAC/B;QACAzE,UAAU;YAAEuE,YAAY;YAASC,UAAUF,aAAaI,KAAK;QAAC;QAC9D1E,UAAU;YACRuE,YAAY;YACZC,UAAUF,aAAaK,MAAM;QAC/B;QACA3E,UAAU;YAAEuE,YAAY;YAAMC,UAAUF,aAAaM,EAAE;QAAC;WACpDN,aAAapB,KAAK,GAClBC,OAAOC,OAAO,CAACkB,aAAapB,KAAK,EAAEtC,GAAG,CAAC,CAAC,CAACkD,KAAKvD,MAAM,GAClDP,UAAU;gBAAEuE,YAAYT;gBAAKU,UAAUjE;YAAM,MAE/C,EAAE;KACP;AACH"}