{"version": 3, "sources": ["../../src/lib/flat-readdir.ts"], "names": ["flatReaddir", "dir", "includes", "dirents", "fs", "opendir", "result", "part", "shouldOmit", "isDirectory", "some", "include", "test", "name", "isSymbolicLink", "stats", "stat", "join", "push"], "mappings": ";;;;+BAGsBA;;;eAAAA;;;sBAHD;iEACN;;;;;;AAER,eAAeA,YAAYC,GAAW,EAAEC,QAAkB;IAC/D,MAAMC,UAAU,MAAMC,iBAAE,CAACC,OAAO,CAACJ;IACjC,MAAMK,SAAS,EAAE;IAEjB,WAAW,MAAMC,QAAQJ,QAAS;QAChC,IAAIK,aACFD,KAAKE,WAAW,MAAM,CAACP,SAASQ,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAACL,KAAKM,IAAI;QAE1E,IAAIN,KAAKO,cAAc,IAAI;YACzB,MAAMC,QAAQ,MAAMX,iBAAE,CAACY,IAAI,CAACC,IAAAA,UAAI,EAAChB,KAAKM,KAAKM,IAAI;YAC/CL,aAAaO,MAAMN,WAAW;QAChC;QAEA,IAAI,CAACD,YAAY;YACfF,OAAOY,IAAI,CAACD,IAAAA,UAAI,EAAChB,KAAKM,KAAKM,IAAI;QACjC;IACF;IAEA,OAAOP;AACT"}