{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-data-property.ts"], "names": ["CacheStates", "createRouterCache<PERSON>ey", "fillCacheWithDataProperty", "newCache", "existingCache", "flightSegmentPath", "fetchResponse", "bailOnParallelRoutes", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "size", "bailOptimistic", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "data", "status", "DATA_FETCH", "subTreeData", "slice"], "mappings": "AAGA,SAASA,WAAW,QAAQ,wDAAuD;AAEnF,SAASC,oBAAoB,QAAQ,4BAA2B;AAEhE;;CAEC,GACD,OAAO,SAASC,0BACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC,EACpCC,aAA8D,EAC9DC,oBAAqC;IAArCA,IAAAA,iCAAAA,uBAAgC;IAEhC,MAAMC,cAAcH,kBAAkBI,MAAM,IAAI;IAEhD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGN;IACpC,MAAMO,WAAWX,qBAAqBU;IAEtC,MAAME,0BACJT,cAAcU,cAAc,CAACC,GAAG,CAACL;IAEnC,IACE,CAACG,2BACAN,wBAAwBH,cAAcU,cAAc,CAACE,IAAI,GAAG,GAC7D;QACA,6EAA6E;QAC7E,qDAAqD;QACrD,sEAAsE;QACtE,OAAO;YAAEC,gBAAgB;QAAK;IAChC;IAEA,IAAIC,kBAAkBf,SAASW,cAAc,CAACC,GAAG,CAACL;IAElD,IAAI,CAACQ,mBAAmBA,oBAAoBL,yBAAyB;QACnEK,kBAAkB,IAAIC,IAAIN;QAC1BV,SAASW,cAAc,CAACM,GAAG,CAACV,kBAAkBQ;IAChD;IAEA,MAAMG,yBAAyBR,wBAAwBE,GAAG,CAACH;IAC3D,IAAIU,iBAAiBJ,gBAAgBH,GAAG,CAACH;IAEzC,yFAAyF;IACzF,IAAIJ,aAAa;QACf,IACE,CAACc,kBACD,CAACA,eAAeC,IAAI,IACpBD,mBAAmBD,wBACnB;YACAH,gBAAgBE,GAAG,CAACR,UAAU;gBAC5BY,QAAQxB,YAAYyB,UAAU;gBAC9BF,MAAMjB;gBACNoB,aAAa;gBACbZ,gBAAgB,IAAIK;YACtB;QACF;QACA;IACF;IAEA,IAAI,CAACG,kBAAkB,CAACD,wBAAwB;QAC9C,+EAA+E;QAC/E,IAAI,CAACC,gBAAgB;YACnBJ,gBAAgBE,GAAG,CAACR,UAAU;gBAC5BY,QAAQxB,YAAYyB,UAAU;gBAC9BF,MAAMjB;gBACNoB,aAAa;gBACbZ,gBAAgB,IAAIK;YACtB;QACF;QACA;IACF;IAEA,IAAIG,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfE,QAAQF,eAAeE,MAAM;YAC7BD,MAAMD,eAAeC,IAAI;YACzBG,aAAaJ,eAAeI,WAAW;YACvCZ,gBAAgB,IAAIK,IAAIG,eAAeR,cAAc;QACvD;QACAI,gBAAgBE,GAAG,CAACR,UAAUU;IAChC;IAEA,OAAOpB,0BACLoB,gBACAD,wBACAhB,kBAAkBsB,KAAK,CAAC,IACxBrB;AAEJ"}