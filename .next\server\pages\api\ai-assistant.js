"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ai-assistant";
exports.ids = ["pages/api/ai-assistant"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai-assistant&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cai-assistant.js&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai-assistant&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cai-assistant.js&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_ai_assistant_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\ai-assistant.js */ \"(api)/./pages/api/ai-assistant.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_assistant_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_ai_assistant_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ai-assistant\",\n        pathname: \"/api/ai-assistant\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_ai_assistant_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmFpLWFzc2lzdGFudCZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTVDYXBpJTVDYWktYXNzaXN0YW50LmpzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNMO0FBQzFEO0FBQzBEO0FBQzFEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyx1REFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsdURBQVE7QUFDcEM7QUFDTyx3QkFBd0IsZ0hBQW1CO0FBQ2xEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFzay1tYW5hZ2VtZW50LWFwcC8/NzdkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXNcXFxcYXBpXFxcXGFpLWFzc2lzdGFudC5qc1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2FpLWFzc2lzdGFudFwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2FpLWFzc2lzdGFudFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai-assistant&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cai-assistant.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/openrouter-config.js":
/*!**********************************!*\
  !*** ./lib/openrouter-config.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChatCompletion: () => (/* binding */ createChatCompletion),\n/* harmony export */   getOpenRouterHeaders: () => (/* binding */ getOpenRouterHeaders),\n/* harmony export */   openRouterConfig: () => (/* binding */ openRouterConfig),\n/* harmony export */   parseTaskCommand: () => (/* binding */ parseTaskCommand)\n/* harmony export */ });\nconst OPENROUTER_API_URL = \"https://openrouter.ai/api/v1/chat/completions\";\nconst openRouterConfig = {\n    apiUrl: OPENROUTER_API_URL,\n    model: \"anthropic/claude-sonnet-4\"\n};\nconst getOpenRouterHeaders = (apiKey)=>({\n        \"Authorization\": `Bearer ${apiKey}`,\n        \"Content-Type\": \"application/json\",\n        \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n        \"X-Title\": \"Task Management App\"\n    });\nconst createChatCompletion = async (messages, systemPrompt = \"\", apiKey)=>{\n    try {\n        const response = await fetch(OPENROUTER_API_URL, {\n            method: \"POST\",\n            headers: getOpenRouterHeaders(apiKey),\n            body: JSON.stringify({\n                model: openRouterConfig.model,\n                messages: [\n                    ...systemPrompt ? [\n                        {\n                            role: \"system\",\n                            content: systemPrompt\n                        }\n                    ] : [],\n                    ...messages\n                ],\n                temperature: 0.7,\n                max_tokens: 1000\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);\n        }\n        const data = await response.json();\n        return data.choices[0].message.content;\n    } catch (error) {\n        console.error(\"Error calling OpenRouter API:\", error);\n        throw error;\n    }\n};\nconst parseTaskCommand = (userInput)=>{\n    const input = userInput.toLowerCase().trim();\n    // Enhanced create task patterns\n    if (input.includes(\"create\") && (input.includes(\"task\") || input.includes(\"todo\"))) {\n        const patterns = [\n            /create.*(?:task|todo).*called\\s+[\"']?([^\"']+)[\"']?/i,\n            /create.*[\"']?([^\"']+)[\"']?.*(?:task|todo)/i,\n            /add.*(?:task|todo).*[\"']?([^\"']+)[\"']?/i,\n            /new.*(?:task|todo).*[\"']?([^\"']+)[\"']?/i\n        ];\n        for (const pattern of patterns){\n            const match = input.match(pattern);\n            if (match) {\n                return {\n                    action: \"create\",\n                    taskName: match[1].trim(),\n                    column: \"todo\"\n                };\n            }\n        }\n    }\n    // Enhanced move task patterns\n    if (input.includes(\"move\") || input.includes(\"change\") || input.includes(\"set\")) {\n        const taskPatterns = [\n            /(?:move|change|set).*task\\s+[\"']?([^\"']+)[\"']?.*(?:to|into)\\s+(to do|todo|in progress|progress|done|completed)/i,\n            /(?:move|change|set).*[\"']?([^\"']+)[\"']?.*(?:to|into)\\s+(to do|todo|in progress|progress|done|completed)/i\n        ];\n        for (const pattern of taskPatterns){\n            const match = input.match(pattern);\n            if (match) {\n                const columnMap = {\n                    \"to do\": \"todo\",\n                    \"todo\": \"todo\",\n                    \"in progress\": \"inprogress\",\n                    \"progress\": \"inprogress\",\n                    \"done\": \"done\",\n                    \"completed\": \"done\"\n                };\n                return {\n                    action: \"move\",\n                    taskName: match[1].trim(),\n                    column: columnMap[match[2].toLowerCase()]\n                };\n            }\n        }\n    }\n    // Enhanced delete task patterns\n    if (input.includes(\"delete\") || input.includes(\"remove\")) {\n        const patterns = [\n            /(?:delete|remove).*task.*[\"']?([^\"']+)[\"']?/i,\n            /(?:delete|remove).*[\"']?([^\"']+)[\"']?.*task/i\n        ];\n        for (const pattern of patterns){\n            const match = input.match(pattern);\n            if (match) {\n                return {\n                    action: \"delete\",\n                    taskName: match[1].trim()\n                };\n            }\n        }\n    }\n    return {\n        action: \"chat\",\n        message: userInput\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/openrouter-config.js\n");

/***/ }),

/***/ "(api)/./pages/api/ai-assistant.js":
/*!***********************************!*\
  !*** ./pages/api/ai-assistant.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_openrouter_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/openrouter-config */ \"(api)/./lib/openrouter-config.js\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const { message, context } = req.body;\n        if (!message) {\n            return res.status(400).json({\n                message: \"Message is required\"\n            });\n        }\n        const systemPrompt = `You are a helpful task management assistant. You can help users create, update, move, and delete tasks. You have access to the user's current tasks and can provide insights and suggestions.\n\nCurrent user context:\n- Total tasks: ${context?.taskCount || 0}\n- Tasks in To Do: ${context?.todoCount || 0}\n- Tasks in Progress: ${context?.inProgressCount || 0}\n- Tasks completed: ${context?.doneCount || 0}\n\nYou should be concise, helpful, and focused on task management. If the user asks you to perform actions on tasks, provide clear instructions or confirmations.`;\n        const messages = [\n            {\n                role: \"user\",\n                content: message\n            }\n        ];\n        const response = await (0,_lib_openrouter_config__WEBPACK_IMPORTED_MODULE_0__.createChatCompletion)(messages, systemPrompt, process.env.OPENROUTER_API_KEY);\n        res.status(200).json({\n            response\n        });\n    } catch (error) {\n        console.error(\"AI Assistant API error:\", error);\n        res.status(500).json({\n            message: \"Failed to process request\",\n            error: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9wYWdlcy9hcGkvYWktYXNzaXN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1FO0FBRXBELGVBQWVDLFFBQVFDLEdBQUcsRUFBRUMsR0FBRztJQUM1QyxJQUFJRCxJQUFJRSxNQUFNLEtBQUssUUFBUTtRQUN6QixPQUFPRCxJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1lBQUVDLFNBQVM7UUFBcUI7SUFDOUQ7SUFFQSxJQUFJO1FBQ0YsTUFBTSxFQUFFQSxPQUFPLEVBQUVDLE9BQU8sRUFBRSxHQUFHTixJQUFJTyxJQUFJO1FBRXJDLElBQUksQ0FBQ0YsU0FBUztZQUNaLE9BQU9KLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7Z0JBQUVDLFNBQVM7WUFBc0I7UUFDL0Q7UUFFQSxNQUFNRyxlQUFlLENBQUM7OztlQUdYLEVBQUVGLFNBQVNHLGFBQWEsRUFBRTtrQkFDdkIsRUFBRUgsU0FBU0ksYUFBYSxFQUFFO3FCQUN2QixFQUFFSixTQUFTSyxtQkFBbUIsRUFBRTttQkFDbEMsRUFBRUwsU0FBU00sYUFBYSxFQUFFOzs4SkFFaUgsQ0FBQztRQUUzSixNQUFNQyxXQUFXO1lBQ2Y7Z0JBQUVDLE1BQU07Z0JBQVFDLFNBQVNWO1lBQVE7U0FDbEM7UUFFRCxNQUFNVyxXQUFXLE1BQU1sQiw0RUFBb0JBLENBQUNlLFVBQVVMLGNBQWNTLFFBQVFDLEdBQUcsQ0FBQ0Msa0JBQWtCO1FBRWxHbEIsSUFBSUUsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztZQUFFWTtRQUFTO0lBQ2xDLEVBQUUsT0FBT0ksT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtRQUN6Q25CLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7WUFDbkJDLFNBQVM7WUFDVGUsT0FBT0EsTUFBTWYsT0FBTztRQUN0QjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXNrLW1hbmFnZW1lbnQtYXBwLy4vcGFnZXMvYXBpL2FpLWFzc2lzdGFudC5qcz9mNTU2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNoYXRDb21wbGV0aW9uIH0gZnJvbSAnLi4vLi4vbGliL29wZW5yb3V0ZXItY29uZmlnJztcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gaGFuZGxlcihyZXEsIHJlcykge1xuICBpZiAocmVxLm1ldGhvZCAhPT0gJ1BPU1QnKSB7XG4gICAgcmV0dXJuIHJlcy5zdGF0dXMoNDA1KS5qc29uKHsgbWVzc2FnZTogJ01ldGhvZCBub3QgYWxsb3dlZCcgfSk7XG4gIH1cblxuICB0cnkge1xuICAgIGNvbnN0IHsgbWVzc2FnZSwgY29udGV4dCB9ID0gcmVxLmJvZHk7XG5cbiAgICBpZiAoIW1lc3NhZ2UpIHtcbiAgICAgIHJldHVybiByZXMuc3RhdHVzKDQwMCkuanNvbih7IG1lc3NhZ2U6ICdNZXNzYWdlIGlzIHJlcXVpcmVkJyB9KTtcbiAgICB9XG5cbiAgICBjb25zdCBzeXN0ZW1Qcm9tcHQgPSBgWW91IGFyZSBhIGhlbHBmdWwgdGFzayBtYW5hZ2VtZW50IGFzc2lzdGFudC4gWW91IGNhbiBoZWxwIHVzZXJzIGNyZWF0ZSwgdXBkYXRlLCBtb3ZlLCBhbmQgZGVsZXRlIHRhc2tzLiBZb3UgaGF2ZSBhY2Nlc3MgdG8gdGhlIHVzZXIncyBjdXJyZW50IHRhc2tzIGFuZCBjYW4gcHJvdmlkZSBpbnNpZ2h0cyBhbmQgc3VnZ2VzdGlvbnMuXG5cbkN1cnJlbnQgdXNlciBjb250ZXh0OlxuLSBUb3RhbCB0YXNrczogJHtjb250ZXh0Py50YXNrQ291bnQgfHwgMH1cbi0gVGFza3MgaW4gVG8gRG86ICR7Y29udGV4dD8udG9kb0NvdW50IHx8IDB9XG4tIFRhc2tzIGluIFByb2dyZXNzOiAke2NvbnRleHQ/LmluUHJvZ3Jlc3NDb3VudCB8fCAwfVxuLSBUYXNrcyBjb21wbGV0ZWQ6ICR7Y29udGV4dD8uZG9uZUNvdW50IHx8IDB9XG5cbllvdSBzaG91bGQgYmUgY29uY2lzZSwgaGVscGZ1bCwgYW5kIGZvY3VzZWQgb24gdGFzayBtYW5hZ2VtZW50LiBJZiB0aGUgdXNlciBhc2tzIHlvdSB0byBwZXJmb3JtIGFjdGlvbnMgb24gdGFza3MsIHByb3ZpZGUgY2xlYXIgaW5zdHJ1Y3Rpb25zIG9yIGNvbmZpcm1hdGlvbnMuYDtcblxuICAgIGNvbnN0IG1lc3NhZ2VzID0gW1xuICAgICAgeyByb2xlOiAndXNlcicsIGNvbnRlbnQ6IG1lc3NhZ2UgfVxuICAgIF07XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNyZWF0ZUNoYXRDb21wbGV0aW9uKG1lc3NhZ2VzLCBzeXN0ZW1Qcm9tcHQsIHByb2Nlc3MuZW52Lk9QRU5ST1VURVJfQVBJX0tFWSk7XG5cbiAgICByZXMuc3RhdHVzKDIwMCkuanNvbih7IHJlc3BvbnNlIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0FJIEFzc2lzdGFudCBBUEkgZXJyb3I6JywgZXJyb3IpO1xuICAgIHJlcy5zdGF0dXMoNTAwKS5qc29uKHsgXG4gICAgICBtZXNzYWdlOiAnRmFpbGVkIHRvIHByb2Nlc3MgcmVxdWVzdCcsXG4gICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSBcbiAgICB9KTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNoYXRDb21wbGV0aW9uIiwiaGFuZGxlciIsInJlcSIsInJlcyIsIm1ldGhvZCIsInN0YXR1cyIsImpzb24iLCJtZXNzYWdlIiwiY29udGV4dCIsImJvZHkiLCJzeXN0ZW1Qcm9tcHQiLCJ0YXNrQ291bnQiLCJ0b2RvQ291bnQiLCJpblByb2dyZXNzQ291bnQiLCJkb25lQ291bnQiLCJtZXNzYWdlcyIsInJvbGUiLCJjb250ZW50IiwicmVzcG9uc2UiLCJwcm9jZXNzIiwiZW52IiwiT1BFTlJPVVRFUl9BUElfS0VZIiwiZXJyb3IiLCJjb25zb2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./pages/api/ai-assistant.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai-assistant&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cai-assistant.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();