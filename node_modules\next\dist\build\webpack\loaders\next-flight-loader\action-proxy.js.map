{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/action-proxy.ts"], "names": ["createActionProxy", "SERVER_REFERENCE_TAG", "Symbol", "for", "id", "boundArgsFromClosure", "action", "originalAction", "bindImpl", "_", "boundArgs", "currentAction", "newAction", "args", "$$bound", "concat", "key", "bind", "Object", "defineProperties", "$$typeof", "value", "$$id"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAFhB,MAAMC,uBAAuBC,OAAOC,GAAG,CAAC;AAEjC,SAASH,kBACdI,EAAU,EACVC,oBAAkC,EAClCC,MAAW,EACXC,cAAoB;IAEpB,SAASC,SAAoBC,CAAM,EAAE,GAAGC,SAAgB;QACtD,MAAMC,gBAAgB,IAAI;QAE1B,MAAMC,YAAY,eAAgB,GAAGC,IAAW;YAC9C,IAAIN,gBAAgB;gBAClB,OAAOA,eAAeK,UAAUE,OAAO,CAACC,MAAM,CAACF;YACjD,OAAO;gBACL,+DAA+D;gBAC/D,OAAOF,iBAAiBC,UAAUE,OAAO,KAAKD;YAChD;QACF;QAEA,KAAK,MAAMG,OAAO;YAAC;YAAY;YAAQ;SAAgB,CAAE;YACvD,aAAa;YACbJ,SAAS,CAACI,IAAI,GAAGL,aAAa,CAACK,IAAI;QACrC;QAEA,cAAc;QACdJ,UAAUE,OAAO,GAAG,AAACH,CAAAA,cAAcG,OAAO,IAAI,EAAE,AAAD,EAAGC,MAAM,CAACL;QAEzD,qBAAqB;QACrBE,UAAUK,IAAI,GAAGT,SAASS,IAAI,CAACL;QAE/B,OAAOA;IACT;IAEAM,OAAOC,gBAAgB,CAACb,QAAQ;QAC9Bc,UAAU;YACRC,OAAOpB;QACT;QACAqB,MAAM;YACJD,OAAOjB;QACT;QACAU,SAAS;YACPO,OAAOhB;QACT;QACAY,MAAM;YACJI,OAAOb;QACT;IACF;AACF"}