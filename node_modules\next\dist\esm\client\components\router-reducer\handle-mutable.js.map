{"version": 3, "sources": ["../../../../src/client/components/router-reducer/handle-mutable.ts"], "names": ["computeChangedPath", "handleMutable", "state", "mutable", "shouldScroll", "buildId", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "focusAndScrollRef", "apply", "scrollableSegments", "undefined", "onlyHashChange", "hashFragment", "split", "decodeURIComponent", "slice", "segmentPaths", "cache", "prefetchCache", "tree", "patchedTree", "nextUrl"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAO3D,OAAO,SAASC,cACdC,KAA2B,EAC3BC,OAAgB;QAmCRA;QAhCaA;IADrB,0DAA0D;IAC1D,MAAMC,eAAeD,CAAAA,wBAAAA,QAAQC,YAAY,YAApBD,wBAAwB;QA2CrCA,6BAaAH;IAtDR,OAAO;QACLK,SAASH,MAAMG,OAAO;QACtB,YAAY;QACZC,cACEH,QAAQG,YAAY,IAAI,OACpBH,QAAQG,YAAY,KAAKJ,MAAMI,YAAY,GACzCJ,MAAMI,YAAY,GAClBH,QAAQG,YAAY,GACtBJ,MAAMI,YAAY;QACxBC,SAAS;YACPC,aACEL,QAAQK,WAAW,IAAI,OACnBL,QAAQK,WAAW,GACnBN,MAAMK,OAAO,CAACC,WAAW;YAC/BC,eACEN,QAAQM,aAAa,IAAI,OACrBN,QAAQM,aAAa,GACrBP,MAAMK,OAAO,CAACE,aAAa;QACnC;QACA,kEAAkE;QAClEC,mBAAmB;YACjBC,OAAOP,eACHD,CAAAA,2BAAAA,QAASS,kBAAkB,MAAKC,YAC9B,OACAX,MAAMQ,iBAAiB,CAACC,KAAK,GAE/B;YACJG,gBACE,CAAC,CAACX,QAAQY,YAAY,IACtBb,MAAMI,YAAY,CAACU,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,OACjCb,wBAAAA,QAAQG,YAAY,qBAApBH,sBAAsBa,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;YAC1CD,cAAcX,eAEV,oCAAoC;YACpCD,QAAQY,YAAY,IAAIZ,QAAQY,YAAY,KAAK,KAE/CE,mBAAmBd,QAAQY,YAAY,CAACG,KAAK,CAAC,MAC9ChB,MAAMQ,iBAAiB,CAACK,YAAY,GAEtC;YACJI,cAAcf,eACVD,CAAAA,8BAAAA,2BAAAA,QAASS,kBAAkB,YAA3BT,8BAA+BD,MAAMQ,iBAAiB,CAACS,YAAY,GAEnE,EAAE;QACR;QACA,eAAe;QACfC,OAAOjB,QAAQiB,KAAK,GAAGjB,QAAQiB,KAAK,GAAGlB,MAAMkB,KAAK;QAClDC,eAAelB,QAAQkB,aAAa,GAChClB,QAAQkB,aAAa,GACrBnB,MAAMmB,aAAa;QACvB,8BAA8B;QAC9BC,MAAMnB,QAAQoB,WAAW,KAAKV,YAAYV,QAAQoB,WAAW,GAAGrB,MAAMoB,IAAI;QAC1EE,SACErB,QAAQoB,WAAW,KAAKV,YACpBb,CAAAA,sBAAAA,mBAAmBE,MAAMoB,IAAI,EAAEnB,QAAQoB,WAAW,aAAlDvB,sBACAE,MAAMI,YAAY,GAClBJ,MAAMsB,OAAO;IACrB;AACF"}