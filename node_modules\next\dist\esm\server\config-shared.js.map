{"version": 3, "sources": ["../../src/server/config-shared.ts"], "names": ["os", "imageConfigDefault", "defaultConfig", "env", "webpack", "eslint", "ignoreDuringBuilds", "typescript", "ignoreBuildErrors", "tsconfigPath", "distDir", "cleanDistDir", "assetPrefix", "config<PERSON><PERSON><PERSON>", "useFileSystemPublicRoutes", "generateBuildId", "generateEtags", "pageExtensions", "poweredByHeader", "compress", "analyticsId", "process", "VERCEL_ANALYTICS_ID", "images", "devIndicators", "buildActivity", "buildActivityPosition", "onDemandEntries", "maxInactiveAge", "pagesBufferLength", "amp", "canonicalBase", "basePath", "sassOptions", "trailingSlash", "i18n", "productionBrowserSourceMaps", "optimizeFonts", "excludeDefaultMomentLocales", "serverRuntimeConfig", "publicRuntimeConfig", "reactProductionProfiling", "reactStrictMode", "httpAgentOptions", "keepAlive", "outputFileTracing", "staticPageGenerationTimeout", "swcMinify", "output", "NEXT_PRIVATE_STANDALONE", "undefined", "modularizeImports", "experimental", "serverMinification", "serverSourceMaps", "caseSensitiveRoutes", "useDeploymentId", "deploymentId", "useDeploymentIdServerActions", "appDocumentPreloading", "clientRouterFilter", "clientRouterFilterRedirects", "fetchCacheKeyPrefix", "middlewarePrefetch", "optimisticClientCache", "manualClientBasePath", "cpus", "Math", "max", "Number", "CIRCLE_NODE_TOTAL", "length", "memoryBasedWorkersCount", "isrFlushToDisk", "workerThreads", "proxyTimeout", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "externalDir", "disableOptimizedLoading", "gzipSize", "craCompat", "esmExternals", "isrMemoryCacheSize", "incremental<PERSON>ache<PERSON>andlerPath", "fullySpecified", "outputFileTracingRoot", "NEXT_PRIVATE_OUTPUT_TRACE_ROOT", "swcTraceProfiling", "forceSwcTransforms", "swcPlugins", "largePageDataBytes", "disablePostcssPresetEnv", "urlImports", "adjustFontFallbacks", "adjustFontFallbacksWithSizeAdjust", "turbo", "turbotrace", "typedRoutes", "instrumentationHook", "bundlePagesExternals", "webpackBuildWorker", "normalizeConfig", "phase", "config"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AAGnB,SAASC,kBAAkB,QAAQ,6BAA4B;AAqqB/D,OAAO,MAAMC,gBAA4B;IACvCC,KAAK,CAAC;IACNC,SAAS;IACTC,QAAQ;QACNC,oBAAoB;IACtB;IACAC,YAAY;QACVC,mBAAmB;QACnBC,cAAc;IAChB;IACAC,SAAS;IACTC,cAAc;IACdC,aAAa;IACbC,cAAc;IACdC,2BAA2B;IAC3BC,iBAAiB,IAAM;IACvBC,eAAe;IACfC,gBAAgB;QAAC;QAAO;QAAM;QAAO;KAAK;IAC1CC,iBAAiB;IACjBC,UAAU;IACVC,aAAaC,QAAQlB,GAAG,CAACmB,mBAAmB,IAAI;IAChDC,QAAQtB;IACRuB,eAAe;QACbC,eAAe;QACfC,uBAAuB;IACzB;IACAC,iBAAiB;QACfC,gBAAgB,KAAK;QACrBC,mBAAmB;IACrB;IACAC,KAAK;QACHC,eAAe;IACjB;IACAC,UAAU;IACVC,aAAa,CAAC;IACdC,eAAe;IACfC,MAAM;IACNC,6BAA6B;IAC7BC,eAAe;IACfC,6BAA6B;IAC7BC,qBAAqB,CAAC;IACtBC,qBAAqB,CAAC;IACtBC,0BAA0B;IAC1BC,iBAAiB;IACjBC,kBAAkB;QAChBC,WAAW;IACb;IACAC,mBAAmB;IACnBC,6BAA6B;IAC7BC,WAAW;IACXC,QAAQ,CAAC,CAAC3B,QAAQlB,GAAG,CAAC8C,uBAAuB,GAAG,eAAeC;IAC/DC,mBAAmBD;IACnBE,cAAc;QACZC,oBAAoB;QACpBC,kBAAkB;QAClBC,qBAAqB;QACrBC,iBAAiB;QACjBC,cAAcP;QACdQ,8BAA8B;QAC9BC,uBAAuBT;QACvBU,oBAAoB;QACpBC,6BAA6B;QAC7BC,qBAAqB;QACrBC,oBAAoB;QACpBC,uBAAuB;QACvBC,sBAAsB;QACtBC,MAAMC,KAAKC,GAAG,CACZ,GACA,AAACC,CAAAA,OAAOhD,QAAQlB,GAAG,CAACmE,iBAAiB,KACnC,AAACtE,CAAAA,GAAGkE,IAAI,MAAM;YAAEK,QAAQ;QAAE,CAAA,EAAGA,MAAM,AAAD,IAAK;QAE3CC,yBAAyB;QACzBC,gBAAgB;QAChBC,eAAe;QACfC,cAAczB;QACd0B,aAAa;QACbC,mBAAmB;QACnBC,mBAAmB;QACnBC,aAAa;QACbC,yBAAyB;QACzBC,UAAU;QACVC,WAAW;QACXC,cAAc;QACd,wBAAwB;QACxBC,oBAAoB,KAAK,OAAO;QAChCC,6BAA6BnC;QAC7BoC,gBAAgB;QAChBC,uBAAuBlE,QAAQlB,GAAG,CAACqF,8BAA8B,IAAI;QACrEC,mBAAmB;QACnBC,oBAAoB;QACpBC,YAAYzC;QACZ0C,oBAAoB,MAAM;QAC1BC,yBAAyB3C;QACzBpB,KAAKoB;QACL4C,YAAY5C;QACZ6C,qBAAqB;QACrBC,mCAAmC;QACnCC,OAAO/C;QACPgD,YAAYhD;QACZiD,aAAa;QACbC,qBAAqB;QACrBC,sBAAsB;QACtBC,oBAAoBpD;IACtB;AACF,EAAC;AAED,OAAO,eAAeqD,gBAAgBC,KAAa,EAAEC,MAAW;IAC9D,IAAI,OAAOA,WAAW,YAAY;QAChCA,SAASA,OAAOD,OAAO;YAAEtG;QAAc;IACzC;IACA,gFAAgF;IAChF,OAAO,MAAMuG;AACf"}