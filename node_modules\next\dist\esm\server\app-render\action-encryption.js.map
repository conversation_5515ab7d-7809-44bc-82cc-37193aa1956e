{"version": 3, "sources": ["../../../src/server/app-render/action-encryption.ts"], "names": ["renderToReadableStream", "decodeReply", "createFromReadableStream", "encodeReply", "streamToString", "arrayBufferToString", "decrypt", "encrypt", "getActionEncryptionKey", "getClientReferenceManifestSingleton", "getServerModuleMap", "stringToUint8Array", "decodeActionBoundArg", "actionId", "arg", "key", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "undefined", "decrypted", "startsWith", "length", "encodeActionBoundArg", "randomBytes", "Uint8Array", "crypto", "getRandomValues", "buffer", "encrypted", "btoa", "encryptActionBoundArgs", "args", "clientReferenceManifestSingleton", "serialized", "clientModules", "decryptActionBoundArgs", "decryped", "deserialized", "ReadableStream", "start", "controller", "enqueue", "TextEncoder", "encode", "close", "ssrManifest", "moduleLoading", "moduleMap", "serverModuleMap", "transformed"], "mappings": "AAAA,oDAAoD,GACpD,OAAO,cAAa;AAEpB,oDAAoD,GACpD,SACEA,sBAAsB,EACtBC,WAAW,QACN,uCAAsC;AAC7C,oDAAoD,GACpD,SACEC,wBAAwB,EACxBC,WAAW,QACN,uCAAsC;AAE7C,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SACEC,mBAAmB,EACnBC,OAAO,EACPC,OAAO,EACPC,sBAAsB,EACtBC,mCAAmC,EACnCC,kBAAkB,EAClBC,kBAAkB,QACb,4BAA2B;AAElC,eAAeC,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMP;IAClB,IAAI,OAAOO,QAAQ,aAAa;QAC9B,MAAM,IAAIC,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKJ;IAC7B,MAAMK,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IACtC,IAAIC,YAAYC,WAAW;QACzB,MAAM,IAAIN,MAAM;IAClB;IAEA,MAAMO,YAAYlB,oBAChB,MAAMC,QAAQS,KAAKJ,mBAAmBQ,UAAUR,mBAAmBU;IAGrE,IAAI,CAACE,UAAUC,UAAU,CAACX,WAAW;QACnC,MAAM,IAAIG,MAAM;IAClB;IAEA,OAAOO,UAAUH,KAAK,CAACP,SAASY,MAAM;AACxC;AAEA,eAAeC,qBAAqBb,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMP;IAClB,IAAIO,QAAQO,WAAW;QACrB,MAAM,IAAIN,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,6BAA6B;IAC7B,MAAMW,cAAc,IAAIC,WAAW;IACnCC,OAAOC,eAAe,CAACH;IACvB,MAAMR,UAAUd,oBAAoBsB,YAAYI,MAAM;IAEtD,MAAMC,YAAY,MAAMzB,QACtBQ,KACAY,aACAhB,mBAAmBE,WAAWC;IAGhC,OAAOmB,KAAKd,UAAUd,oBAAoB2B;AAC5C;AAEA,kDAAkD;AAClD,OAAO,eAAeE,uBAAuBrB,QAAgB,EAAEsB,IAAW;IACxE,MAAMC,mCAAmC3B;IAEzC,oDAAoD;IACpD,MAAM4B,aAAa,MAAMjC,eACvBJ,uBAAuBmC,MAAMC,iCAAiCE,aAAa;IAG7E,gEAAgE;IAChE,gFAAgF;IAChF,iBAAiB;IACjB,MAAMN,YAAY,MAAMN,qBAAqBb,UAAUwB;IAEvD,OAAOL;AACT;AAEA,8DAA8D;AAC9D,OAAO,eAAeO,uBACpB1B,QAAgB,EAChBmB,SAA0B;IAE1B,gEAAgE;IAChE,MAAMQ,WAAW,MAAM5B,qBAAqBC,UAAU,MAAMmB;IAE5D,wDAAwD;IACxD,MAAMS,eAAe,MAAMvC,yBACzB,IAAIwC,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAAC,IAAIC,cAAcC,MAAM,CAACP;YAC5CI,WAAWI,KAAK;QAClB;IACF,IACA;QACEC,aAAa;YACX,0EAA0E;YAC1E,uEAAuE;YACvE,8BAA8B;YAC9B,+CAA+C;YAC/CC,eAAe,CAAC;YAChBC,WAAW,CAAC;QACd;IACF;IAGF,oEAAoE;IACpE,MAAMC,kBAAkB1C;IACxB,MAAM2C,cAAc,MAAMpD,YACxB,MAAME,YAAYsC,eAClBW;IAGF,OAAOC;AACT"}