import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import AuthPage from '../components/Auth/AuthPage';
import DemoMode from '../components/DemoMode';
import Sidebar from '../components/Sidebar';
import KanbanBoard from '../components/KanbanBoard';
import TodoList from '../components/TodoList';
import AIAssistant from '../components/AIAssistant';
import { Kanban, List } from 'lucide-react';

export default function Dashboard() {
  const { user, loading } = useAuth();
  const [activeView, setActiveView] = useState('kanban');
  const [showAI, setShowAI] = useState(false);

  const isDemoMode = process.env.NEXT_PUBLIC_FIREBASE_API_KEY === 'demo-api-key' ||
                     !process.env.NEXT_PUBLIC_FIREBASE_API_KEY;

  if (isDemoMode) {
    return <DemoMode />;
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <AuthPage />;
  }

  return (
    <div className="h-screen flex bg-gray-50">
      <Sidebar onToggleAI={() => setShowAI(!showAI)} />
      
      <div className="flex-1 flex flex-col">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Task Management</h1>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setActiveView('kanban')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  activeView === 'kanban'
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Kanban className="h-4 w-4" />
                <span>Kanban</span>
              </button>
              
              <button
                onClick={() => setActiveView('todo')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  activeView === 'todo'
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <List className="h-4 w-4" />
                <span>Todo List</span>
              </button>
            </div>
          </div>
        </div>
        
        <div className="flex-1 overflow-hidden">
          {activeView === 'kanban' ? <KanbanBoard /> : <TodoList />}
        </div>
      </div>

      <AIAssistant 
        isOpen={showAI} 
        onClose={() => setShowAI(false)} 
      />
    </div>
  );
}
