{"version": 3, "sources": ["../../../src/server/lib/dev-bundler-service.ts"], "names": ["DevBundlerService", "constructor", "bundler", "handler", "ensurePage", "definition", "hotReloader", "logErrorWithOriginalStack", "args", "getFallbackErrorComponents", "buildFallbackError", "page", "clientOnly", "undefined", "getCompilationError", "errors", "getCompilationErrors", "revalidate", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "revalidateOpts", "mocked", "createRequestResponseMocks", "url", "headers", "req", "res", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "unstable_onlyGenerated", "Error"], "mappings": ";;;;+BAUaA;;;eAAAA;;;6BAN8B;AAMpC,MAAMA;IACXC,YACmBC,SACAC,QACjB;uBAFiBD;uBACAC;aAGZC,aAAyD,OAC9DC;YAEA,oDAAoD;YACpD,OAAO,MAAM,IAAI,CAACH,OAAO,CAACI,WAAW,CAACF,UAAU,CAACC;QACnD;aAEOE,4BACL,OAAO,GAAGC;YACR,OAAO,MAAM,IAAI,CAACN,OAAO,CAACK,yBAAyB,IAAIC;QACzD;IAZC;IAcH,MAAaC,6BAA6B;QACxC,MAAM,IAAI,CAACP,OAAO,CAACI,WAAW,CAACI,kBAAkB;QACjD,4DAA4D;QAC5D,8DAA8D;QAC9D,MAAM,IAAI,CAACR,OAAO,CAACI,WAAW,CAACF,UAAU,CAAC;YACxCO,MAAM;YACNC,YAAY;YACZP,YAAYQ;QACd;IACF;IAEA,MAAaC,oBAAoBH,IAAY,EAAE;QAC7C,MAAMI,SAAS,MAAM,IAAI,CAACb,OAAO,CAACI,WAAW,CAACU,oBAAoB,CAACL;QACnE,IAAI,CAACI,QAAQ;QAEb,wCAAwC;QACxC,OAAOA,MAAM,CAAC,EAAE;IAClB;IAEA,MAAaE,WAAW,EACtBC,OAAO,EACPC,iBAAiB,EACjBC,MAAMC,cAAc,EAKrB,EAAE;QACD,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxCC,KAAKN;YACLO,SAASN;QACX;QAEA,MAAM,IAAI,CAAChB,OAAO,CAACmB,OAAOI,GAAG,EAAEJ,OAAOK,GAAG;QACzC,MAAML,OAAOK,GAAG,CAACC,WAAW;QAE5B,IACEN,OAAOK,GAAG,CAACE,SAAS,CAAC,sBAAsB,iBAC3C,CAAEP,CAAAA,OAAOK,GAAG,CAACG,UAAU,KAAK,OAAOT,eAAeU,sBAAsB,AAAD,GACvE;YACA,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAEV,OAAOK,GAAG,CAACG,UAAU,CAAC,CAAC;QAC7D;QAEA,OAAO,CAAC;IACV;AACF"}