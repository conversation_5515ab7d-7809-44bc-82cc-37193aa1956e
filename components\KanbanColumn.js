import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import TaskCard from './TaskCard';

export default function KanbanColumn({ id, title, color, headerColor, tasks }) {
  const { setNodeRef } = useDroppable({
    id: id,
  });

  return (
    <div className={`flex flex-col h-full rounded-xl border-2 border-dashed ${color} p-4 transition-colors`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className={`font-semibold ${headerColor || 'text-gray-900 dark:text-white'}`}>{title}</h3>
        <span className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs px-2.5 py-1 rounded-full font-medium shadow-sm border border-gray-200 dark:border-gray-600">
          {tasks.length}
        </span>
      </div>
      
      <div
        ref={setNodeRef}
        className="flex-1 space-y-3 min-h-[200px]"
      >
        <SortableContext items={tasks.map(task => task.id)} strategy={verticalListSortingStrategy}>
          {tasks.map((task) => (
            <TaskCard key={task.id} task={task} />
          ))}
        </SortableContext>
        
        {tasks.length === 0 && (
          <div className="flex items-center justify-center h-32 text-gray-400 dark:text-gray-500 text-sm">
            Drop tasks here
          </div>
        )}
      </div>
    </div>
  );
}
