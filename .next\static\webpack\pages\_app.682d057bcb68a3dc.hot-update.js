"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./lib/firebase-config.js":
/*!********************************!*\
  !*** ./lib/firebase-config.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   db: function() { return /* binding */ db; }\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"./node_modules/firebase/app/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ\" || 0,\n    authDomain: \"zatconss.firebaseapp.com\" || 0,\n    databaseURL: \"https://zatconss-default-rtdb.firebaseio.com\",\n    projectId: \"zatconss\" || 0,\n    storageBucket: \"zatconss.firebasestorage.app\" || 0,\n    messagingSenderId: \"947257597349\" || 0,\n    appId: \"1:947257597349:web:4f62c8e2bf4952eebe5c4c\" || 0,\n    measurementId: \"G-ZCHBDYX3VW\"\n};\nlet app;\nlet auth;\nlet db;\ntry {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n    auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\n    db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n    if ( true && firebaseConfig.apiKey === \"demo-key\") {\n        console.warn(\"⚠️  Using demo Firebase config. Please set up your Firebase project and update .env.local\");\n    }\n} catch (error) {\n    console.error(\"Firebase initialization error:\", error);\n    console.log(\"\\uD83D\\uDCDD Please check your Firebase configuration in .env.local\");\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (app);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvZmlyZWJhc2UtY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZDO0FBQ2dCO0FBQ2U7QUFFNUUsTUFBTUssaUJBQWlCO0lBQ3JCQyxRQUFRQyx5Q0FBd0MsSUFBSTtJQUNwREcsWUFBWUgsMEJBQTRDLElBQUk7SUFDNURLLGFBQWFMLDhDQUE2QztJQUMxRE8sV0FBV1AsVUFBMkMsSUFBSTtJQUMxRFMsZUFBZVQsOEJBQStDLElBQUk7SUFDbEVXLG1CQUFtQlgsY0FBb0QsSUFBSTtJQUMzRWEsT0FBT2IsMkNBQXVDLElBQUk7SUFDbERlLGVBQWVmLGNBQStDO0FBQ2hFO0FBRUEsSUFBSWlCO0FBQ0osSUFBSUM7QUFDSixJQUFJQztBQUVKLElBQUk7SUFDRkYsTUFBTXhCLDJEQUFhQSxDQUFDSztJQUNwQm9CLE9BQU94QixzREFBT0EsQ0FBQ3VCO0lBQ2ZFLEtBQUt2QixnRUFBWUEsQ0FBQ3FCO0lBRWxCLElBQUlqQixLQUF5QixJQUFpQkYsZUFBZUMsTUFBTSxLQUFLLFlBQVk7UUFDbEZxQixRQUFRQyxJQUFJLENBQUM7SUFDZjtBQUNGLEVBQUUsT0FBT0MsT0FBTztJQUNkRixRQUFRRSxLQUFLLENBQUMsa0NBQWtDQTtJQUNoREYsUUFBUUcsR0FBRyxDQUFDO0FBQ2Q7QUFFb0I7QUFDcEIsK0RBQWVOLEdBQUdBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbGliL2ZpcmViYXNlLWNvbmZpZy5qcz8wOTdlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGluaXRpYWxpemVBcHAgfSBmcm9tICdmaXJlYmFzZS9hcHAnO1xuaW1wb3J0IHsgZ2V0QXV0aCwgY29ubmVjdEF1dGhFbXVsYXRvciB9IGZyb20gJ2ZpcmViYXNlL2F1dGgnO1xuaW1wb3J0IHsgZ2V0RmlyZXN0b3JlLCBjb25uZWN0RmlyZXN0b3JlRW11bGF0b3IgfSBmcm9tICdmaXJlYmFzZS9maXJlc3RvcmUnO1xuXG5jb25zdCBmaXJlYmFzZUNvbmZpZyA9IHtcbiAgYXBpS2V5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9BUElfS0VZIHx8ICdkZW1vLWtleScsXG4gIGF1dGhEb21haW46IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FVVEhfRE9NQUlOIHx8ICdkZW1vLmZpcmViYXNlYXBwLmNvbScsXG4gIGRhdGFiYXNlVVJMOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9EQVRBQkFTRV9VUkwsXG4gIHByb2plY3RJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfUFJPSkVDVF9JRCB8fCAnZGVtby1wcm9qZWN0JyxcbiAgc3RvcmFnZUJ1Y2tldDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfU1RPUkFHRV9CVUNLRVQgfHwgJ2RlbW8uYXBwc3BvdC5jb20nLFxuICBtZXNzYWdpbmdTZW5kZXJJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfTUVTU0FHSU5HX1NFTkRFUl9JRCB8fCAnMTIzNDU2Nzg5JyxcbiAgYXBwSWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FQUF9JRCB8fCAnMToxMjM0NTY3ODk6d2ViOmRlbW8nLFxuICBtZWFzdXJlbWVudElkOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9NRUFTVVJFTUVOVF9JRFxufTtcblxubGV0IGFwcDtcbmxldCBhdXRoO1xubGV0IGRiO1xuXG50cnkge1xuICBhcHAgPSBpbml0aWFsaXplQXBwKGZpcmViYXNlQ29uZmlnKTtcbiAgYXV0aCA9IGdldEF1dGgoYXBwKTtcbiAgZGIgPSBnZXRGaXJlc3RvcmUoYXBwKTtcblxuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgJiYgZmlyZWJhc2VDb25maWcuYXBpS2V5ID09PSAnZGVtby1rZXknKSB7XG4gICAgY29uc29sZS53YXJuKCfimqDvuI8gIFVzaW5nIGRlbW8gRmlyZWJhc2UgY29uZmlnLiBQbGVhc2Ugc2V0IHVwIHlvdXIgRmlyZWJhc2UgcHJvamVjdCBhbmQgdXBkYXRlIC5lbnYubG9jYWwnKTtcbiAgfVxufSBjYXRjaCAoZXJyb3IpIHtcbiAgY29uc29sZS5lcnJvcignRmlyZWJhc2UgaW5pdGlhbGl6YXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICBjb25zb2xlLmxvZygn8J+TnSBQbGVhc2UgY2hlY2sgeW91ciBGaXJlYmFzZSBjb25maWd1cmF0aW9uIGluIC5lbnYubG9jYWwnKTtcbn1cblxuZXhwb3J0IHsgYXV0aCwgZGIgfTtcbmV4cG9ydCBkZWZhdWx0IGFwcDtcbiJdLCJuYW1lcyI6WyJpbml0aWFsaXplQXBwIiwiZ2V0QXV0aCIsImNvbm5lY3RBdXRoRW11bGF0b3IiLCJnZXRGaXJlc3RvcmUiLCJjb25uZWN0RmlyZXN0b3JlRW11bGF0b3IiLCJmaXJlYmFzZUNvbmZpZyIsImFwaUtleSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9BUElfS0VZIiwiYXV0aERvbWFpbiIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FVVEhfRE9NQUlOIiwiZGF0YWJhc2VVUkwiLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9EQVRBQkFTRV9VUkwiLCJwcm9qZWN0SWQiLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9QUk9KRUNUX0lEIiwic3RvcmFnZUJ1Y2tldCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX1NUT1JBR0VfQlVDS0VUIiwibWVzc2FnaW5nU2VuZGVySWQiLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9NRVNTQUdJTkdfU0VOREVSX0lEIiwiYXBwSWQiLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9BUFBfSUQiLCJtZWFzdXJlbWVudElkIiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfTUVBU1VSRU1FTlRfSUQiLCJhcHAiLCJhdXRoIiwiZGIiLCJjb25zb2xlIiwid2FybiIsImVycm9yIiwibG9nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./lib/firebase-config.js\n"));

/***/ })

});