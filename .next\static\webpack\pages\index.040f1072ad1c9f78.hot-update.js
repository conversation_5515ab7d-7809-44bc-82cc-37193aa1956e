"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/KanbanBoard.js":
/*!***********************************!*\
  !*** ./components/KanbanBoard.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KanbanBoard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/core */ \"./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/sortable */ \"./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _KanbanColumn__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./KanbanColumn */ \"./components/KanbanColumn.js\");\n/* harmony import */ var _TaskCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TaskCard */ \"./components/TaskCard.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst columns = [\n    {\n        id: \"todo\",\n        title: \"To Do\",\n        color: \"bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700\",\n        headerColor: \"text-gray-700 dark:text-gray-300\"\n    },\n    {\n        id: \"inprogress\",\n        title: \"In Progress\",\n        color: \"bg-primary/5 dark:bg-primary/10 border-primary/20 dark:border-primary/30\",\n        headerColor: \"text-primary\"\n    },\n    {\n        id: \"done\",\n        title: \"Done\",\n        color: \"bg-accent/10 dark:bg-accent/20 border-accent/30 dark:border-accent/40\",\n        headerColor: \"text-accent\"\n    }\n];\nfunction KanbanBoard() {\n    _s();\n    const { tasks, moveTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    const [activeId, setActiveId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.PointerSensor), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.KeyboardSensor, {\n        coordinateGetter: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.sortableKeyboardCoordinates\n    }));\n    const getTasksByStatus = (status)=>{\n        return tasks.filter((task)=>task.status === status);\n    };\n    const findContainer = (id)=>{\n        if (id in tasks) {\n            return id;\n        }\n        const task = tasks.find((task)=>task.id === id);\n        return task ? task.status : null;\n    };\n    const handleDragStart = (event)=>{\n        const { active } = event;\n        setActiveId(active.id);\n    };\n    const handleDragOver = (event)=>{\n        const { active, over } = event;\n        const overId = over === null || over === void 0 ? void 0 : over.id;\n        if (!overId) return;\n        const activeContainer = findContainer(active.id);\n        const overContainer = findContainer(overId);\n        if (!activeContainer || !overContainer || activeContainer === overContainer) {\n            return;\n        }\n    };\n    const handleDragEnd = async (event)=>{\n        const { active, over } = event;\n        if (!over) {\n            setActiveId(null);\n            return;\n        }\n        const activeId = active.id;\n        const overId = over.id;\n        const activeTask = tasks.find((task)=>task.id === activeId);\n        if (!activeTask) {\n            setActiveId(null);\n            return;\n        }\n        let newStatus = activeTask.status;\n        if (columns.some((col)=>col.id === overId)) {\n            newStatus = overId;\n        } else {\n            const overTask = tasks.find((task)=>task.id === overId);\n            if (overTask) {\n                newStatus = overTask.status;\n            }\n        }\n        if (newStatus !== activeTask.status) {\n            try {\n                await moveTask(activeId, newStatus);\n            } catch (error) {\n                console.error(\"Failed to move task:\", error);\n            }\n        }\n        setActiveId(null);\n    };\n    const activeTask = activeId ? tasks.find((task)=>task.id === activeId) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                        children: \"Task Board\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Drag and drop tasks between columns\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DndContext, {\n                sensors: sensors,\n                collisionDetection: _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.closestCorners,\n                onDragStart: handleDragStart,\n                onDragOver: handleDragOver,\n                onDragEnd: handleDragEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 h-full\",\n                        children: columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.SortableContext, {\n                                items: getTasksByStatus(column.id).map((task)=>task.id),\n                                strategy: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.verticalListSortingStrategy,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_KanbanColumn__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    id: column.id,\n                                    title: column.title,\n                                    color: column.color,\n                                    headerColor: column.headerColor,\n                                    tasks: getTasksByStatus(column.id)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this)\n                            }, column.id, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DragOverlay, {\n                        children: activeTask ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            task: activeTask,\n                            isDragging: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 159,\n                            columnNumber: 25\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(KanbanBoard, \"UcftU53IEUYblNLJeoshLScqsVo=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors\n    ];\n});\n_c = KanbanBoard;\nvar _c;\n$RefreshReg$(_c, \"KanbanBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/KanbanBoard.js\n"));

/***/ })

});