{"version": 3, "sources": ["../../../../src/server/future/route-matcher-managers/dev-route-matcher-manager.ts"], "names": ["RouteKind", "DefaultRouteMatcherManager", "path", "Log", "cyan", "DevRouteMatcherManager", "constructor", "production", "ensurer", "dir", "test", "pathname", "options", "match", "validate", "matcher", "duplicated", "some", "duplicate", "definition", "kind", "APP_PAGE", "APP_ROUTE", "PAGES", "PAGES_API", "matchAll", "reload", "development", "ensure", "matchers", "Object", "entries", "duplicates", "identity", "slice", "warn", "map", "relative", "filename", "join"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAe;AAGzC,SAASC,0BAA0B,QAAQ,kCAAiC;AAE5E,OAAOC,UAAU,sCAAqC;AACtD,YAAYC,SAAS,4BAA2B;AAChD,SAASC,IAAI,QAAQ,0BAAyB;AAO9C,OAAO,MAAMC,+BAA+BJ;IAC1CK,YACmBC,YACAC,SACAC,IACjB;QACA,KAAK;0BAJYF;uBACAC;mBACAC;IAGnB;IAEA,MAAaC,KAAKC,QAAgB,EAAEC,OAAqB,EAAoB;QAC3E,mDAAmD;QACnD,MAAMC,QAAQ,MAAM,KAAK,CAACA,MAAMF,UAAUC;QAE1C,wEAAwE;QACxE,uEAAuE;QACvE,qCAAqC;QACrC,OAAOC,UAAU;IACnB;IAEUC,SACRH,QAAgB,EAChBI,OAAqB,EACrBH,OAAqB,EACF;QACnB,MAAMC,QAAQ,KAAK,CAACC,SAASH,UAAUI,SAASH;QAEhD,0EAA0E;QAC1E,eAAe;QACf,8DAA8D;QAC9D,IACEC,SACAE,QAAQC,UAAU,IAClBD,QAAQC,UAAU,CAACC,IAAI,CACrB,CAACC,YACCA,UAAUC,UAAU,CAACC,IAAI,KAAKpB,UAAUqB,QAAQ,IAChDH,UAAUC,UAAU,CAACC,IAAI,KAAKpB,UAAUsB,SAAS,KAErDP,QAAQC,UAAU,CAACC,IAAI,CACrB,CAACC,YACCA,UAAUC,UAAU,CAACC,IAAI,KAAKpB,UAAUuB,KAAK,IAC7CL,UAAUC,UAAU,CAACC,IAAI,KAAKpB,UAAUwB,SAAS,GAErD;YACA,OAAO;QACT;QAEA,OAAOX;IACT;IAEA,OAAcY,SACZd,QAAgB,EAChBC,OAAqB,EACoD;QACzE,kCAAkC;QAClC,+GAA+G;QAC/G,MAAM,KAAK,CAACc;QAEZ,uEAAuE;QACvE,gBAAgB;QAChB,WAAW,MAAMC,eAAe,KAAK,CAACF,SAASd,UAAUC,SAAU;YACjE,qEAAqE;YACrE,gEAAgE;YAChE,MAAM,IAAI,CAACJ,OAAO,CAACoB,MAAM,CAACD;YAC1B,MAAM,IAAI,CAACpB,UAAU,CAACmB,MAAM;YAE5B,yEAAyE;YACzE,sEAAsE;YACtE,WAAW,MAAMnB,cAAc,IAAI,CAACA,UAAU,CAACkB,QAAQ,CACrDd,UACAC,SACC;gBACD,MAAML;YACR;QACF;QAEA,4EAA4E;QAC5E,gCAAgC;QAChC,OAAO;IACT;IAEA,MAAamB,SAAwB;QACnC,uCAAuC;QACvC,MAAM,IAAI,CAACnB,UAAU,CAACmB,MAAM;QAE5B,kCAAkC;QAClC,MAAM,KAAK,CAACA;QAEZ,wCAAwC;QACxC,KAAK,MAAM,CAACf,UAAUkB,SAAS,IAAIC,OAAOC,OAAO,CAC/C,IAAI,CAACF,QAAQ,CAACG,UAAU,EACvB;YACD,0EAA0E;YAC1E,4BAA4B;YAC5B,MAAMC,WAAWJ,QAAQ,CAAC,EAAE,CAACI,QAAQ;YACrC,IAAIJ,SAASK,KAAK,CAAC,GAAGjB,IAAI,CAAC,CAACF,UAAYA,QAAQkB,QAAQ,KAAKA,WAAW;gBACtE;YACF;YAEA9B,IAAIgC,IAAI,CACN,CAAC,yBAAyB,EAAEN,SACzBO,GAAG,CAAC,CAACrB,UACJX,KAAKF,KAAKmC,QAAQ,CAAC,IAAI,CAAC5B,GAAG,EAAEM,QAAQI,UAAU,CAACmB,QAAQ,IAEzDC,IAAI,CAAC,SAAS,YAAY,EAAEnC,KAAKO,UAAU,CAAC;QAEnD;IACF;AACF"}