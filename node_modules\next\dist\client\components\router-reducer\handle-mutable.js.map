{"version": 3, "sources": ["../../../../src/client/components/router-reducer/handle-mutable.ts"], "names": ["handleMutable", "state", "mutable", "shouldScroll", "computeChangedPath", "buildId", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "focusAndScrollRef", "apply", "scrollableSegments", "undefined", "onlyHashChange", "hashFragment", "split", "decodeURIComponent", "slice", "segmentPaths", "cache", "prefetchCache", "tree", "patchedTree", "nextUrl"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;oCAPmB;AAO5B,SAASA,cACdC,KAA2B,EAC3BC,OAAgB;QAmCRA;QAhCaA;IADrB,0DAA0D;IAC1D,MAAMC,eAAeD,CAAAA,wBAAAA,QAAQC,YAAY,YAApBD,wBAAwB;QA2CrCA,6BAaAE;IAtDR,OAAO;QACLC,SAASJ,MAAMI,OAAO;QACtB,YAAY;QACZC,cACEJ,QAAQI,YAAY,IAAI,OACpBJ,QAAQI,YAAY,KAAKL,MAAMK,YAAY,GACzCL,MAAMK,YAAY,GAClBJ,QAAQI,YAAY,GACtBL,MAAMK,YAAY;QACxBC,SAAS;YACPC,aACEN,QAAQM,WAAW,IAAI,OACnBN,QAAQM,WAAW,GACnBP,MAAMM,OAAO,CAACC,WAAW;YAC/BC,eACEP,QAAQO,aAAa,IAAI,OACrBP,QAAQO,aAAa,GACrBR,MAAMM,OAAO,CAACE,aAAa;QACnC;QACA,kEAAkE;QAClEC,mBAAmB;YACjBC,OAAOR,eACHD,CAAAA,2BAAAA,QAASU,kBAAkB,MAAKC,YAC9B,OACAZ,MAAMS,iBAAiB,CAACC,KAAK,GAE/B;YACJG,gBACE,CAAC,CAACZ,QAAQa,YAAY,IACtBd,MAAMK,YAAY,CAACU,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,OACjCd,wBAAAA,QAAQI,YAAY,qBAApBJ,sBAAsBc,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;YAC1CD,cAAcZ,eAEV,oCAAoC;YACpCD,QAAQa,YAAY,IAAIb,QAAQa,YAAY,KAAK,KAE/CE,mBAAmBf,QAAQa,YAAY,CAACG,KAAK,CAAC,MAC9CjB,MAAMS,iBAAiB,CAACK,YAAY,GAEtC;YACJI,cAAchB,eACVD,CAAAA,8BAAAA,2BAAAA,QAASU,kBAAkB,YAA3BV,8BAA+BD,MAAMS,iBAAiB,CAACS,YAAY,GAEnE,EAAE;QACR;QACA,eAAe;QACfC,OAAOlB,QAAQkB,KAAK,GAAGlB,QAAQkB,KAAK,GAAGnB,MAAMmB,KAAK;QAClDC,eAAenB,QAAQmB,aAAa,GAChCnB,QAAQmB,aAAa,GACrBpB,MAAMoB,aAAa;QACvB,8BAA8B;QAC9BC,MAAMpB,QAAQqB,WAAW,KAAKV,YAAYX,QAAQqB,WAAW,GAAGtB,MAAMqB,IAAI;QAC1EE,SACEtB,QAAQqB,WAAW,KAAKV,YACpBT,CAAAA,sBAAAA,IAAAA,sCAAkB,EAACH,MAAMqB,IAAI,EAAEpB,QAAQqB,WAAW,aAAlDnB,sBACAH,MAAMK,YAAY,GAClBL,MAAMuB,OAAO;IACrB;AACF"}