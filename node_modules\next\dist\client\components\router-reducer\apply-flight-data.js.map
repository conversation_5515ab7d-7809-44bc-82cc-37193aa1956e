{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-flight-data.ts"], "names": ["applyFlightData", "existingCache", "cache", "flightDataPath", "wasPrefetched", "treePatch", "subTreeData", "head", "slice", "length", "status", "CacheStates", "READY", "fillLazyItemsTillLeafWithHead", "parallelRoutes", "Map", "fillCacheWithNewSubTreeData"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;+CANY;+CAGkB;6CACF;AAErC,SAASA,gBACdC,aAAwB,EACxBC,KAAgB,EAChBC,cAA8B,EAC9BC,aAA8B;IAA9BA,IAAAA,0BAAAA,gBAAyB;IAEzB,0DAA0D;IAC1D,MAAM,CAACC,WAAWC,aAAaC,KAAK,GAAGJ,eAAeK,KAAK,CAAC,CAAC;IAE7D,8FAA8F;IAC9F,IAAIF,gBAAgB,MAAM;QACxB,OAAO;IACT;IAEA,IAAIH,eAAeM,MAAM,KAAK,GAAG;QAC/BP,MAAMQ,MAAM,GAAGC,0CAAW,CAACC,KAAK;QAChCV,MAAMI,WAAW,GAAGA;QACpBO,IAAAA,4DAA6B,EAC3BX,OACAD,eACAI,WACAE,MACAH;IAEJ,OAAO;QACL,mDAAmD;QACnDF,MAAMQ,MAAM,GAAGC,0CAAW,CAACC,KAAK;QAChCV,MAAMI,WAAW,GAAGL,cAAcK,WAAW;QAC7CJ,MAAMY,cAAc,GAAG,IAAIC,IAAId,cAAca,cAAc;QAC3D,oEAAoE;QACpEE,IAAAA,wDAA2B,EACzBd,OACAD,eACAE,gBACAC;IAEJ;IAEA,OAAO;AACT"}