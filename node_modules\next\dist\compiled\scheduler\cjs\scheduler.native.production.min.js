/**
 * @license React
 * scheduler.native.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';function f(a,c){var b=a.length;a.push(c);a:for(;0<b;){var d=b-1>>>1,e=a[d];if(0<g(e,c))a[d]=c,a[b]=e,b=d;else break a}}function h(a){return 0===a.length?null:a[0]}function k(a){if(0===a.length)return null;var c=a[0],b=a.pop();if(b!==c){a[0]=b;a:for(var d=0,e=a.length,x=e>>>1;d<x;){var y=2*(d+1)-1,D=a[y],m=y+1,z=a[m];if(0>g(D,b))m<e&&0>g(z,D)?(a[d]=z,a[m]=b,d=m):(a[d]=D,a[y]=b,d=y);else if(m<e&&0>g(z,b))a[d]=z,a[m]=b,d=m;else break a}}return c}
function g(a,c){var b=a.sortIndex-c.sortIndex;return 0!==b?b:a.id-c.id}var l;if("object"===typeof performance&&"function"===typeof performance.now){var n=performance;l=function(){return n.now()}}else{var p=Date,q=p.now();l=function(){return p.now()-q}}var r=[],t=[],u=1,v=null,w=3,A=!1,B=!1,C=!1,E="function"===typeof setTimeout?setTimeout:null,F="function"===typeof clearTimeout?clearTimeout:null,G="undefined"!==typeof setImmediate?setImmediate:null;
"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending?navigator.scheduling.isInputPending.bind(navigator.scheduling):null;function H(a){for(var c=h(t);null!==c;){if(null===c.callback)k(t);else if(c.startTime<=a)k(t),c.sortIndex=c.expirationTime,f(r,c);else break;c=h(t)}}function I(a){C=!1;H(a);if(!B)if(null!==h(r))B=!0,J||(J=!0,K());else{var c=h(t);null!==c&&L(I,c.startTime-a)}}
function M(a,c,b){var d=l();"object"===typeof b&&null!==b?(b=b.delay,b="number"===typeof b&&0<b?d+b:d):b=d;switch(a){case 1:var e=-1;break;case 2:e=250;break;case 5:e=1073741823;break;case 4:e=1E4;break;default:e=5E3}e=b+e;a={id:u++,callback:c,priorityLevel:a,startTime:b,expirationTime:e,sortIndex:-1};b>d?(a.sortIndex=b,f(t,a),null===h(r)&&a===h(t)&&(C?(F(N),N=-1):C=!0,L(I,b-d))):(a.sortIndex=e,f(r,a),B||A||(B=!0,J||(J=!0,K())));return a}function O(a){a.callback=null}function P(){return w}
var J=!1,N=-1,Q=-1;function R(){return 5>l()-Q?!1:!0}function S(){}
function T(){if(J){var a=l();Q=a;var c=!0;try{a:{B=!1;C&&(C=!1,F(N),N=-1);A=!0;var b=w;try{b:{H(a);for(v=h(r);null!==v&&!(v.expirationTime>a&&R());){var d=v.callback;if("function"===typeof d){v.callback=null;w=v.priorityLevel;var e=d(v.expirationTime<=a);a=l();if("function"===typeof e){v.callback=e;H(a);c=!0;break b}v===h(r)&&k(r);H(a)}else k(r);v=h(r)}if(null!==v)c=!0;else{var x=h(t);null!==x&&L(I,x.startTime-a);c=!1}}break a}finally{v=null,w=b,A=!1}c=void 0}}finally{c?K():J=!1}}}var K;
if("function"===typeof G)K=function(){G(T)};else if("undefined"!==typeof MessageChannel){var U=new MessageChannel,V=U.port2;U.port1.onmessage=T;K=function(){V.postMessage(null)}}else K=function(){E(T,0)};function L(a,c){N=E(function(){a(l())},c)}
var W="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_UserBlockingPriority:2,X="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_NormalPriority:3,Y="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_LowPriority:4,aa="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_ImmediatePriority:1,ba="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_scheduleCallback:M,ca="undefined"!==
typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_cancelCallback:O,da="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_getCurrentPriorityLevel:P,ea="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_shouldYield:R,fa="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_requestPaint:S,ha="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_now:l;
function Z(){throw Error("Not implemented.");}exports.unstable_IdlePriority="undefined"!==typeof nativeRuntimeScheduler?nativeRuntimeScheduler.unstable_IdlePriority:5;exports.unstable_ImmediatePriority=aa;exports.unstable_LowPriority=Y;exports.unstable_NormalPriority=X;exports.unstable_Profiling=null;exports.unstable_UserBlockingPriority=W;exports.unstable_cancelCallback=ca;exports.unstable_continueExecution=Z;exports.unstable_forceFrameRate=Z;exports.unstable_getCurrentPriorityLevel=da;
exports.unstable_getFirstCallbackNode=Z;exports.unstable_next=Z;exports.unstable_now=ha;exports.unstable_pauseExecution=Z;exports.unstable_requestPaint=fa;exports.unstable_runWithPriority=Z;exports.unstable_scheduleCallback=ba;exports.unstable_shouldYield=ea;exports.unstable_wrapCallback=Z;
