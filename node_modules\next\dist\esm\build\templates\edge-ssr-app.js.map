{"version": 3, "sources": ["../../../src/build/templates/edge-ssr-app.ts"], "names": ["self", "adapter", "getRender", "IncrementalCache", "renderToHTMLOrFlight", "renderToHTML", "pageMod", "Document", "appMod", "errorMod", "error500Mod", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "__BUILD_MANIFEST", "prerenderManifest", "__PRERENDER_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "render", "pagesType", "dev", "page", "clientReferenceManifest", "isServerComponent", "serverActionsManifest", "serverActionsBodySizeLimit", "config", "nextConfig", "buildId", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "handler"], "mappings": "IAsCoBA;AAtCpB,OAAO,2BAA0B;AACjC,SAASC,OAAO,QAAQ,2BAA0B;AAClD,SAASC,SAAS,QAAQ,iDAAgD;AAC1E,SAASC,gBAAgB,QAAQ,qCAAoC;AAErE,SAASC,wBAAwBC,YAAY,QAAQ,qCAAoC;AACzF,YAAYC,aAAa,eAAc;AAQvC,0CAA0C;AAE1C,MAAMC,WAAyB;AAC/B,MAAMC,SAAS;AACf,MAAMC,WAAW;AACjB,MAAMC,cAAc;AAQpB,oBAAoB;AACpB,2BAA2B;AAC3B,aAAa;AACb,oCAAoC;AACpC,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BhB,KAAKiB,gBAAgB;AAC1D,MAAMC,oBAAoBP,eAAeX,KAAKmB,oBAAoB;AAClE,MAAMC,wBAAwBT,eAAeX,KAAKqB,yBAAyB;AAC3E,MAAMC,eAActB,uBAAAA,KAAKuB,cAAc,qBAAnBvB,oBAAqB,CAAC,WAAW;AACrD,MAAMwB,oBAAoBb,eAAeX,KAAKyB,qBAAqB;AACnE,MAAMC,+BAA+BC,aACjChB,eAAeX,KAAK4B,gCAAgC,IACpDb;AACJ,MAAMc,mBAAmBlB,eAAeX,KAAK8B,oBAAoB;AAEjE,MAAMC,SAAS7B,UAAU;IACvB8B,WAAW;IACXC;IACAC,MAAM;IACN1B;IACAF;IACAG;IACAC;IACAH;IACAS;IACAE;IACAb;IACAe;IACAe,yBAAyBC,oBAAoBd,cAAc;IAC3De,uBAAuBD,oBAAoBZ,oBAAoB;IAC/Dc,4BAA4BF,oBACxBE,6BACAvB;IACJW;IACAa,QAAQC;IACRC,SAAS;IACTZ;IACAa;AACF;AAEA,OAAO,MAAMC,eAAerC,QAAO;AAEnC,eAAe,SAASsC,SAASC,IAA4C;IAC3E,OAAO5C,QAAQ;QACb,GAAG4C,IAAI;QACP1C;QACA2C,SAASf;IACX;AACF"}