"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/KanbanColumn.js":
/*!************************************!*\
  !*** ./components/KanbanColumn.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KanbanColumn; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @dnd-kit/core */ \"./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/sortable */ \"./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\");\n/* harmony import */ var _TaskCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TaskCard */ \"./components/TaskCard.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction KanbanColumn(param) {\n    let { id, title, color, headerColor, tasks } = param;\n    _s();\n    const { setNodeRef } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDroppable)({\n        id: id\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full rounded-xl border-2 border-dashed \".concat(color, \" p-4 transition-colors\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold \".concat(headerColor || \"text-gray-900 dark:text-white\"),\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs px-2.5 py-1 rounded-full font-medium shadow-sm border border-gray-200 dark:border-gray-600\",\n                        children: tasks.length\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: setNodeRef,\n                className: \"flex-1 space-y-3 min-h-[200px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__.SortableContext, {\n                        items: tasks.map((task)=>task.id),\n                        strategy: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__.verticalListSortingStrategy,\n                        children: tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                task: task\n                            }, task.id, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    tasks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-32 text-gray-400 dark:text-gray-500 text-sm\",\n                        children: \"Drop tasks here\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanColumn.js\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_s(KanbanColumn, \"cRxoCnej0Qm2GWGzi2a2LoWImLI=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDroppable\n    ];\n});\n_c = KanbanColumn;\nvar _c;\n$RefreshReg$(_c, \"KanbanColumn\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/KanbanColumn.js\n"));

/***/ })

});