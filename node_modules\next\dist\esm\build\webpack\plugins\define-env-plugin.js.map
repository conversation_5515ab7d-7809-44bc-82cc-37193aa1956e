{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["webpack", "needsExperimentalReact", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getDefineEnv", "isTurbopack", "allowedRevalidateHeaderKeys", "clientRouterFilters", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "previewModeId", "__NEXT_DEFINE_ENV", "Object", "keys", "process", "env", "reduce", "prev", "startsWith", "JSON", "stringify", "acc", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "useDeploymentIdServerActions", "deploymentId", "manualClientBasePath", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "output", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "assetPrefix", "undefined", "getDefineEnvPlugin", "options", "DefinePlugin"], "mappings": "AAEA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,sBAAsB,QAAQ,wCAAuC;AAE9E,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AA0BA,OAAO,SAASC,aAAa,EAC3BC,WAAW,EACXC,2BAA2B,EAC3BC,mBAAmB,EACnBV,MAAM,EACNW,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EAClBC,aAAa,EACU;QAsHNpB,gBAKSA,iBAY0BA;IAtIpD,OAAO;QACL,+CAA+C;QAC/CqB,mBAAmB;QAEnB,GAAGC,OAAOC,IAAI,CAACC,QAAQC,GAAG,EAAEC,MAAM,CAChC,CAACC,MAAiC1B;YAChC,IAAIA,IAAI2B,UAAU,CAAC,iBAAiB;gBAClCD,IAAI,CAAC,CAAC,YAAY,EAAE1B,IAAI,CAAC,CAAC,GAAG4B,KAAKC,SAAS,CAACN,QAAQC,GAAG,CAACxB,IAAI;YAC9D;YACA,OAAO0B;QACT,GACA,CAAC,EACF;QACD,GAAGL,OAAOC,IAAI,CAACvB,OAAOyB,GAAG,EAAEC,MAAM,CAAC,CAACK,KAAK9B;YACtCF,qBAAqBC,QAAQC;YAE7B,OAAO;gBACL,GAAG8B,GAAG;gBACN,CAAC,CAAC,YAAY,EAAE9B,IAAI,CAAC,CAAC,EAAE4B,KAAKC,SAAS,CAAC9B,OAAOyB,GAAG,CAACxB,IAAI;YACxD;QACF,GAAG,CAAC,EAAE;QACN,GAAI,CAACe,eACD,CAAC,IACD;YACEgB,aAAaH,KAAKC,SAAS,CACzB;;;;aAIC,GACDN,QAAQC,GAAG,CAACQ,0BAA0B,IAAI;QAE9C,CAAC;QACL,qBAAqBJ,KAAKC,SAAS,CAACtB;QACpC,yBAAyBqB,KAAKC,SAAS,CAACtB;QACxC,6DAA6D;QAC7D,wBAAwBqB,KAAKC,SAAS,CAACnB,MAAM,gBAAgB;QAC7D,4BAA4BkB,KAAKC,SAAS,CACxCd,eAAe,SAASE,eAAe,WAAW;QAEpD,4BAA4BW,KAAKC,SAAS,CAAC;QAC3C,4CAA4CD,KAAKC,SAAS,CACxD9B,OAAOkC,YAAY,CAACC,4BAA4B;QAElD,kCAAkCN,KAAKC,SAAS,CAC9C9B,OAAOkC,YAAY,CAACE,YAAY,IAAI;QAEtC,6CACEP,KAAKC,SAAS,CAACjB;QACjB,sCAAsCgB,KAAKC,SAAS,CAACV;QACrD,iDAAiDS,KAAKC,SAAS,CAC7DrB;QAEF,0CAA0CoB,KAAKC,SAAS,CACtDX,sBAAsB,EAAE;QAE1B,8CAA8CU,KAAKC,SAAS,CAC1D9B,OAAOkC,YAAY,CAACG,oBAAoB;QAE1C,mDAAmDR,KAAKC,SAAS,CAC/D9B,OAAOkC,YAAY,CAACI,kBAAkB;QAExC,6CAA6CT,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB6B,YAAY;QAEnC,6CAA6CV,KAAKC,SAAS,CACzDpB,uCAAAA,oBAAqB8B,aAAa;QAEpC,8CAA8CX,KAAKC,SAAS,CAC1D9B,OAAOkC,YAAY,CAACO,qBAAqB;QAE3C,0CAA0CZ,KAAKC,SAAS,CACtD9B,OAAOkC,YAAY,CAACQ,kBAAkB;QAExC,mCAAmCb,KAAKC,SAAS,CAAC9B,OAAO2C,WAAW;QACpE,mBAAmBd,KAAKC,SAAS,CAACf;QAClC,gCAAgCc,KAAKC,SAAS,CAC5CN,QAAQC,GAAG,CAACmB,gBAAgB;QAE9B,2FAA2F;QAC3F,GAAIjC,OAAQI,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+Ba,KAAKC,SAAS,CAAClB;QAChD,IACA,CAAC,CAAC;QACN,qCAAqCiB,KAAKC,SAAS,CAAC9B,OAAO6C,aAAa;QACxE,sCAAsChB,KAAKC,SAAS,CAClD9B,OAAO8C,aAAa,CAACC,aAAa;QAEpC,+CAA+ClB,KAAKC,SAAS,CAC3D9B,OAAO8C,aAAa,CAACE,qBAAqB;QAE5C,kCAAkCnB,KAAKC,SAAS,CAC9C9B,OAAOiD,eAAe,KAAK,OAAO,QAAQjD,OAAOiD,eAAe;QAElE,sCAAsCpB,KAAKC,SAAS,CAClD,6EAA6E;QAC7E9B,OAAOiD,eAAe,KAAK,OAAO,OAAOjD,OAAOiD,eAAe;QAEjE,qCAAqCpB,KAAKC,SAAS,CACjD,CAACnB,OAAOX,OAAOkD,aAAa;QAE9B,mCAAmCrB,KAAKC,SAAS,CAC/C9B,OAAOkC,YAAY,CAACiB,WAAW,IAAI,CAACxC;QAEtC,qCAAqCkB,KAAKC,SAAS,CACjD9B,OAAOkC,YAAY,CAACkB,iBAAiB,IAAI,CAACzC;QAE5C,yCAAyCkB,KAAKC,SAAS,CACrD9B,OAAOkC,YAAY,CAACmB,iBAAiB;QAEvC,iCAAiCxB,KAAKC,SAAS,CAAC;YAC9CwB,aAAatD,OAAOuD,MAAM,CAACD,WAAW;YACtCE,YAAYxD,OAAOuD,MAAM,CAACC,UAAU;YACpCC,MAAMzD,OAAOuD,MAAM,CAACE,IAAI;YACxBC,QAAQ1D,OAAOuD,MAAM,CAACG,MAAM;YAC5BC,qBAAqB3D,OAAOuD,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAE5D,2BAAAA,iBAAAA,OAAQuD,MAAM,qBAAdvD,eAAgB4D,WAAW;YACxC,GAAIjD,MACA;gBACE,gEAAgE;gBAChEkD,SAAS7D,OAAOuD,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAE9D,kBAAAA,OAAOuD,MAAM,qBAAbvD,gBAAe8D,cAAc;gBAC7CC,QAAQ/D,OAAO+D,MAAM;YACvB,IACA,CAAC,CAAC;QACR;QACA,sCAAsClC,KAAKC,SAAS,CAAC9B,OAAOgE,QAAQ;QACpE,uCAAuCnC,KAAKC,SAAS,CACnD9B,OAAOkC,YAAY,CAAC+B,cAAc;QAEpC,mCAAmCpC,KAAKC,SAAS,CAAChB;QAClD,oCAAoCe,KAAKC,SAAS,CAAC9B,OAAO+D,MAAM;QAChE,mCAAmClC,KAAKC,SAAS,CAAC,CAAC,CAAC9B,OAAOkE,IAAI;QAC/D,mCAAmCrC,KAAKC,SAAS,EAAC9B,eAAAA,OAAOkE,IAAI,qBAAXlE,aAAa6D,OAAO;QACtE,mCAAmChC,KAAKC,SAAS,CAAC9B,OAAOmE,WAAW;QACpE,kDAAkDtC,KAAKC,SAAS,CAC9D9B,OAAOoE,0BAA0B;QAEnC,0DAA0DvC,KAAKC,SAAS,CACtE9B,OAAOkC,YAAY,CAACmC,iCAAiC;QAEvD,4CAA4CxC,KAAKC,SAAS,CACxD9B,OAAOsE,yBAAyB;QAElC,iDAAiDzC,KAAKC,SAAS,CAC7D9B,OAAOkC,YAAY,CAACqC,oBAAoB,IACtCvE,OAAOkC,YAAY,CAACqC,oBAAoB,CAACC,MAAM,GAAG;QAEtD,6CAA6C3C,KAAKC,SAAS,CACzD9B,OAAOkC,YAAY,CAACqC,oBAAoB;QAE1C,mCAAmC1C,KAAKC,SAAS,CAAC9B,OAAOyE,WAAW;QACpE,GAAIxD,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiBY,KAAKC,SAAS,CAAC;QAClC,IACA4C,SAAS;QACb,GAAIzD,0BACA;YACE,yCAAyCY,KAAKC,SAAS,CACrDhC,uBAAuBE;QAE3B,IACA0E,SAAS;IACf;AACF;AAEA,OAAO,SAASC,mBAAmBC,OAA+B;IAChE,OAAO,IAAI/E,QAAQgF,YAAY,CAACtE,aAAaqE;AAC/C"}