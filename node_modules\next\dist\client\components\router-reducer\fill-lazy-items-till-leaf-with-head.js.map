{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.ts"], "names": ["fillLazyItemsTillLeafWithHead", "newCache", "existingCache", "routerState", "head", "wasPrefetched", "isLastSegment", "Object", "keys", "length", "key", "parallelRouteState", "segmentForParallelRoute", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingParallelRoutesCacheNode", "parallelRoutes", "get", "parallelRouteCacheNode", "Map", "existingCacheNode", "newCacheNode", "status", "data", "subTreeData", "CacheStates", "LAZY_INITIALIZED", "set", "existingParallelRoutes", "undefined"], "mappings": ";;;;+BAKg<PERSON>;;;eAAAA;;;+CALY;sCAGS;AAE9B,SAASA,8BACdC,QAAmB,EACnBC,aAAoC,EACpCC,WAA8B,EAC9BC,IAAqB,EACrBC,aAAuB;IAEvB,MAAMC,gBAAgBC,OAAOC,IAAI,CAACL,WAAW,CAAC,EAAE,EAAEM,MAAM,KAAK;IAC7D,IAAIH,eAAe;QACjBL,SAASG,IAAI,GAAGA;QAChB;IACF;IACA,+FAA+F;IAC/F,IAAK,MAAMM,OAAOP,WAAW,CAAC,EAAE,CAAE;QAChC,MAAMQ,qBAAqBR,WAAW,CAAC,EAAE,CAACO,IAAI;QAC9C,MAAME,0BAA0BD,kBAAkB,CAAC,EAAE;QACrD,MAAME,WAAWC,IAAAA,0CAAoB,EAACF;QAEtC,IAAIV,eAAe;YACjB,MAAMa,kCACJb,cAAcc,cAAc,CAACC,GAAG,CAACP;YACnC,IAAIK,iCAAiC;gBACnC,IAAIG,yBAAyB,IAAIC,IAAIJ;gBACrC,MAAMK,oBAAoBF,uBAAuBD,GAAG,CAACJ;gBACrD,MAAMQ,eACJhB,iBAAiBe,oBACZ;oBACCE,QAAQF,kBAAkBE,MAAM;oBAChCC,MAAMH,kBAAkBG,IAAI;oBAC5BC,aAAaJ,kBAAkBI,WAAW;oBAC1CR,gBAAgB,IAAIG,IAAIC,kBAAkBJ,cAAc;gBAC1D,IACA;oBACEM,QAAQG,0CAAW,CAACC,gBAAgB;oBACpCH,MAAM;oBACNC,aAAa;oBACbR,gBAAgB,IAAIG,IAAIC,qCAAAA,kBAAmBJ,cAAc;gBAC3D;gBACN,mDAAmD;gBACnDE,uBAAuBS,GAAG,CAACd,UAAUQ;gBACrC,qEAAqE;gBACrErB,8BACEqB,cACAD,mBACAT,oBACAP,MACAC;gBAGFJ,SAASe,cAAc,CAACW,GAAG,CAACjB,KAAKQ;gBACjC;YACF;QACF;QAEA,MAAMG,eAA0B;YAC9BC,QAAQG,0CAAW,CAACC,gBAAgB;YACpCH,MAAM;YACNC,aAAa;YACbR,gBAAgB,IAAIG;QACtB;QAEA,MAAMS,yBAAyB3B,SAASe,cAAc,CAACC,GAAG,CAACP;QAC3D,IAAIkB,wBAAwB;YAC1BA,uBAAuBD,GAAG,CAACd,UAAUQ;QACvC,OAAO;YACLpB,SAASe,cAAc,CAACW,GAAG,CAACjB,KAAK,IAAIS,IAAI;gBAAC;oBAACN;oBAAUQ;iBAAa;aAAC;QACrE;QAEArB,8BACEqB,cACAQ,WACAlB,oBACAP,MACAC;IAEJ;AACF"}