{"version": 3, "sources": ["../../../src/client/components/app-router.tsx"], "names": ["React", "use", "useEffect", "useMemo", "useCallback", "startTransition", "useInsertionEffect", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "CacheStates", "reducer", "ACTION_FAST_REFRESH", "ACTION_NAVIGATE", "ACTION_PREFETCH", "ACTION_REFRESH", "ACTION_RESTORE", "ACTION_SERVER_ACTION", "ACTION_SERVER_PATCH", "PrefetchKind", "createHrefFromUrl", "SearchParamsContext", "PathnameContext", "useReducerWithReduxDevtools", "Error<PERSON>ou<PERSON><PERSON>", "createInitialRouterState", "isBot", "addBasePath", "AppRouterAnnouncer", "RedirectBoundary", "findHeadInCache", "createInfinitePromise", "NEXT_RSC_UNION_QUERY", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "isServer", "window", "initialParallelRoutes", "Map", "globalServerActionDispatcher", "getServerActionDispatcher", "globalMutable", "refresh", "urlToUrlWithoutFlightMarker", "url", "urlWithoutFlightParameters", "URL", "location", "origin", "searchParams", "delete", "process", "env", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "length", "slice", "isExternalURL", "HistoryUpdater", "tree", "pushRef", "canonicalUrl", "sync", "historyState", "__NA", "pendingPush", "href", "history", "pushState", "replaceState", "createEmptyCacheNode", "status", "LAZY_INITIALIZED", "data", "subTreeData", "parallelRoutes", "useServerActionDispatcher", "dispatch", "serverActionDispatcher", "actionPayload", "type", "mutable", "cache", "useChangeByServerResponse", "previousTree", "flightData", "overrideCanonicalUrl", "useNavigate", "navigateType", "forceOptimisticNavigation", "shouldScroll", "pendingNavigatePath", "isExternalUrl", "locationSearch", "search", "Router", "buildId", "initialHead", "initialTree", "initialCanonicalUrl", "children", "assetPrefix", "initialState", "prefetchCache", "focusAndScrollRef", "nextUrl", "changeByServerResponse", "navigate", "appRouter", "routerInstance", "back", "forward", "prefetch", "options", "navigator", "userAgent", "kind", "FULL", "replace", "Boolean", "scroll", "push", "fastRefresh", "Error", "next", "router", "nd", "handlePageShow", "event", "persisted", "state", "addEventListener", "removeEventListener", "mpaNavigation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assign", "onPopState", "reload", "head", "content", "DevRootNotFoundBoundary", "require", "HotReloader", "default", "Provider", "value", "childNodes", "AppRouter", "props", "globalErrorComponent", "rest", "errorComponent"], "mappings": "AAAA;AAGA,OAAOA,SACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,kBAAkB,QACb,QAAO;AACd,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,yBAAyB,EACzBC,WAAW,QACN,qDAAoD;AAU3D,SAASC,OAAO,QAAQ,kCAAiC;AACzD,SACEC,mBAAmB,EACnBC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,oBAAoB,EACpBC,mBAAmB,EACnBC,YAAY,QACP,wCAAuC;AAQ9C,SAASC,iBAAiB,QAAQ,wCAAuC;AACzE,SACEC,mBAAmB,EACnBC,eAAe,QACV,uDAAsD;AAC7D,SAASC,2BAA2B,QAAQ,8BAA6B;AACzE,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,wBAAwB,QAAQ,+CAA8C;AAEvF,SAASC,KAAK,QAAQ,uCAAsC;AAC5D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,eAAe,QAAQ,+CAA8C;AAC9E,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,oBAAoB,QAAQ,uBAAsB;AAC3D,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,MAAMC,WAAW,OAAOC,WAAW;AAEnC,iHAAiH;AACjH,IAAIC,wBAAqDF,WACrD,OACA,IAAIG;AAER,IAAIC,+BAA+B;AAEnC,OAAO,SAASC;IACd,OAAOD;AACT;AAEA,IAAIE,gBAA0C;IAC5CC,SAAS,KAAO;AAClB;AAEA,OAAO,SAASC,4BAA4BC,GAAW;IACrD,MAAMC,6BAA6B,IAAIC,IAAIF,KAAKG,SAASC,MAAM;IAC/DH,2BAA2BI,YAAY,CAACC,MAAM,CAAClB;IAC/C,IAAImB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IACEF,QAAQC,GAAG,CAACE,oBAAoB,KAAK,YACrCT,2BAA2BU,QAAQ,CAACC,QAAQ,CAAC,SAC7C;YACA,MAAM,EAAED,QAAQ,EAAE,GAAGV;YACrB,MAAMY,SAASF,SAASC,QAAQ,CAAC,gBAAgB,KAAK;YACtD,gEAAgE;YAChEX,2BAA2BU,QAAQ,GAAGA,SAASG,KAAK,CAAC,GAAG,CAACD;QAC3D;IACF;IACA,OAAOZ;AACT;AAWA,SAASc,cAAcf,GAAQ;IAC7B,OAAOA,IAAII,MAAM,KAAKZ,OAAOW,QAAQ,CAACC,MAAM;AAC9C;AAEA,SAASY,eAAe,KAA0C;IAA1C,IAAA,EAAEC,IAAI,EAAEC,OAAO,EAAEC,YAAY,EAAEC,IAAI,EAAO,GAA1C;IACtB1D,mBAAmB;QACjB,yCAAyC;QACzC,kFAAkF;QAClF,iFAAiF;QACjF,MAAM2D,eAAe;YACnBC,MAAM;YACNL;QACF;QACA,IACEC,QAAQK,WAAW,IACnB/C,kBAAkB,IAAI0B,IAAIV,OAAOW,QAAQ,CAACqB,IAAI,OAAOL,cACrD;YACA,qJAAqJ;YACrJD,QAAQK,WAAW,GAAG;YACtB/B,OAAOiC,OAAO,CAACC,SAAS,CAACL,cAAc,IAAIF;QAC7C,OAAO;YACL3B,OAAOiC,OAAO,CAACE,YAAY,CAACN,cAAc,IAAIF;QAChD;QACAC;IACF,GAAG;QAACH;QAAMC;QAASC;QAAcC;KAAK;IACtC,OAAO;AACT;AAEA,MAAMQ,uBAAuB,IAAO,CAAA;QAClCC,QAAQ/D,YAAYgE,gBAAgB;QACpCC,MAAM;QACNC,aAAa;QACbC,gBAAgB,IAAIvC;IACtB,CAAA;AAEA,SAASwC,0BAA0BC,QAAwC;IACzE,MAAMC,yBAAiD5E,YACrD,CAAC6E;QACC5E,gBAAgB;YACd0E,SAAS;gBACP,GAAGE,aAAa;gBAChBC,MAAMjE;gBACNkE,SAAS;oBAAE1C;gBAAc;gBACzB2C,OAAOZ;YACT;QACF;IACF,GACA;QAACO;KAAS;IAEZxC,+BAA+ByC;AACjC;AAEA;;CAEC,GACD,SAASK,0BACPN,QAAwC;IAExC,OAAO3E,YACL,CACEkF,cACAC,YACAC;QAEAnF,gBAAgB;YACd0E,SAAS;gBACPG,MAAMhE;gBACNqE;gBACAD;gBACAE;gBACAJ,OAAOZ;gBACPW,SAAS;oBAAE1C;gBAAc;YAC3B;QACF;IACF,GACA;QAACsC;KAAS;AAEd;AAEA,SAASU,YAAYV,QAAwC;IAC3D,OAAO3E,YACL,CAACgE,MAAMsB,cAAcC,2BAA2BC;QAC9C,MAAMhD,MAAM,IAAIE,IAAInB,YAAYyC,OAAOrB,SAASqB,IAAI;QACpD3B,cAAcoD,mBAAmB,GAAGzE,kBAAkBwB;QAEtD,OAAOmC,SAAS;YACdG,MAAMrE;YACN+B;YACAkD,eAAenC,cAAcf;YAC7BmD,gBAAgBhD,SAASiD,MAAM;YAC/BL;YACAC,cAAcA,uBAAAA,eAAgB;YAC9BF;YACAN,OAAOZ;YACPW,SAAS;gBAAE1C;YAAc;QAC3B;IACF,GACA;QAACsC;KAAS;AAEd;AAEA;;CAEC,GACD,SAASkB,OAAO,KAOC;IAPD,IAAA,EACdC,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,mBAAmB,EACnBC,QAAQ,EACRC,WAAW,EACI,GAPD;IAQd,MAAMC,eAAerG,QACnB,IACEsB,yBAAyB;YACvByE;YACAI;YACAD;YACAD;YACA/D;YACAF;YACAY,UAAU,CAACZ,WAAWC,OAAOW,QAAQ,GAAG;YACxCoD;QACF,IACF;QAACD;QAASI;QAAUD;QAAqBD;QAAaD;KAAY;IAEpE,MAAM,CACJ,EACEtC,IAAI,EACJuB,KAAK,EACLqB,aAAa,EACb3C,OAAO,EACP4C,iBAAiB,EACjB3C,YAAY,EACZ4C,OAAO,EACR,EACD5B,UACAf,KACD,GAAGzC,4BAA4BZ,SAAS6F;IAEzCtG,UAAU;QACR,yEAAyE;QACzEmC,wBAAwB;IAC1B,GAAG,EAAE;IAEL,mEAAmE;IACnE,MAAM,EAAEY,YAAY,EAAEM,QAAQ,EAAE,GAAGpD,QAAQ;QACzC,MAAMyC,MAAM,IAAIE,IACdiB,cACA,OAAO3B,WAAW,cAAc,aAAaA,OAAOW,QAAQ,CAACqB,IAAI;QAGnE,OAAO;YACL,4DAA4D;YAC5DnB,cAAcL,IAAIK,YAAY;YAC9BM,UAAUrB,YAAYU,IAAIW,QAAQ,IAC9BtB,eAAeW,IAAIW,QAAQ,IAC3BX,IAAIW,QAAQ;QAClB;IACF,GAAG;QAACQ;KAAa;IAEjB,MAAM6C,yBAAyBvB,0BAA0BN;IACzD,MAAM8B,WAAWpB,YAAYV;IAC7BD,0BAA0BC;IAE1B;;GAEC,GACD,MAAM+B,YAAY3G,QAA2B;QAC3C,MAAM4G,iBAAoC;YACxCC,MAAM,IAAM5E,OAAOiC,OAAO,CAAC2C,IAAI;YAC/BC,SAAS,IAAM7E,OAAOiC,OAAO,CAAC4C,OAAO;YACrCC,UAAU,CAAC9C,MAAM+C;gBACf,kDAAkD;gBAClD,uEAAuE;gBACvE,IACEzF,MAAMU,OAAOgF,SAAS,CAACC,SAAS,KAChClE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzB;oBACA;gBACF;gBACA,MAAMT,MAAM,IAAIE,IAAInB,YAAYyC,OAAOrB,SAASqB,IAAI;gBACpD,qDAAqD;gBACrD,IAAIT,cAAcf,MAAM;oBACtB;gBACF;gBACAvC,gBAAgB;wBAIN8G;oBAHRpC,SAAS;wBACPG,MAAMpE;wBACN8B;wBACA0E,MAAMH,CAAAA,gBAAAA,2BAAAA,QAASG,IAAI,YAAbH,gBAAiBhG,aAAaoG,IAAI;oBAC1C;gBACF;YACF;YACAC,SAAS,CAACpD,MAAM+C;oBAAAA,oBAAAA,UAAU,CAAC;gBACzB9G,gBAAgB;wBAKZ8G;oBAJFN,SACEzC,MACA,WACAqD,QAAQN,QAAQxB,yBAAyB,GACzCwB,CAAAA,kBAAAA,QAAQO,MAAM,YAAdP,kBAAkB;gBAEtB;YACF;YACAQ,MAAM,CAACvD,MAAM+C;oBAAAA,oBAAAA,UAAU,CAAC;gBACtB9G,gBAAgB;wBAKZ8G;oBAJFN,SACEzC,MACA,QACAqD,QAAQN,QAAQxB,yBAAyB,GACzCwB,CAAAA,kBAAAA,QAAQO,MAAM,YAAdP,kBAAkB;gBAEtB;YACF;YACAzE,SAAS;gBACPrC,gBAAgB;oBACd0E,SAAS;wBACPG,MAAMnE;wBACNqE,OAAOZ;wBACPW,SAAS;4BAAE1C;wBAAc;wBACzBO,QAAQZ,OAAOW,QAAQ,CAACC,MAAM;oBAChC;gBACF;YACF;YACA,wDAAwD;YACxD4E,aAAa;gBACX,IAAIzE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;oBAC1C,MAAM,IAAIwE,MACR;gBAEJ,OAAO;oBACLxH,gBAAgB;wBACd0E,SAAS;4BACPG,MAAMtE;4BACNwE,OAAOZ;4BACPW,SAAS;gCAAE1C;4BAAc;4BACzBO,QAAQZ,OAAOW,QAAQ,CAACC,MAAM;wBAChC;oBACF;gBACF;YACF;QACF;QAEA,OAAO+D;IACT,GAAG;QAAChC;QAAU8B;KAAS;IAEvB3G,UAAU;QACR,gEAAgE;QAChE,IAAIkC,OAAO0F,IAAI,EAAE;YACf1F,OAAO0F,IAAI,CAACC,MAAM,GAAGjB;QACvB;IACF,GAAG;QAACA;KAAU;IAEd5G,UAAU;QACRuC,cAAcC,OAAO,GAAGoE,UAAUpE,OAAO;IAC3C,GAAG;QAACoE,UAAUpE,OAAO;KAAC;IAEtB,IAAIS,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,4FAA4F;QAC5F,sDAAsD;QACtDnD,UAAU;YACR,0CAA0C;YAC1C,uGAAuG;YACvG,mCAAmC;YACnCkC,OAAO4F,EAAE,GAAG;gBACVD,QAAQjB;gBACR1B;gBACAqB;gBACA5C;YACF;QACF,GAAG;YAACiD;YAAW1B;YAAOqB;YAAe5C;SAAK;IAC5C;IAEA3D,UAAU;QACR,0DAA0D;QAC1D,uFAAuF;QACvF,qEAAqE;QACrE,wGAAwG;QACxG,SAAS+H,eAAeC,KAA0B;gBACvB9F;YAAzB,IAAI,CAAC8F,MAAMC,SAAS,IAAI,GAAC/F,wBAAAA,OAAOiC,OAAO,CAAC+D,KAAK,qBAApBhG,sBAAsByB,IAAI,GAAE;YAErDkB,SAAS;gBACPG,MAAMlE;gBACN4B,KAAK,IAAIE,IAAIV,OAAOW,QAAQ,CAACqB,IAAI;gBACjCP,MAAMzB,OAAOiC,OAAO,CAAC+D,KAAK,CAACvE,IAAI;YACjC;QACF;QAEAzB,OAAOiG,gBAAgB,CAAC,YAAYJ;QAEpC,OAAO;YACL7F,OAAOkG,mBAAmB,CAAC,YAAYL;QACzC;IACF,GAAG;QAAClD;KAAS;IAEb,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,YAAY;IACZ,EAAE;IACF,sEAAsE;IACtE,6EAA6E;IAC7E,6EAA6E;IAC7E,uBAAuB;IACvB,IAAIjB,QAAQyE,aAAa,EAAE;QACzB,gHAAgH;QAChH,IAAI9F,cAAc+F,cAAc,KAAKzE,cAAc;YACjD,MAAMhB,YAAWX,OAAOW,QAAQ;YAChC,IAAIe,QAAQK,WAAW,EAAE;gBACvBpB,UAAS0F,MAAM,CAAC1E;YAClB,OAAO;gBACLhB,UAASyE,OAAO,CAACzD;YACnB;YAEAtB,cAAc+F,cAAc,GAAGzE;QACjC;QACA,mEAAmE;QACnE,4EAA4E;QAC5E,+BAA+B;QAC/B9D,IAAI8B;IACN;IAEA;;;;GAIC,GACD,MAAM2G,aAAatI,YACjB;YAAC,EAAEgI,KAAK,EAAiB;QACvB,IAAI,CAACA,OAAO;YACV,+IAA+I;YAC/I;QACF;QAEA,6EAA6E;QAC7E,IAAI,CAACA,MAAMlE,IAAI,EAAE;YACf9B,OAAOW,QAAQ,CAAC4F,MAAM;YACtB;QACF;QAEA,kCAAkC;QAClC,gHAAgH;QAChH,oEAAoE;QACpEtI,gBAAgB;YACd0E,SAAS;gBACPG,MAAMlE;gBACN4B,KAAK,IAAIE,IAAIV,OAAOW,QAAQ,CAACqB,IAAI;gBACjCP,MAAMuE,MAAMvE,IAAI;YAClB;QACF;IACF,GACA;QAACkB;KAAS;IAGZ,8CAA8C;IAC9C7E,UAAU;QACRkC,OAAOiG,gBAAgB,CAAC,YAAYK;QACpC,OAAO;YACLtG,OAAOkG,mBAAmB,CAAC,YAAYI;QACzC;IACF,GAAG;QAACA;KAAW;IAEf,MAAME,OAAOzI,QAAQ;QACnB,OAAO2B,gBAAgBsD,OAAOvB,IAAI,CAAC,EAAE;IACvC,GAAG;QAACuB;QAAOvB;KAAK;IAEhB,IAAIgF,wBACF,oBAAChH,wBACE+G,MACAxD,MAAMR,WAAW,gBAClB,oBAAChD;QAAmBiC,MAAMA;;IAI9B,IAAIV,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAOjB,WAAW,aAAa;YACjC,MAAM0G,0BACJC,QAAQ,iCAAiCD,uBAAuB;YAClED,wBAAU,oBAACC,+BAAyBD;QACtC;QACA,MAAMG,cACJD,QAAQ,2CAA2CE,OAAO;QAE5DJ,wBAAU,oBAACG;YAAYzC,aAAaA;WAAcsC;IACpD;IAEA,qBACE,wDACE,oBAACjF;QACCC,MAAMA;QACNC,SAASA;QACTC,cAAcA;QACdC,MAAMA;sBAER,oBAAC1C,gBAAgB4H,QAAQ;QAACC,OAAO5F;qBAC/B,oBAAClC,oBAAoB6H,QAAQ;QAACC,OAAOlG;qBACnC,oBAACxC,0BAA0ByI,QAAQ;QACjCC,OAAO;YACLjD;YACAU;YACA/C;YACA6C;YACAC;QACF;qBAEA,oBAACpG,iBAAiB2I,QAAQ;QAACC,OAAOrC;qBAChC,oBAACtG,oBAAoB0I,QAAQ;QAC3BC,OAAO;YACLC,YAAYhE,MAAMP,cAAc;YAChChB,MAAMA;YACN,6BAA6B;YAC7B,8EAA8E;YAC9EjB,KAAKmB;QACP;OAEC8E;AAQjB;AAEA,eAAe,SAASQ,UACtBC,KAAgE;IAEhE,MAAM,EAAEC,oBAAoB,EAAE,GAAGC,MAAM,GAAGF;IAE1C,qBACE,oBAAC9H;QAAciI,gBAAgBF;qBAC7B,oBAACtD,QAAWuD;AAGlB"}