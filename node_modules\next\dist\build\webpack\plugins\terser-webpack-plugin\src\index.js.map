{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/terser-webpack-plugin/src/index.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getEcmaVersion", "environment", "arrowFunction", "const", "destructuring", "forOf", "module", "bigIntLiteral", "dynamicImport", "buildError", "error", "file", "line", "Error", "message", "col", "stack", "split", "slice", "join", "debugMinify", "process", "env", "NEXT_DEBUG_MINIFY", "constructor", "options", "terserOptions", "parallel", "swcMinify", "optimize", "compiler", "compilation", "assets", "optimizeOptions", "cache", "SourceMapSource", "RawSource", "compilationSpan", "spans", "get", "terserSpan", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "name", "traceAsyncFn", "numberOfAssetsForMinify", "assetsList", "Object", "keys", "assetsForMinify", "Promise", "all", "filter", "ModuleFilenameHelpers", "matchObject", "bind", "undefined", "test", "res", "getAsset", "console", "log", "match", "info", "minimized", "map", "source", "eTag", "getLazyHashedEtag", "output", "getPromise", "JSON", "stringify", "toString", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSource", "numberOfWorkers", "Math", "min", "availableNumberOfCores", "initializedWorker", "getWorker", "minify", "result", "require", "input", "inputSourceMap", "sourceMap", "content", "compress", "mangle", "toplevel", "keep_classnames", "keep_fnames", "Worker", "path", "__dirname", "numWorkers", "enableWorkerThreads", "getStdout", "pipe", "stdout", "getStderr", "stderr", "limit", "pLimit", "scheduledTasks", "asset", "push", "minifySpan", "sourceFromInputSource", "sourceAndMap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "javascriptModule", "errors", "code", "storePromise", "newInfo", "updateAsset", "end", "apply", "webpack", "sources", "ecma", "pluginName", "hooks", "thisCompilation", "tap", "getCache", "handleHashForChunk", "hash", "_chunk", "update", "JSModulesHooks", "javascript", "JavascriptModulesPlugin", "getCompilationHooks", "chunkHash", "chunk", "hasRuntime", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "statsPrinter", "stats", "print", "for", "green", "formatFlag"], "mappings": ";;;;+BAkDaA;;;eAAAA;;;8DAlDS;yBAKf;+DACY;4BACI;iCACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,SAASC,eAAeC,WAAgB;IACtC,SAAS;IACT,IACEA,YAAYC,aAAa,IACzBD,YAAYE,KAAK,IACjBF,YAAYG,aAAa,IACzBH,YAAYI,KAAK,IACjBJ,YAAYK,MAAM,EAClB;QACA,OAAO;IACT;IAEA,UAAU;IACV,IAAIL,YAAYM,aAAa,IAAIN,YAAYO,aAAa,EAAE;QAC1D,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASC,WAAWC,KAAU,EAAEC,IAAY;IAC1C,IAAID,MAAME,IAAI,EAAE;QACd,OAAO,IAAIC,MACT,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEH,KAAK,CAAC,EAAED,MAAME,IAAI,CAAC,CAAC,EAC5DF,MAAMK,GAAG,CACV,CAAC,EACAL,MAAMM,KAAK,GAAG,CAAC,EAAE,EAAEN,MAAMM,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAGC,IAAI,CAAC,MAAM,CAAC,GAAG,GACpE,CAAC;IAEN;IAEA,IAAIT,MAAMM,KAAK,EAAE;QACf,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEJ,MAAMM,KAAK,CAAC,CAAC;IAC1E;IAEA,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,CAAC;AAC1D;AAEA,MAAMM,cAAcC,QAAQC,GAAG,CAACC,iBAAiB;AAE1C,MAAMxB;IAEXyB,YAAYC,UAAe,CAAC,CAAC,CAAE;QAC7B,MAAM,EAAEC,gBAAgB,CAAC,CAAC,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;QAEpD,IAAI,CAACA,OAAO,GAAG;YACbG;YACAD;YACAD;QACF;IACF;IAEA,MAAMG,SACJC,QAAa,EACbC,WAAgB,EAChBC,MAAW,EACXC,eAAoB,EACpBC,KAAU,EACV,EAAEC,eAAe,EAAEC,SAAS,EAAO,EACnC;QACA,MAAMC,kBAAkBC,sBAAK,CAACC,GAAG,CAACR,gBAAiBO,sBAAK,CAACC,GAAG,CAACT;QAC7D,MAAMU,aAAaH,gBAAgBI,UAAU,CAC3C;QAEFD,WAAWE,YAAY,CAAC,mBAAmBX,YAAYY,IAAI;QAC3DH,WAAWE,YAAY,CAAC,aAAa,IAAI,CAACjB,OAAO,CAACG,SAAS;QAE3D,OAAOY,WAAWI,YAAY,CAAC;YAC7B,IAAIC,0BAA0B;YAC9B,MAAMC,aAAaC,OAAOC,IAAI,CAAChB;YAE/B,MAAMiB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCL,WACGM,MAAM,CAAC,CAACT;gBACP,IACE,CAACU,8BAAqB,CAACC,WAAW,CAACC,IAAI,CACrC,wCAAwC;gBACxCC,WACA;oBAAEC,MAAM;gBAAqB,GAC7Bd,OACF;oBACA,OAAO;gBACT;gBAEA,MAAMe,MAAM3B,YAAY4B,QAAQ,CAAChB;gBACjC,IAAI,CAACe,KAAK;oBACRE,QAAQC,GAAG,CAAClB;oBACZ,OAAO;gBACT;gBAEA,yDAAyD;gBACzD,gEAAgE;gBAChE,IACEA,KAAKmB,KAAK,CACR,2DAEF;oBACA,OAAO;gBACT;gBAEA,MAAM,EAAEC,IAAI,EAAE,GAAGL;gBAEjB,qDAAqD;gBACrD,IAAIK,KAAKC,SAAS,EAAE;oBAClB,OAAO;gBACT;gBAEA,OAAO;YACT,GACCC,GAAG,CAAC,OAAOtB;gBACV,MAAM,EAAEoB,IAAI,EAAEG,MAAM,EAAE,GAAGnC,YAAY4B,QAAQ,CAAChB;gBAE9C,MAAMwB,OAAOjC,MAAMkC,iBAAiB,CAACF;gBACrC,MAAMG,SAAS,MAAMnC,MAAMoC,UAAU,CAAC3B,MAAMwB;gBAE5C,IAAI,CAACE,QAAQ;oBACXxB,2BAA2B;gBAC7B;gBAEA,IAAIzB,eAAeA,gBAAgB,KAAK;oBACtCwC,QAAQC,GAAG,CACTU,KAAKC,SAAS,CAAC;wBACb7B;wBACAuB,QAAQA,OAAOA,MAAM,GAAGO,QAAQ;oBAClC,IACA;wBACEC,aAAaC;wBACbC,iBAAiBD;oBACnB;gBAEJ;gBACA,OAAO;oBAAEhC;oBAAMoB;oBAAMc,aAAaX;oBAAQG;oBAAQF;gBAAK;YACzD;YAGJ,MAAMW,kBAAkBC,KAAKC,GAAG,CAC9BnC,yBACAZ,gBAAgBgD,sBAAsB;YAGxC,IAAIC;YAEJ,6CAA6C;YAC7C,MAAMC,YAAY;gBAChB,IAAI,IAAI,CAAC1D,OAAO,CAACG,SAAS,EAAE;oBAC1B,OAAO;wBACLwD,QAAQ,OAAO3D;4BACb,MAAM4D,SAAS,MAAMC,QAAQ,mBAAmBF,MAAM,CACpD3D,QAAQ8D,KAAK,EACb;gCACE,GAAI9D,QAAQ+D,cAAc,GACtB;oCACEC,WAAW;wCACTC,SAASnB,KAAKC,SAAS,CAAC/C,QAAQ+D,cAAc;oCAChD;gCACF,IACA,CAAC,CAAC;gCACNG,UAAU;gCACV,oCAAoC;gCACpCC,QAAQ;oCACNC,UAAU;oCACVC,iBAAiB;oCACjBC,aAAa;gCACf;4BACF;4BAGF,OAAOV;wBACT;oBACF;gBACF;gBAEA,IAAIH,mBAAmB;oBACrB,OAAOA;gBACT;gBAEAA,oBAAoB,IAAIc,kBAAM,CAACC,MAAK9E,IAAI,CAAC+E,WAAW,gBAAgB;oBAClEC,YAAYrB;oBACZsB,qBAAqB;gBACvB;gBAEAlB,kBAAkBmB,SAAS,GAAGC,IAAI,CAACjF,QAAQkF,MAAM;gBACjDrB,kBAAkBsB,SAAS,GAAGF,IAAI,CAACjF,QAAQoF,MAAM;gBAEjD,OAAOvB;YACT;YAEA,MAAMwB,QAAQC,IAAAA,eAAM,EAClB,mEAAmE;YACnE,IAAI,CAAClF,OAAO,CAACG,SAAS,GAClB+C,WACA9B,0BAA0B,IAC1BiC,kBACAH;YAEN,MAAMiC,iBAAiB,EAAE;YAEzB,KAAK,MAAMC,SAAS5D,gBAAiB;gBACnC2D,eAAeE,IAAI,CACjBJ,MAAM;oBACJ,MAAM,EAAE/D,IAAI,EAAEkC,WAAW,EAAEd,IAAI,EAAEI,IAAI,EAAE,GAAG0C;oBAC1C,IAAI,EAAExC,MAAM,EAAE,GAAGwC;oBAEjB,MAAME,aAAavE,WAAWC,UAAU,CAAC;oBACzCsE,WAAWrE,YAAY,CAAC,QAAQC;oBAChCoE,WAAWrE,YAAY,CACrB,SACA,OAAO2B,WAAW,cAAc,SAAS;oBAG3C,OAAO0C,WAAWnE,YAAY,CAAC;wBAC7B,IAAI,CAACyB,QAAQ;4BACX,MAAM,EAAEH,QAAQ8C,qBAAqB,EAAE/C,KAAKuB,cAAc,EAAE,GAC1DX,YAAYoC,YAAY;4BAE1B,MAAM1B,QAAQ2B,OAAOC,QAAQ,CAACH,yBAC1BA,sBAAsBvC,QAAQ,KAC9BuC;4BAEJ,MAAMvF,UAAU;gCACdkB;gCACA4C;gCACAC;gCACA9D,eAAe;oCAAE,GAAG,IAAI,CAACD,OAAO,CAACC,aAAa;gCAAC;4BACjD;4BAEA,IAAI,OAAOD,QAAQC,aAAa,CAACpB,MAAM,KAAK,aAAa;gCACvD,IAAI,OAAOyD,KAAKqD,gBAAgB,KAAK,aAAa;oCAChD3F,QAAQC,aAAa,CAACpB,MAAM,GAAGyD,KAAKqD,gBAAgB;gCACtD,OAAO,IAAI,iBAAiB3D,IAAI,CAACd,OAAO;oCACtClB,QAAQC,aAAa,CAACpB,MAAM,GAAG;gCACjC,OAAO,IAAI,iBAAiBmD,IAAI,CAACd,OAAO;oCACtClB,QAAQC,aAAa,CAACpB,MAAM,GAAG;gCACjC;4BACF;4BAEA,IAAI;gCACF+D,SAAS,MAAMc,YAAYC,MAAM,CAAC3D;4BACpC,EAAE,OAAOf,OAAO;gCACdqB,YAAYsF,MAAM,CAACP,IAAI,CAACrG,WAAWC,OAAOiC;gCAE1C;4BACF;4BAEA,IAAI0B,OAAOJ,GAAG,EAAE;gCACdI,OAAOH,MAAM,GAAG,IAAI/B,gBAClBkC,OAAOiD,IAAI,EACX3E,MACA0B,OAAOJ,GAAG,EACVsB,OACAC,gBACA;4BAEJ,OAAO;gCACLnB,OAAOH,MAAM,GAAG,IAAI9B,UAAUiC,OAAOiD,IAAI;4BAC3C;4BAEA,MAAMpF,MAAMqF,YAAY,CAAC5E,MAAMwB,MAAM;gCACnCD,QAAQG,OAAOH,MAAM;4BACvB;wBACF;wBAEA,MAAMsD,UAAU;4BAAExD,WAAW;wBAAK;wBAClC,MAAM,EAAEE,MAAM,EAAE,GAAGG;wBAEnBtC,YAAY0F,WAAW,CAAC9E,MAAMuB,QAAQsD;oBACxC;gBACF;YAEJ;YAEA,MAAMtE,QAAQC,GAAG,CAACyD;YAElB,IAAI1B,mBAAmB;gBACrB,MAAMA,kBAAkBwC,GAAG;YAC7B;QACF;IACF;IAEAC,MAAM7F,QAAa,EAAE;YACoBA;QAAvC,MAAM,EAAEK,eAAe,EAAEC,SAAS,EAAE,GAAGN,CAAAA,6BAAAA,oBAAAA,SAAU8F,OAAO,qBAAjB9F,kBAAmB+F,OAAO,KAAIA,gBAAO;QAC5E,MAAM,EAAExD,MAAM,EAAE,GAAGvC,SAASL,OAAO;QAEnC,IAAI,OAAO,IAAI,CAACA,OAAO,CAACC,aAAa,CAACoG,IAAI,KAAK,aAAa;YAC1D,IAAI,CAACrG,OAAO,CAACC,aAAa,CAACoG,IAAI,GAAG9H,eAAeqE,OAAOpE,WAAW,IAAI,CAAC;QAC1E;QAEA,MAAM8H,aAAa,IAAI,CAACvG,WAAW,CAACmB,IAAI;QACxC,MAAMsC,yBAAyB,IAAI,CAACxD,OAAO,CAACE,QAAQ;QAEpDG,SAASkG,KAAK,CAACC,eAAe,CAACC,GAAG,CAACH,YAAY,CAAChG;YAC9C,MAAMG,QAAQH,YAAYoG,QAAQ,CAAC;YAEnC,MAAMC,qBAAqB,CAACC,MAAWC;gBACrC,oCAAoC;gBACpCD,KAAKE,MAAM,CAAC;YACd;YAEA,MAAMC,iBACJZ,gBAAO,CAACa,UAAU,CAACC,uBAAuB,CAACC,mBAAmB,CAC5D5G;YAEJyG,eAAeI,SAAS,CAACV,GAAG,CAACH,YAAY,CAACc,OAAOR;gBAC/C,IAAI,CAACQ,MAAMC,UAAU,IAAI;gBACzB,OAAOV,mBAAmBC,MAAMQ;YAClC;YAEA9G,YAAYiG,KAAK,CAACe,aAAa,CAACC,UAAU,CACxC;gBACErG,MAAMoF;gBACNkB,OAAOrB,gBAAO,CAACsB,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACnH,SACC,IAAI,CAACH,QAAQ,CACXC,UACAC,aACAC,QACA;oBACEiD;gBACF,GACA/C,OACA;oBAAEC;oBAAiBC;gBAAU;YAInCL,YAAYiG,KAAK,CAACoB,YAAY,CAAClB,GAAG,CAACH,YAAY,CAACsB;gBAC9CA,MAAMrB,KAAK,CAACsB,KAAK,CACdC,GAAG,CAAC,wBACJrB,GAAG,CACF,yBACA,CAAClE,WAAgB,EAAEwF,KAAK,EAAEC,UAAU,EAAO,GACzC,wCAAwC;oBACxCzF,YAAYwF,MAAMC,WAAW,gBAAgBjG;YAErD;QACF;IACF;AACF"}