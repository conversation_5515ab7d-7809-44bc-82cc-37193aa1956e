{"version": 3, "sources": ["../../src/lib/create-client-router-filter.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDynamicRoute", "removeTrailingSlash", "tryToParsePath", "createClientRouterFilter", "paths", "redirects", "allowedErrorRate", "staticPaths", "Set", "dynamicPaths", "path", "subPath", "pathParts", "split", "i", "length", "cur<PERSON><PERSON>", "startsWith", "add", "redirect", "source", "tokens", "every", "token", "staticFilter", "from", "dynamicFilter", "data", "export"], "mappings": "AACA,SAASA,WAAW,QAAQ,6BAA4B;AACxD,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,mBAAmB,QAAQ,mDAAkD;AAEtF,SAASC,cAAc,QAAQ,sBAAqB;AAEpD,OAAO,SAASC,yBACdC,KAAe,EACfC,SAAqB,EACrBC,gBAAyB;IAKzB,MAAMC,cAAc,IAAIC;IACxB,MAAMC,eAAe,IAAID;IAEzB,KAAK,MAAME,QAAQN,MAAO;QACxB,IAAIJ,eAAeU,OAAO;YACxB,IAAIC,UAAU;YACd,MAAMC,YAAYF,KAAKG,KAAK,CAAC;YAE7B,uDAAuD;YACvD,kDAAkD;YAClD,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,GAAG,GAAGD,IAAK;gBAC7C,MAAME,UAAUJ,SAAS,CAACE,EAAE;gBAE5B,IAAIE,QAAQC,UAAU,CAAC,MAAM;oBAC3B;gBACF;gBACAN,UAAU,CAAC,EAAEA,QAAQ,CAAC,EAAEK,QAAQ,CAAC;YACnC;YAEA,IAAIL,SAAS;gBACXF,aAAaS,GAAG,CAACP;YACnB;QACF,OAAO;YACLJ,YAAYW,GAAG,CAACR;QAClB;IACF;IAEA,KAAK,MAAMS,YAAYd,UAAW;QAChC,MAAM,EAAEe,MAAM,EAAE,GAAGD;QACnB,MAAMT,OAAOT,oBAAoBmB;QACjC,IAAIC,SAAkB,EAAE;QAExB,IAAI;YACFA,SAASnB,eAAekB,QAAQC,MAAM,IAAI,EAAE;QAC9C,EAAE,OAAM,CAAC;QAET,IAAIA,OAAOC,KAAK,CAAC,CAACC,QAAU,OAAOA,UAAU,WAAW;YACtD,0CAA0C;YAC1ChB,YAAYW,GAAG,CAACR;QAClB;IACF;IAEA,MAAMc,eAAezB,YAAY0B,IAAI,CAAC;WAAIlB;KAAY,EAAED;IAExD,MAAMoB,gBAAgB3B,YAAY0B,IAAI,CAAC;WAAIhB;KAAa,EAAEH;IAC1D,MAAMqB,OAAO;QACXH,cAAcA,aAAaI,MAAM;QACjCF,eAAeA,cAAcE,MAAM;IACrC;IACA,OAAOD;AACT"}