"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/Auth/AuthPage.js":
/*!*************************************!*\
  !*** ./components/Auth/AuthPage.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AuthPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoginForm */ \"./components/Auth/LoginForm.js\");\n/* harmony import */ var _SignupForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SignupForm */ \"./components/Auth/SignupForm.js\");\n/* harmony import */ var _FirebaseSetupCheck__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../FirebaseSetupCheck */ \"./components/FirebaseSetupCheck.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction AuthPage() {\n    _s();\n    const [isLogin, setIsLogin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const toggleMode = ()=>{\n        setIsLogin(!isLogin);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 left-4 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FirebaseSetupCheck__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\AuthPage.js\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\AuthPage.js\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            isLogin ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoginForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onToggleMode: toggleMode\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\AuthPage.js\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SignupForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onToggleMode: toggleMode\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\AuthPage.js\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Auth\\\\AuthPage.js\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthPage, \"juHMKC6x2j1wnRvCiB5VrABnZyE=\");\n_c = AuthPage;\nvar _c;\n$RefreshReg$(_c, \"AuthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0F1dGgvQXV0aFBhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFpQztBQUNHO0FBQ0U7QUFDaUI7QUFFeEMsU0FBU0k7O0lBQ3RCLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHTiwrQ0FBUUEsQ0FBQztJQUV2QyxNQUFNTyxhQUFhO1FBQ2pCRCxXQUFXLENBQUNEO0lBQ2Q7SUFFQSxxQkFDRSw4REFBQ0c7OzBCQUNDLDhEQUFDQTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ04sMkRBQWtCQTs7Ozs7Ozs7OztZQUVwQkUsd0JBQ0MsOERBQUNKLGtEQUFTQTtnQkFBQ1MsY0FBY0g7Ozs7O3FDQUV6Qiw4REFBQ0wsbURBQVVBO2dCQUFDUSxjQUFjSDs7Ozs7Ozs7Ozs7O0FBSWxDO0dBbkJ3Qkg7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9BdXRoL0F1dGhQYWdlLmpzPzRhMDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTG9naW5Gb3JtIGZyb20gJy4vTG9naW5Gb3JtJztcbmltcG9ydCBTaWdudXBGb3JtIGZyb20gJy4vU2lnbnVwRm9ybSc7XG5pbXBvcnQgRmlyZWJhc2VTZXR1cENoZWNrIGZyb20gJy4uL0ZpcmViYXNlU2V0dXBDaGVjayc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF1dGhQYWdlKCkge1xuICBjb25zdCBbaXNMb2dpbiwgc2V0SXNMb2dpbl0gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICBjb25zdCB0b2dnbGVNb2RlID0gKCkgPT4ge1xuICAgIHNldElzTG9naW4oIWlzTG9naW4pO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTQgbGVmdC00IHJpZ2h0LTQgei01MFwiPlxuICAgICAgICA8RmlyZWJhc2VTZXR1cENoZWNrIC8+XG4gICAgICA8L2Rpdj5cbiAgICAgIHtpc0xvZ2luID8gKFxuICAgICAgICA8TG9naW5Gb3JtIG9uVG9nZ2xlTW9kZT17dG9nZ2xlTW9kZX0gLz5cbiAgICAgICkgOiAoXG4gICAgICAgIDxTaWdudXBGb3JtIG9uVG9nZ2xlTW9kZT17dG9nZ2xlTW9kZX0gLz5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJMb2dpbkZvcm0iLCJTaWdudXBGb3JtIiwiRmlyZWJhc2VTZXR1cENoZWNrIiwiQXV0aFBhZ2UiLCJpc0xvZ2luIiwic2V0SXNMb2dpbiIsInRvZ2dsZU1vZGUiLCJkaXYiLCJjbGFzc05hbWUiLCJvblRvZ2dsZU1vZGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Auth/AuthPage.js\n"));

/***/ })

});