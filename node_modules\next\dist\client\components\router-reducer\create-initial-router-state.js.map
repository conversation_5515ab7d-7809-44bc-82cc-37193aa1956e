{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "names": ["createInitialRouterState", "buildId", "initialTree", "children", "initialCanonicalUrl", "initialParallelRoutes", "isServer", "location", "initialHead", "cache", "status", "CacheStates", "READY", "data", "subTreeData", "parallelRoutes", "Map", "size", "fillLazyItemsTillLeafWithHead", "undefined", "extractPathFromFlightRouterState", "tree", "prefetchCache", "pushRef", "pendingPush", "mpaNavigation", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "canonicalUrl", "createHrefFromUrl", "nextUrl", "pathname"], "mappings": ";;;;+BAoBgBA;;;eAAAA;;;+CAhBY;mCACM;+CACY;oCACG;AAa1C,SAASA,yBAAyB,KASV;IATU,IAAA,EACvCC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,mBAAmB,EACnBC,qBAAqB,EACrBC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACkB,GATU;IAUvC,MAAMC,QAAmB;QACvBC,QAAQC,0CAAW,CAACC,KAAK;QACzBC,MAAM;QACNC,aAAaX;QACb,oJAAoJ;QACpJY,gBAAgBT,WAAW,IAAIU,QAAQX;IACzC;IAEA,yEAAyE;IACzE,IAAIA,0BAA0B,QAAQA,sBAAsBY,IAAI,KAAK,GAAG;QACtEC,IAAAA,4DAA6B,EAACT,OAAOU,WAAWjB,aAAaM;IAC/D;QAsBI,sEAAsE;IACrEY;IArBL,OAAO;QACLnB;QACAoB,MAAMnB;QACNO;QACAa,eAAe,IAAIN;QACnBO,SAAS;YAAEC,aAAa;YAAOC,eAAe;QAAM;QACpDC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAC,cACE,6EAA6E;QAC7E,kJAAkJ;QAClJxB,WAEIyB,IAAAA,oCAAiB,EAACzB,YAClBH;QACN6B,SAEE,CAACb,OAAAA,IAAAA,oDAAgC,EAAClB,iBAAgBK,4BAAAA,SAAU2B,QAAQ,aAAnEd,OACD;IACJ;AACF"}