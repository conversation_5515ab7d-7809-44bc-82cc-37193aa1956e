{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/app-paths.ts"], "names": ["normalizeAppPath", "normalizeRscURL", "normalizePostponedURL", "route", "ensureLeadingSlash", "split", "reduce", "pathname", "segment", "index", "segments", "isGroupSegment", "length", "url", "replace", "parsed", "parse", "startsWith", "substring", "format"], "mappings": ";;;;;;;;;;;;;;;;IAuBgBA,gBAAgB;eAAhBA;;IAmCAC,eAAe;eAAfA;;IAaAC,qBAAqB;eAArBA;;;oCAvEmB;yBACJ;qBACD;AAqBvB,SAASF,iBAAiBG,KAAa;IAC5C,OAAOC,IAAAA,sCAAkB,EACvBD,MAAME,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,IAAII,IAAAA,uBAAc,EAACH,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACE,AAACC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASE,MAAM,GAAG,GAC5B;YACA,OAAOL;QACT;QAEA,OAAO,AAAGA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASP,gBAAgBY,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,8BAA8B;IAC9B;AAEJ;AAOO,SAASZ,sBAAsBW,GAAW;IAC/C,MAAME,SAASC,IAAAA,UAAK,EAACH;IACrB,IAAI,EAAEN,QAAQ,EAAE,GAAGQ;IACnB,IAAIR,YAAYA,SAASU,UAAU,CAAC,qBAAqB;QACvDV,WAAWA,SAASW,SAAS,CAAC,mBAAmBN,MAAM,KAAK;QAE5D,OAAOO,IAAAA,WAAM,EAAC;YAAE,GAAGJ,MAAM;YAAER;QAAS;IACtC;IAEA,OAAOM;AACT"}