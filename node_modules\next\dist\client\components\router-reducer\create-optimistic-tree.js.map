{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-optimistic-tree.ts"], "names": ["createOptimisticTree", "segments", "flightRouterState", "parentRefetch", "existingSegment", "existingParallelRoutes", "url", "refresh", "isRootLayout", "segment", "isLastSegment", "length", "segmentMatches", "matchSegment", "hasMultipleParallelRoutes", "Object", "keys", "shouldRefetchThisLevel", "parallelRoutes", "childTree", "childItem", "slice", "children", "result"], "mappings": ";;;;+BAOg<PERSON>;;;eAAAA;;;+BANa;AAMtB,SAASA,qBACdC,QAAkB,EAClBC,iBAA2C,EAC3CC,aAAsB;IAEtB,MAAM,CAACC,iBAAiBC,wBAAwBC,KAAKC,SAASC,aAAa,GACzEN,qBAAqB;QAAC;QAAM,CAAC;KAAE;IACjC,MAAMO,UAAUR,QAAQ,CAAC,EAAE;IAC3B,MAAMS,gBAAgBT,SAASU,MAAM,KAAK;IAE1C,MAAMC,iBACJR,oBAAoB,QAAQS,IAAAA,2BAAY,EAACT,iBAAiBK;IAE5D,+EAA+E;IAC/E,yEAAyE;IACzE,8CAA8C;IAC9C,MAAMK,4BACJC,OAAOC,IAAI,CAACX,wBAAwBM,MAAM,GAAG;IAC/C,MAAMM,yBACJ,CAACf,qBAAqB,CAACU,kBAAkBE;IAE3C,IAAII,iBAAuC,CAAC;IAC5C,IAAId,oBAAoB,QAAQQ,gBAAgB;QAC9CM,iBAAiBb;IACnB;IAEA,IAAIc;IAEJ,4EAA4E;IAC5E,0EAA0E;IAC1E,0BAA0B;IAC1B,IAAI,CAACT,iBAAiB,CAACI,2BAA2B;QAChD,MAAMM,YAAYpB,qBAChBC,SAASoB,KAAK,CAAC,IACfH,iBAAiBA,eAAeI,QAAQ,GAAG,MAC3CnB,iBAAiBc;QAGnBE,YAAYC;IACd;IAEA,MAAMG,SAA4B;QAChCd;QACA;YACE,GAAGS,cAAc;YACjB,GAAIC,YAAY;gBAAEG,UAAUH;YAAU,IAAI,CAAC,CAAC;QAC9C;KACD;IAED,IAAIb,KAAK;QACPiB,MAAM,CAAC,EAAE,GAAGjB;IACd;IAEA,IAAI,CAACH,iBAAiBc,wBAAwB;QAC5CM,MAAM,CAAC,EAAE,GAAG;IACd,OAAO,IAAIX,kBAAkBL,SAAS;QACpCgB,MAAM,CAAC,EAAE,GAAGhB;IACd;IAEA,IAAIK,kBAAkBJ,cAAc;QAClCe,MAAM,CAAC,EAAE,GAAGf;IACd;IAEA,OAAOe;AACT"}