{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/next-types-plugin/index.ts"], "names": ["fs", "webpack", "sources", "parse", "path", "WEBPACK_LAYERS", "denormalizePagePath", "ensureLeadingSlash", "normalizePathSep", "HTTP_METHODS", "isDynamicRoute", "normalizeAppPath", "getPageFromPath", "devPageFiles", "PLUGIN_NAME", "createTypeGuardFile", "fullPath", "relativePath", "options", "type", "map", "method", "join", "slots", "slot", "collectNamedSlots", "<PERSON><PERSON><PERSON>", "layoutDir", "dirname", "items", "readdir", "withFileTypes", "item", "isDirectory", "name", "startsWith", "push", "slice", "routeTypes", "edge", "static", "dynamic", "node", "extra", "formatRouteToRouteType", "route", "isDynamic", "split", "part", "endsWith", "routeType", "redirectsRewritesTypesProcessed", "addRedirectsRewritesRouteTypes", "rewrites", "redirects", "addExtraRoute", "source", "tokens", "Array", "isArray", "possibleNormalizedRoutes", "slugCnt", "append", "suffix", "i", "length", "fork", "<PERSON><PERSON><PERSON><PERSON>", "token", "slug", "modifier", "prefix", "pattern", "test", "normalizedRoute", "rewrite", "beforeFiles", "afterFiles", "fallback", "redirect", "createRouteDefinitions", "staticRouteTypes", "dynamicRouteTypes", "routeTypesFallback", "appTypesBasePath", "NextTypesPlugin", "constructor", "dir", "distDir", "appDir", "dev", "isEdgeServer", "pageExtensions", "pagesDir", "typedRoutes", "distDirAbsolutePath", "originalRewrites", "originalRedirects", "getRelativePathFromAppTypesDir", "moduleRelativePathToAppDir", "moduleAbsolutePath", "moduleInAppTypesAbsolutePath", "relative", "collectPage", "filePath", "isApp", "sep", "isPages", "apply", "compiler", "assetDirRelative", "handleModule", "mod", "assets", "resource", "layer", "reactServerComponents", "appRouteHandler", "IS_LAYOUT", "IS_PAGE", "IS_ROUTE", "relativePathToApp", "typePath", "replace", "relativeImportPath", "assetPath", "RawSource", "hooks", "compilation", "tap", "processAssets", "tapAsync", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "callback", "promises", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "anyModule", "modules", "concatenatedMod", "Promise", "all", "packageJsonAssetPath", "file", "linkAssetPath"], "mappings": "AAGA,OAAOA,QAAQ,cAAa;AAC5B,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SAASC,KAAK,QAAQ,oCAAmC;AACzD,OAAOC,UAAU,OAAM;AAEvB,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,mBAAmB,QAAQ,yDAAwD;AAC5F,SAASC,kBAAkB,QAAQ,wDAAuD;AAC1F,SAASC,gBAAgB,QAAQ,sDAAqD;AACtF,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,cAAc,QAAQ,sCAAqC;AACpE,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,eAAe,QAAQ,mBAAkB;AAClD,SAASC,YAAY,QAAQ,WAAU;AAEvC,MAAMC,cAAc;AAoBpB,SAASC,oBACPC,QAAgB,EAChBC,YAAoB,EACpBC,OAGC;IAED,OAAO,CAAC,SAAS,EAAEF,SAAS;wBACN,EAAEC,aAAa;AACvC,EACEC,QAAQC,IAAI,KAAK,UACb,CAAC,iDAAiD,CAAC,GACnD,CAAC,8GAA8G,CAAC,CACrH;;6BAE4B,EAAEF,aAAa;;;;EAI1C,EACEC,QAAQC,IAAI,KAAK,UACbV,aAAaW,GAAG,CAAC,CAACC,SAAW,CAAC,EAAEA,OAAO,WAAW,CAAC,EAAEC,IAAI,CAAC,UAC1D,oBACL;;;;;;;;;;EAUD,EACEJ,QAAQC,IAAI,KAAK,UACb,KACA,CAAC;;;;;EAKP,CAAC,CACA;;;AAGH,EACED,QAAQC,IAAI,KAAK,UACbV,aAAaW,GAAG,CACd,CAACC,SAAW,CAAC;KAChB,EAAEA,OAAO;;;;;kBAKI,EAAEA,OAAO;;qDAE0B,EAAEA,OAAO;;OAEvD,EAAEA,OAAO;;;;;;;kBAOE,EAAEA,OAAO;;sDAE2B,EAAEA,OAAO;;OAExD,EAAEA,OAAO;;;EAGd,EACE,GAID;;;;kBAIe,EAAEA,OAAO;;;;kBAIT,EAAEA,OAAO;wDAC6B,EAAEA,OAAO;;OAE1D,EAAEA,OAAO;;;;AAIhB,CAAC,EACOC,IAAI,CAAC,MACP,CAAC;iBACU,EACTJ,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;mBAIY,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;;;mBAMgB,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;AAGH,CAAC,CACA;;;;;;;;;;;;;;AAcD,EACED,QAAQK,KAAK,GACTL,QAAQK,KAAK,CAACH,GAAG,CAAC,CAACI,OAAS,CAAC,EAAE,EAAEA,KAAK,iBAAiB,CAAC,EAAEF,IAAI,CAAC,QAC/D,GACL;;;;;;;;;;;;;;;;AAgBD,EACEJ,QAAQC,IAAI,KAAK,UACb,CAAC;;;;CAIN,CAAC,GACI,GACL;;;;;;;;;AASD,CAAC;AACD;AAEA,eAAeM,kBAAkBC,UAAkB;IACjD,MAAMC,YAAYvB,KAAKwB,OAAO,CAACF;IAC/B,MAAMG,QAAQ,MAAM7B,GAAG8B,OAAO,CAACH,WAAW;QAAEI,eAAe;IAAK;IAChE,MAAMR,QAAQ,EAAE;IAChB,KAAK,MAAMS,QAAQH,MAAO;QACxB,IAAIG,KAAKC,WAAW,MAAMD,KAAKE,IAAI,CAACC,UAAU,CAAC,MAAM;YACnDZ,MAAMa,IAAI,CAACJ,KAAKE,IAAI,CAACG,KAAK,CAAC;QAC7B;IACF;IACA,OAAOd;AACT;AAEA,oEAAoE;AACpE,0EAA0E;AAC1E,8DAA8D;AAC9D,MAAMe,aAGF;IACFC,MAAM;QACJC,QAAQ;QACRC,SAAS;IACX;IACAC,MAAM;QACJF,QAAQ;QACRC,SAAS;IACX;IACAE,OAAO;QACLH,QAAQ;QACRC,SAAS;IACX;AACF;AAEA,SAASG,uBAAuBC,KAAa;IAC3C,MAAMC,YAAYpC,eAAemC;IACjC,IAAIC,WAAW;QACbD,QAAQA,MACLE,KAAK,CAAC,KACN3B,GAAG,CAAC,CAAC4B;YACJ,IAAIA,KAAKb,UAAU,CAAC,QAAQa,KAAKC,QAAQ,CAAC,MAAM;gBAC9C,IAAID,KAAKb,UAAU,CAAC,SAAS;oBAC3B,aAAa;oBACb,OAAO,CAAC,mBAAmB,CAAC;gBAC9B,OAAO,IAAIa,KAAKb,UAAU,CAAC,YAAYa,KAAKC,QAAQ,CAAC,OAAO;oBAC1D,eAAe;oBACf,OAAO,CAAC,2BAA2B,CAAC;gBACtC;gBACA,UAAU;gBACV,OAAO,CAAC,eAAe,CAAC;YAC1B;YACA,OAAOD;QACT,GACC1B,IAAI,CAAC;IACV;IAEA,OAAO;QACLwB;QACAI,WAAW,CAAC,UAAU,EAAEL,MAAM,EAAE,CAAC;IACnC;AACF;AAEA,6EAA6E;AAC7E,IAAIM,kCAAkC;AAEtC,kDAAkD;AAClD,SAASC,+BACPC,QAA8B,EAC9BC,SAAiC;IAEjC,SAASC,cAAcC,MAAc;QACnC,IAAIC;QACJ,IAAI;YACFA,SAAStD,MAAMqD;QACjB,EAAE,OAAM;QACN,gEAAgE;QAClE;QAEA,IAAIE,MAAMC,OAAO,CAACF,SAAS;YACzB,MAAMG,2BAA2B;gBAAC;aAAG;YACrC,IAAIC,UAAU;YAEd,SAASC,OAAOC,MAAc;gBAC5B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,yBAAyBK,MAAM,EAAED,IAAK;oBACxDJ,wBAAwB,CAACI,EAAE,IAAID;gBACjC;YACF;YAEA,SAASG,KAAKH,MAAc;gBAC1B,MAAMI,gBAAgBP,yBAAyBK,MAAM;gBACrD,IAAK,IAAID,IAAI,GAAGA,IAAIG,eAAeH,IAAK;oBACtCJ,yBAAyBxB,IAAI,CAACwB,wBAAwB,CAACI,EAAE,GAAGD;gBAC9D;YACF;YAEA,KAAK,MAAMK,SAASX,OAAQ;gBAC1B,IAAI,OAAOW,UAAU,UAAU;oBAC7B,sCAAsC;oBACtC,MAAMC,OACJD,MAAMlC,IAAI,IAAK2B,CAAAA,cAAc,IAAI,SAAS,CAAC,IAAI,EAAEA,QAAQ,CAAC,AAAD;oBAE3D,IAAIO,MAAME,QAAQ,KAAK,KAAK;wBAC1BR,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,KAAK,EAAEF,KAAK,EAAE,CAAC;oBACxC,OAAO,IAAID,MAAME,QAAQ,KAAK,KAAK;wBACjCR,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;oBACtC,OAAO,IAAID,MAAME,QAAQ,KAAK,IAAI;wBAChC,IAAIF,MAAMI,OAAO,KAAK,gBAAgB;4BACpC,cAAc;4BACdV,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC;wBACnC,OAAO,IAAID,MAAMI,OAAO,KAAK,MAAM;4BACjC,6BAA6B;4BAC7BV,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,KAAK,EAAEF,KAAK,EAAE,CAAC;wBACxC,OAAO,IAAID,MAAMI,OAAO,KAAK,MAAM;4BACjC,mBAAmB;4BACnBV,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;wBACtC,OAAO;4BACL,2DAA2D;4BAC3D;wBACF;oBACF,OAAO,IAAID,MAAME,QAAQ,KAAK,KAAK;wBACjC,IAAI,mBAAmBG,IAAI,CAACL,MAAMI,OAAO,GAAG;4BAC1C,yDAAyD;4BACzDV,OAAOM,MAAMG,MAAM;4BACnBL,KAAKE,MAAMI,OAAO;wBACpB,OAAO;4BACL,8DAA8D;4BAC9D;wBACF;oBACF;gBACF,OAAO,IAAI,OAAOJ,UAAU,UAAU;oBACpCN,OAAOM;gBACT;YACF;YAEA,KAAK,MAAMM,mBAAmBd,yBAA0B;gBACtD,MAAM,EAAEd,SAAS,EAAEI,SAAS,EAAE,GAAGN,uBAAuB8B;gBACxDpC,WAAWK,KAAK,CAACG,YAAY,YAAY,SAAS,IAAII;YACxD;QACF;IACF;IAEA,IAAIG,UAAU;QACZ,KAAK,MAAMsB,WAAWtB,SAASuB,WAAW,CAAE;YAC1CrB,cAAcoB,QAAQnB,MAAM;QAC9B;QACA,KAAK,MAAMmB,WAAWtB,SAASwB,UAAU,CAAE;YACzCtB,cAAcoB,QAAQnB,MAAM;QAC9B;QACA,KAAK,MAAMmB,WAAWtB,SAASyB,QAAQ,CAAE;YACvCvB,cAAcoB,QAAQnB,MAAM;QAC9B;IACF;IAEA,IAAIF,WAAW;QACb,KAAK,MAAMyB,YAAYzB,UAAW;YAChC,0BAA0B;YAC1B,wIAAwI;YACxI,IAAI,CAAE,CAAA,cAAcyB,QAAO,GAAI;gBAC7BxB,cAAcwB,SAASvB,MAAM;YAC/B;QACF;IACF;AACF;AAEA,SAASwB;IACP,IAAIC,mBAAmB;IACvB,IAAIC,oBAAoB;IAExB,KAAK,MAAM/D,QAAQ;QAAC;QAAQ;QAAQ;KAAQ,CAAW;QACrD8D,oBAAoB3C,UAAU,CAACnB,KAAK,CAACqB,MAAM;QAC3C0C,qBAAqB5C,UAAU,CAACnB,KAAK,CAACsB,OAAO;IAC/C;IAEA,+EAA+E;IAC/E,MAAM0C,qBACJ,CAACF,oBAAoB,CAACC,oBAAoB,WAAW;IAEvD,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBA8BY,EAAED,oBAAoB,QAAQ;kDACF,EAC9CC,qBAAqB,QACtB;;sBAEmB,EAClBC,sBACA,CAAC;IACD,EACE,uDAAuD;IACvD,iBACD;;;;;IAKD,CAAC,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DH,CAAC;AACD;AAEA,MAAMC,mBAAmBhF,KAAKkB,IAAI,CAAC,SAAS;AAE5C,OAAO,MAAM+D;IAWXC,YAAYpE,OAAgB,CAAE;QAC5B,IAAI,CAACqE,GAAG,GAAGrE,QAAQqE,GAAG;QACtB,IAAI,CAACC,OAAO,GAAGtE,QAAQsE,OAAO;QAC9B,IAAI,CAACC,MAAM,GAAGvE,QAAQuE,MAAM;QAC5B,IAAI,CAACC,GAAG,GAAGxE,QAAQwE,GAAG;QACtB,IAAI,CAACC,YAAY,GAAGzE,QAAQyE,YAAY;QACxC,IAAI,CAACC,cAAc,GAAG1E,QAAQ0E,cAAc;QAC5C,IAAI,CAACC,QAAQ,GAAGzF,KAAKkB,IAAI,CAAC,IAAI,CAACmE,MAAM,EAAE,MAAM;QAC7C,IAAI,CAACK,WAAW,GAAG5E,QAAQ4E,WAAW;QACtC,IAAI,CAACC,mBAAmB,GAAG3F,KAAKkB,IAAI,CAAC,IAAI,CAACiE,GAAG,EAAE,IAAI,CAACC,OAAO;QAC3D,IAAI,IAAI,CAACM,WAAW,IAAI,CAAC3C,iCAAiC;YACxDA,kCAAkC;YAClCC,+BACElC,QAAQ8E,gBAAgB,EACxB9E,QAAQ+E,iBAAiB;QAE7B;IACF;IAEAC,+BAA+BC,0BAAkC,EAAE;QACjE,MAAMC,qBAAqBhG,KAAKkB,IAAI,CAClC,IAAI,CAACmE,MAAM,EACXU;QAGF,MAAME,+BAA+BjG,KAAKkB,IAAI,CAC5C,IAAI,CAACyE,mBAAmB,EACxBX,kBACAe;QAGF,OAAO/F,KAAKkG,QAAQ,CAClBD,+BAA+B,OAC/BD;IAEJ;IAEAG,YAAYC,QAAgB,EAAE;QAC5B,IAAI,CAAC,IAAI,CAACV,WAAW,EAAE;QAEvB,MAAMW,QAAQD,SAASrE,UAAU,CAAC,IAAI,CAACsD,MAAM,GAAGrF,KAAKsG,GAAG;QACxD,MAAMC,UAAU,CAACF,SAASD,SAASrE,UAAU,CAAC,IAAI,CAAC0D,QAAQ,GAAGzF,KAAKsG,GAAG;QAEtE,IAAI,CAACD,SAAS,CAACE,SAAS;YACtB;QACF;QAEA,qDAAqD;QACrD,IAAIF,SAAS,CAAC,8BAA8BhC,IAAI,CAAC+B,WAAW;YAC1D;QACF;QAEA,yCAAyC;QACzC,IACEG,WACA,iDAAiDlC,IAAI,CAAC+B,WACtD;YACA;QACF;QAEA,IAAI3D,QAAQ,AAAC4D,CAAAA,QAAQ9F,mBAAmBL,mBAAkB,EACxDC,mBACEK,gBACER,KAAKkG,QAAQ,CAACG,QAAQ,IAAI,CAAChB,MAAM,GAAG,IAAI,CAACI,QAAQ,EAAEW,WACnD,IAAI,CAACZ,cAAc;QAKzB,MAAM,EAAE9C,SAAS,EAAEI,SAAS,EAAE,GAAGN,uBAAuBC;QAExDP,UAAU,CAAC,IAAI,CAACqD,YAAY,GAAG,SAAS,OAAO,CAC7C7C,YAAY,YAAY,SACzB,IAAII;IACP;IAEA0D,MAAMC,QAA0B,EAAE;QAChC,+BAA+B;QAC/B,MAAMC,mBAAmB,IAAI,CAACpB,GAAG,GAC7B,OACA,IAAI,CAACC,YAAY,GACjB,OACA;QAEJ,MAAMoB,eAAe,OAAOC,KAA2BC;YACrD,IAAI,CAACD,IAAIE,QAAQ,EAAE;YAEnB,IAAI,CAAC,yBAAyBzC,IAAI,CAACuC,IAAIE,QAAQ,GAAG;YAElD,IAAI,CAACF,IAAIE,QAAQ,CAAC/E,UAAU,CAAC,IAAI,CAACsD,MAAM,GAAGrF,KAAKsG,GAAG,GAAG;gBACpD,IAAI,CAAC,IAAI,CAAChB,GAAG,EAAE;oBACb,IAAIsB,IAAIE,QAAQ,CAAC/E,UAAU,CAAC,IAAI,CAAC0D,QAAQ,GAAGzF,KAAKsG,GAAG,GAAG;wBACrD,IAAI,CAACH,WAAW,CAACS,IAAIE,QAAQ;oBAC/B;gBACF;gBACA;YACF;YACA,IACEF,IAAIG,KAAK,KAAK9G,eAAe+G,qBAAqB,IAClDJ,IAAIG,KAAK,KAAK9G,eAAegH,eAAe,EAE5C;YAEF,MAAMC,YAAY,yBAAyB7C,IAAI,CAACuC,IAAIE,QAAQ;YAC5D,MAAMK,UAAU,CAACD,aAAa,oBAAoB7C,IAAI,CAACuC,IAAIE,QAAQ;YACnE,MAAMM,WAAW,CAACD,WAAW,qBAAqB9C,IAAI,CAACuC,IAAIE,QAAQ;YACnE,MAAMO,oBAAoBrH,KAAKkG,QAAQ,CAAC,IAAI,CAACb,MAAM,EAAEuB,IAAIE,QAAQ;YAEjE,IAAI,CAAC,IAAI,CAACxB,GAAG,EAAE;gBACb,IAAI6B,WAAWC,UAAU;oBACvB,IAAI,CAACjB,WAAW,CAACS,IAAIE,QAAQ;gBAC/B;YACF;YAEA,MAAMQ,WAAWtH,KAAKkB,IAAI,CACxB8D,kBACAqC,kBAAkBE,OAAO,CAAC,0BAA0B;YAEtD,MAAMC,qBAAqBpH,iBACzBJ,KACGkB,IAAI,CAAC,IAAI,CAAC4E,8BAA8B,CAACuB,oBACzCE,OAAO,CAAC,0BAA0B;YAGvC,MAAME,YAAYzH,KAAKkB,IAAI,CAACwF,kBAAkBY;YAE9C,IAAIJ,WAAW;gBACb,MAAM/F,QAAQ,MAAME,kBAAkBuF,IAAIE,QAAQ;gBAClDD,MAAM,CAACY,UAAU,GAAG,IAAI3H,QAAQ4H,SAAS,CACvC/G,oBAAoBiG,IAAIE,QAAQ,EAAEU,oBAAoB;oBACpDzG,MAAM;oBACNI;gBACF;YAEJ,OAAO,IAAIgG,SAAS;gBAClBN,MAAM,CAACY,UAAU,GAAG,IAAI3H,QAAQ4H,SAAS,CACvC/G,oBAAoBiG,IAAIE,QAAQ,EAAEU,oBAAoB;oBACpDzG,MAAM;gBACR;YAEJ,OAAO,IAAIqG,UAAU;gBACnBP,MAAM,CAACY,UAAU,GAAG,IAAI3H,QAAQ4H,SAAS,CACvC/G,oBAAoBiG,IAAIE,QAAQ,EAAEU,oBAAoB;oBACpDzG,MAAM;gBACR;YAEJ;QACF;QAEA0F,SAASkB,KAAK,CAACC,WAAW,CAACC,GAAG,CAACnH,aAAa,CAACkH;YAC3CA,YAAYD,KAAK,CAACG,aAAa,CAACC,QAAQ,CACtC;gBACEjG,MAAMpB;gBACNsH,OAAOnI,QAAQoI,WAAW,CAACC,kCAAkC;YAC/D,GACA,OAAOrB,QAAQsB;gBACb,MAAMC,WAA2B,EAAE;gBAEnC,eAAe;gBACf,IAAI,IAAI,CAAC7C,YAAY,EAAE;oBACrBrD,WAAWC,IAAI,CAACE,OAAO,GAAG;oBAC1BH,WAAWC,IAAI,CAACC,MAAM,GAAG;gBAC3B,OAAO;oBACLF,WAAWI,IAAI,CAACD,OAAO,GAAG;oBAC1BH,WAAWI,IAAI,CAACF,MAAM,GAAG;gBAC3B;gBAEAwF,YAAYS,WAAW,CAACC,OAAO,CAAC,CAACC;oBAC/BA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;wBACzB,IAAI,CAACA,MAAM3G,IAAI,EAAE;wBAEjB,4CAA4C;wBAC5C,IACE,CAAC2G,MAAM3G,IAAI,CAACC,UAAU,CAAC,aACvB,CACE0G,CAAAA,MAAM3G,IAAI,CAACC,UAAU,CAAC,WACrB0G,CAAAA,MAAM3G,IAAI,CAACe,QAAQ,CAAC,YACnB4F,MAAM3G,IAAI,CAACe,QAAQ,CAAC,SAAQ,CAAC,GAEjC;4BACA;wBACF;wBAEA,MAAM6F,eACJd,YAAYe,UAAU,CAACC,uBAAuB,CAC5CH;wBAEJ,KAAK,MAAM7B,OAAO8B,aAAc;4BAC9BN,SAASpG,IAAI,CAAC2E,aAAaC,KAAKC;4BAEhC,oEAAoE;4BACpE,MAAMgC,YAAYjC;4BAGlB,IAAIiC,UAAUC,OAAO,EAAE;gCACrBD,UAAUC,OAAO,CAACR,OAAO,CAAC,CAACS;oCACzBX,SAASpG,IAAI,CAAC2E,aAAaoC,iBAAiBlC;gCAC9C;4BACF;wBACF;oBACF;gBACF;gBAEA,MAAMmC,QAAQC,GAAG,CAACb;gBAElB,8EAA8E;gBAE9E,MAAMc,uBAAuBlJ,KAAKkB,IAAI,CACpCwF,kBACA;gBAGFG,MAAM,CAACqC,qBAAqB,GAAG,IAAIpJ,QAAQ4H,SAAS,CAClD;gBAGF,IAAI,IAAI,CAAChC,WAAW,EAAE;oBACpB,IAAI,IAAI,CAACJ,GAAG,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;wBAClC9E,aAAa6H,OAAO,CAAC,CAACa;4BACpB,IAAI,CAAChD,WAAW,CAACgD;wBACnB;oBACF;oBAEA,MAAMC,gBAAgBpJ,KAAKkB,IAAI,CAACwF,kBAAkB;oBAElDG,MAAM,CAACuC,cAAc,GAAG,IAAItJ,QAAQ4H,SAAS,CAC3C9C;gBAEJ;gBAEAuD;YACF;QAEJ;IACF;AACF"}