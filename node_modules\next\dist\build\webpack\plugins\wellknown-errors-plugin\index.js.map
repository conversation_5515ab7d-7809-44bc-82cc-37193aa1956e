{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/index.ts"], "names": ["WellKnownErrorsPlugin", "NAME", "apply", "compiler", "hooks", "compilation", "tap", "afterSeal", "tapPromise", "warnings", "length", "Promise", "all", "map", "warn", "i", "name", "module", "context", "includes", "errors", "err", "moduleError", "getModuleBuildError", "e", "console", "log"], "mappings": ";;;;+BAKa<PERSON>;;;eAAAA;;;oCAHuB;AAEpC,MAAMC,OAAO;AACN,MAAMD;IACXE,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAACL,MAAM,CAACI;YACpCA,YAAYD,KAAK,CAACG,SAAS,CAACC,UAAU,CAACP,MAAM;oBACvCI,uBAaAA;gBAbJ,KAAIA,wBAAAA,YAAYI,QAAQ,qBAApBJ,sBAAsBK,MAAM,EAAE;oBAChC,MAAMC,QAAQC,GAAG,CACfP,YAAYI,QAAQ,CAACI,GAAG,CAAC,OAAOC,MAAMC;4BAGlCD;wBAFF,IACEA,KAAKE,IAAI,KAAK,+BACdF,uBAAAA,KAAKG,MAAM,CAACC,OAAO,qBAAnBJ,qBAAqBK,QAAQ,CAAC,kBAC9B;4BACA,OAAOd,YAAYI,QAAQ,CAACM,EAAE;wBAChC;oBACF;gBAEJ;gBAEA,KAAIV,sBAAAA,YAAYe,MAAM,qBAAlBf,oBAAoBK,MAAM,EAAE;oBAC9B,MAAMC,QAAQC,GAAG,CACfP,YAAYe,MAAM,CAACP,GAAG,CAAC,OAAOQ,KAAKN;wBACjC,IAAI;4BACF,MAAMO,cAAc,MAAMC,IAAAA,uCAAmB,EAC3CpB,UACAE,aACAgB;4BAEF,IAAIC,gBAAgB,OAAO;gCACzBjB,YAAYe,MAAM,CAACL,EAAE,GAAGO;4BAC1B;wBACF,EAAE,OAAOE,GAAG;4BACVC,QAAQC,GAAG,CAACF;wBACd;oBACF;gBAEJ;YACF;QACF;IACF;AACF"}