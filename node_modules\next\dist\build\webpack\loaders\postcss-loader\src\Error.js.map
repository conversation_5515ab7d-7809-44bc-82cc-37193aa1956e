{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/postcss-loader/src/Error.ts"], "names": ["PostCSSSyntaxError", "Error", "constructor", "error", "line", "column", "reason", "plugin", "file", "name", "message", "code", "showSourceCode", "stack"], "mappings": "AAAA;;;;;;;;;CASC;;;;+BACD;;;eAAqBA;;;AAAN,MAAMA,2BAA2BC;IAE9CC,YAAYC,KAAU,CAAE;QACtB,KAAK,CAACA;QAEN,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE,GAAGL;QAE/C,IAAI,CAACM,IAAI,GAAG;QAEZ,IAAI,CAACC,OAAO,GAAG,CAAC,EAAE,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;QAEjC,IAAI,OAAOL,SAAS,aAAa;YAC/B,IAAI,CAACM,OAAO,IAAI,CAAC,CAAC,EAAEN,KAAK,CAAC,EAAEC,OAAO,EAAE,CAAC;QACxC;QAEA,IAAI,CAACK,OAAO,IAAIH,SAAS,CAAC,EAAEA,OAAO,EAAE,CAAC,GAAG;QACzC,IAAI,CAACG,OAAO,IAAIF,OAAO,CAAC,EAAEA,KAAK,CAAC,CAAC,GAAG;QACpC,IAAI,CAACE,OAAO,IAAI,CAAC,EAAEJ,OAAO,CAAC;QAE3B,MAAMK,OAAOR,MAAMS,cAAc;QAEjC,IAAID,MAAM;YACR,IAAI,CAACD,OAAO,IAAI,CAAC,IAAI,EAAEC,KAAK,EAAE,CAAC;QACjC;QAEA,IAAI,CAACE,KAAK,GAAG;IACf;AACF"}