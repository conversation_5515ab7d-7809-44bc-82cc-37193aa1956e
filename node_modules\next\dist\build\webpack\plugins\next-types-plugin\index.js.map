{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/next-types-plugin/index.ts"], "names": ["NextTypesPlugin", "PLUGIN_NAME", "createTypeGuardFile", "fullPath", "relativePath", "options", "type", "HTTP_METHODS", "map", "method", "join", "slots", "slot", "collectNamedSlots", "<PERSON><PERSON><PERSON>", "layoutDir", "path", "dirname", "items", "fs", "readdir", "withFileTypes", "item", "isDirectory", "name", "startsWith", "push", "slice", "routeTypes", "edge", "static", "dynamic", "node", "extra", "formatRouteToRouteType", "route", "isDynamic", "isDynamicRoute", "split", "part", "endsWith", "routeType", "redirectsRewritesTypesProcessed", "addRedirectsRewritesRouteTypes", "rewrites", "redirects", "addExtraRoute", "source", "tokens", "parse", "Array", "isArray", "possibleNormalizedRoutes", "slugCnt", "append", "suffix", "i", "length", "fork", "<PERSON><PERSON><PERSON><PERSON>", "token", "slug", "modifier", "prefix", "pattern", "test", "normalizedRoute", "rewrite", "beforeFiles", "afterFiles", "fallback", "redirect", "createRouteDefinitions", "staticRouteTypes", "dynamicRouteTypes", "routeTypesFallback", "appTypesBasePath", "constructor", "dir", "distDir", "appDir", "dev", "isEdgeServer", "pageExtensions", "pagesDir", "typedRoutes", "distDirAbsolutePath", "originalRewrites", "originalRedirects", "getRelativePathFromAppTypesDir", "moduleRelativePathToAppDir", "moduleAbsolutePath", "moduleInAppTypesAbsolutePath", "relative", "collectPage", "filePath", "isApp", "sep", "isPages", "normalizeAppPath", "denormalizePagePath", "ensureLeadingSlash", "getPageFromPath", "apply", "compiler", "assetDirRelative", "handleModule", "mod", "assets", "resource", "layer", "WEBPACK_LAYERS", "reactServerComponents", "appRouteHandler", "IS_LAYOUT", "IS_PAGE", "IS_ROUTE", "relativePathToApp", "typePath", "replace", "relativeImportPath", "normalizePathSep", "assetPath", "sources", "RawSource", "hooks", "compilation", "tap", "processAssets", "tapAsync", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "callback", "promises", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "anyModule", "modules", "concatenatedMod", "Promise", "all", "packageJsonAssetPath", "devPageFiles", "file", "linkAssetPath"], "mappings": ";;;;+BAkfaA;;;eAAAA;;;iEA/eE;yBACkB;8BACX;6DACL;2BAEc;qCACK;oCACD;kCACF;sBACJ;uBACE;0BACE;yBACD;wBACH;;;;;;AAE7B,MAAMC,cAAc;AAoBpB,SAASC,oBACPC,QAAgB,EAChBC,YAAoB,EACpBC,OAGC;IAED,OAAO,CAAC,SAAS,EAAEF,SAAS;wBACN,EAAEC,aAAa;AACvC,EACEC,QAAQC,IAAI,KAAK,UACb,CAAC,iDAAiD,CAAC,GACnD,CAAC,8GAA8G,CAAC,CACrH;;6BAE4B,EAAEF,aAAa;;;;EAI1C,EACEC,QAAQC,IAAI,KAAK,UACbC,kBAAY,CAACC,GAAG,CAAC,CAACC,SAAW,CAAC,EAAEA,OAAO,WAAW,CAAC,EAAEC,IAAI,CAAC,UAC1D,oBACL;;;;;;;;;;EAUD,EACEL,QAAQC,IAAI,KAAK,UACb,KACA,CAAC;;;;;EAKP,CAAC,CACA;;;AAGH,EACED,QAAQC,IAAI,KAAK,UACbC,kBAAY,CAACC,GAAG,CACd,CAACC,SAAW,CAAC;KAChB,EAAEA,OAAO;;;;;kBAKI,EAAEA,OAAO;;qDAE0B,EAAEA,OAAO;;OAEvD,EAAEA,OAAO;;;;;;;kBAOE,EAAEA,OAAO;;sDAE2B,EAAEA,OAAO;;OAExD,EAAEA,OAAO;;;EAGd,EACE,GAID;;;;kBAIe,EAAEA,OAAO;;;;kBAIT,EAAEA,OAAO;wDAC6B,EAAEA,OAAO;;OAE1D,EAAEA,OAAO;;;;AAIhB,CAAC,EACOC,IAAI,CAAC,MACP,CAAC;iBACU,EACTL,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;mBAIY,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;;;mBAMgB,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;AAGH,CAAC,CACA;;;;;;;;;;;;;;AAcD,EACED,QAAQM,KAAK,GACTN,QAAQM,KAAK,CAACH,GAAG,CAAC,CAACI,OAAS,CAAC,EAAE,EAAEA,KAAK,iBAAiB,CAAC,EAAEF,IAAI,CAAC,QAC/D,GACL;;;;;;;;;;;;;;;;AAgBD,EACEL,QAAQC,IAAI,KAAK,UACb,CAAC;;;;CAIN,CAAC,GACI,GACL;;;;;;;;;AASD,CAAC;AACD;AAEA,eAAeO,kBAAkBC,UAAkB;IACjD,MAAMC,YAAYC,aAAI,CAACC,OAAO,CAACH;IAC/B,MAAMI,QAAQ,MAAMC,iBAAE,CAACC,OAAO,CAACL,WAAW;QAAEM,eAAe;IAAK;IAChE,MAAMV,QAAQ,EAAE;IAChB,KAAK,MAAMW,QAAQJ,MAAO;QACxB,IAAII,KAAKC,WAAW,MAAMD,KAAKE,IAAI,CAACC,UAAU,CAAC,MAAM;YACnDd,MAAMe,IAAI,CAACJ,KAAKE,IAAI,CAACG,KAAK,CAAC;QAC7B;IACF;IACA,OAAOhB;AACT;AAEA,oEAAoE;AACpE,0EAA0E;AAC1E,8DAA8D;AAC9D,MAAMiB,aAGF;IACFC,MAAM;QACJC,QAAQ;QACRC,SAAS;IACX;IACAC,MAAM;QACJF,QAAQ;QACRC,SAAS;IACX;IACAE,OAAO;QACLH,QAAQ;QACRC,SAAS;IACX;AACF;AAEA,SAASG,uBAAuBC,KAAa;IAC3C,MAAMC,YAAYC,IAAAA,qBAAc,EAACF;IACjC,IAAIC,WAAW;QACbD,QAAQA,MACLG,KAAK,CAAC,KACN9B,GAAG,CAAC,CAAC+B;YACJ,IAAIA,KAAKd,UAAU,CAAC,QAAQc,KAAKC,QAAQ,CAAC,MAAM;gBAC9C,IAAID,KAAKd,UAAU,CAAC,SAAS;oBAC3B,aAAa;oBACb,OAAO,CAAC,mBAAmB,CAAC;gBAC9B,OAAO,IAAIc,KAAKd,UAAU,CAAC,YAAYc,KAAKC,QAAQ,CAAC,OAAO;oBAC1D,eAAe;oBACf,OAAO,CAAC,2BAA2B,CAAC;gBACtC;gBACA,UAAU;gBACV,OAAO,CAAC,eAAe,CAAC;YAC1B;YACA,OAAOD;QACT,GACC7B,IAAI,CAAC;IACV;IAEA,OAAO;QACL0B;QACAK,WAAW,CAAC,UAAU,EAAEN,MAAM,EAAE,CAAC;IACnC;AACF;AAEA,6EAA6E;AAC7E,IAAIO,kCAAkC;AAEtC,kDAAkD;AAClD,SAASC,+BACPC,QAA8B,EAC9BC,SAAiC;IAEjC,SAASC,cAAcC,MAAc;QACnC,IAAIC;QACJ,IAAI;YACFA,SAASC,IAAAA,mBAAK,EAACF;QACjB,EAAE,OAAM;QACN,gEAAgE;QAClE;QAEA,IAAIG,MAAMC,OAAO,CAACH,SAAS;YACzB,MAAMI,2BAA2B;gBAAC;aAAG;YACrC,IAAIC,UAAU;YAEd,SAASC,OAAOC,MAAc;gBAC5B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,yBAAyBK,MAAM,EAAED,IAAK;oBACxDJ,wBAAwB,CAACI,EAAE,IAAID;gBACjC;YACF;YAEA,SAASG,KAAKH,MAAc;gBAC1B,MAAMI,gBAAgBP,yBAAyBK,MAAM;gBACrD,IAAK,IAAID,IAAI,GAAGA,IAAIG,eAAeH,IAAK;oBACtCJ,yBAAyB1B,IAAI,CAAC0B,wBAAwB,CAACI,EAAE,GAAGD;gBAC9D;YACF;YAEA,KAAK,MAAMK,SAASZ,OAAQ;gBAC1B,IAAI,OAAOY,UAAU,UAAU;oBAC7B,sCAAsC;oBACtC,MAAMC,OACJD,MAAMpC,IAAI,IAAK6B,CAAAA,cAAc,IAAI,SAAS,CAAC,IAAI,EAAEA,QAAQ,CAAC,AAAD;oBAE3D,IAAIO,MAAME,QAAQ,KAAK,KAAK;wBAC1BR,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,KAAK,EAAEF,KAAK,EAAE,CAAC;oBACxC,OAAO,IAAID,MAAME,QAAQ,KAAK,KAAK;wBACjCR,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;oBACtC,OAAO,IAAID,MAAME,QAAQ,KAAK,IAAI;wBAChC,IAAIF,MAAMI,OAAO,KAAK,gBAAgB;4BACpC,cAAc;4BACdV,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC;wBACnC,OAAO,IAAID,MAAMI,OAAO,KAAK,MAAM;4BACjC,6BAA6B;4BAC7BV,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,KAAK,EAAEF,KAAK,EAAE,CAAC;wBACxC,OAAO,IAAID,MAAMI,OAAO,KAAK,MAAM;4BACjC,mBAAmB;4BACnBV,OAAO,CAAC,EAAEM,MAAMG,MAAM,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;wBACtC,OAAO;4BACL,2DAA2D;4BAC3D;wBACF;oBACF,OAAO,IAAID,MAAME,QAAQ,KAAK,KAAK;wBACjC,IAAI,mBAAmBG,IAAI,CAACL,MAAMI,OAAO,GAAG;4BAC1C,yDAAyD;4BACzDV,OAAOM,MAAMG,MAAM;4BACnBL,KAAKE,MAAMI,OAAO;wBACpB,OAAO;4BACL,8DAA8D;4BAC9D;wBACF;oBACF;gBACF,OAAO,IAAI,OAAOJ,UAAU,UAAU;oBACpCN,OAAOM;gBACT;YACF;YAEA,KAAK,MAAMM,mBAAmBd,yBAA0B;gBACtD,MAAM,EAAEhB,SAAS,EAAEK,SAAS,EAAE,GAAGP,uBAAuBgC;gBACxDtC,WAAWK,KAAK,CAACG,YAAY,YAAY,SAAS,IAAIK;YACxD;QACF;IACF;IAEA,IAAIG,UAAU;QACZ,KAAK,MAAMuB,WAAWvB,SAASwB,WAAW,CAAE;YAC1CtB,cAAcqB,QAAQpB,MAAM;QAC9B;QACA,KAAK,MAAMoB,WAAWvB,SAASyB,UAAU,CAAE;YACzCvB,cAAcqB,QAAQpB,MAAM;QAC9B;QACA,KAAK,MAAMoB,WAAWvB,SAAS0B,QAAQ,CAAE;YACvCxB,cAAcqB,QAAQpB,MAAM;QAC9B;IACF;IAEA,IAAIF,WAAW;QACb,KAAK,MAAM0B,YAAY1B,UAAW;YAChC,0BAA0B;YAC1B,wIAAwI;YACxI,IAAI,CAAE,CAAA,cAAc0B,QAAO,GAAI;gBAC7BzB,cAAcyB,SAASxB,MAAM;YAC/B;QACF;IACF;AACF;AAEA,SAASyB;IACP,IAAIC,mBAAmB;IACvB,IAAIC,oBAAoB;IAExB,KAAK,MAAMpE,QAAQ;QAAC;QAAQ;QAAQ;KAAQ,CAAW;QACrDmE,oBAAoB7C,UAAU,CAACtB,KAAK,CAACwB,MAAM;QAC3C4C,qBAAqB9C,UAAU,CAACtB,KAAK,CAACyB,OAAO;IAC/C;IAEA,+EAA+E;IAC/E,MAAM4C,qBACJ,CAACF,oBAAoB,CAACC,oBAAoB,WAAW;IAEvD,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBA8BY,EAAED,oBAAoB,QAAQ;kDACF,EAC9CC,qBAAqB,QACtB;;sBAEmB,EAClBC,sBACA,CAAC;IACD,EACE,uDAAuD;IACvD,iBACD;;;;;IAKD,CAAC,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DH,CAAC;AACD;AAEA,MAAMC,mBAAmB5D,aAAI,CAACN,IAAI,CAAC,SAAS;AAErC,MAAMV;IAWX6E,YAAYxE,OAAgB,CAAE;QAC5B,IAAI,CAACyE,GAAG,GAAGzE,QAAQyE,GAAG;QACtB,IAAI,CAACC,OAAO,GAAG1E,QAAQ0E,OAAO;QAC9B,IAAI,CAACC,MAAM,GAAG3E,QAAQ2E,MAAM;QAC5B,IAAI,CAACC,GAAG,GAAG5E,QAAQ4E,GAAG;QACtB,IAAI,CAACC,YAAY,GAAG7E,QAAQ6E,YAAY;QACxC,IAAI,CAACC,cAAc,GAAG9E,QAAQ8E,cAAc;QAC5C,IAAI,CAACC,QAAQ,GAAGpE,aAAI,CAACN,IAAI,CAAC,IAAI,CAACsE,MAAM,EAAE,MAAM;QAC7C,IAAI,CAACK,WAAW,GAAGhF,QAAQgF,WAAW;QACtC,IAAI,CAACC,mBAAmB,GAAGtE,aAAI,CAACN,IAAI,CAAC,IAAI,CAACoE,GAAG,EAAE,IAAI,CAACC,OAAO;QAC3D,IAAI,IAAI,CAACM,WAAW,IAAI,CAAC3C,iCAAiC;YACxDA,kCAAkC;YAClCC,+BACEtC,QAAQkF,gBAAgB,EACxBlF,QAAQmF,iBAAiB;QAE7B;IACF;IAEAC,+BAA+BC,0BAAkC,EAAE;QACjE,MAAMC,qBAAqB3E,aAAI,CAACN,IAAI,CAClC,IAAI,CAACsE,MAAM,EACXU;QAGF,MAAME,+BAA+B5E,aAAI,CAACN,IAAI,CAC5C,IAAI,CAAC4E,mBAAmB,EACxBV,kBACAc;QAGF,OAAO1E,aAAI,CAAC6E,QAAQ,CAClBD,+BAA+B,OAC/BD;IAEJ;IAEAG,YAAYC,QAAgB,EAAE;QAC5B,IAAI,CAAC,IAAI,CAACV,WAAW,EAAE;QAEvB,MAAMW,QAAQD,SAAStE,UAAU,CAAC,IAAI,CAACuD,MAAM,GAAGhE,aAAI,CAACiF,GAAG;QACxD,MAAMC,UAAU,CAACF,SAASD,SAAStE,UAAU,CAAC,IAAI,CAAC2D,QAAQ,GAAGpE,aAAI,CAACiF,GAAG;QAEtE,IAAI,CAACD,SAAS,CAACE,SAAS;YACtB;QACF;QAEA,qDAAqD;QACrD,IAAIF,SAAS,CAAC,8BAA8B/B,IAAI,CAAC8B,WAAW;YAC1D;QACF;QAEA,yCAAyC;QACzC,IACEG,WACA,iDAAiDjC,IAAI,CAAC8B,WACtD;YACA;QACF;QAEA,IAAI5D,QAAQ,AAAC6D,CAAAA,QAAQG,0BAAgB,GAAGC,wCAAmB,AAAD,EACxDC,IAAAA,sCAAkB,EAChBC,IAAAA,wBAAe,EACbtF,aAAI,CAAC6E,QAAQ,CAACG,QAAQ,IAAI,CAAChB,MAAM,GAAG,IAAI,CAACI,QAAQ,EAAEW,WACnD,IAAI,CAACZ,cAAc;QAKzB,MAAM,EAAE/C,SAAS,EAAEK,SAAS,EAAE,GAAGP,uBAAuBC;QAExDP,UAAU,CAAC,IAAI,CAACsD,YAAY,GAAG,SAAS,OAAO,CAC7C9C,YAAY,YAAY,SACzB,IAAIK;IACP;IAEA8D,MAAMC,QAA0B,EAAE;QAChC,+BAA+B;QAC/B,MAAMC,mBAAmB,IAAI,CAACxB,GAAG,GAC7B,OACA,IAAI,CAACC,YAAY,GACjB,OACA;QAEJ,MAAMwB,eAAe,OAAOC,KAA2BC;YACrD,IAAI,CAACD,IAAIE,QAAQ,EAAE;YAEnB,IAAI,CAAC,yBAAyB5C,IAAI,CAAC0C,IAAIE,QAAQ,GAAG;YAElD,IAAI,CAACF,IAAIE,QAAQ,CAACpF,UAAU,CAAC,IAAI,CAACuD,MAAM,GAAGhE,aAAI,CAACiF,GAAG,GAAG;gBACpD,IAAI,CAAC,IAAI,CAAChB,GAAG,EAAE;oBACb,IAAI0B,IAAIE,QAAQ,CAACpF,UAAU,CAAC,IAAI,CAAC2D,QAAQ,GAAGpE,aAAI,CAACiF,GAAG,GAAG;wBACrD,IAAI,CAACH,WAAW,CAACa,IAAIE,QAAQ;oBAC/B;gBACF;gBACA;YACF;YACA,IACEF,IAAIG,KAAK,KAAKC,yBAAc,CAACC,qBAAqB,IAClDL,IAAIG,KAAK,KAAKC,yBAAc,CAACE,eAAe,EAE5C;YAEF,MAAMC,YAAY,yBAAyBjD,IAAI,CAAC0C,IAAIE,QAAQ;YAC5D,MAAMM,UAAU,CAACD,aAAa,oBAAoBjD,IAAI,CAAC0C,IAAIE,QAAQ;YACnE,MAAMO,WAAW,CAACD,WAAW,qBAAqBlD,IAAI,CAAC0C,IAAIE,QAAQ;YACnE,MAAMQ,oBAAoBrG,aAAI,CAAC6E,QAAQ,CAAC,IAAI,CAACb,MAAM,EAAE2B,IAAIE,QAAQ;YAEjE,IAAI,CAAC,IAAI,CAAC5B,GAAG,EAAE;gBACb,IAAIkC,WAAWC,UAAU;oBACvB,IAAI,CAACtB,WAAW,CAACa,IAAIE,QAAQ;gBAC/B;YACF;YAEA,MAAMS,WAAWtG,aAAI,CAACN,IAAI,CACxBkE,kBACAyC,kBAAkBE,OAAO,CAAC,0BAA0B;YAEtD,MAAMC,qBAAqBC,IAAAA,kCAAgB,EACzCzG,aAAI,CACDN,IAAI,CAAC,IAAI,CAAC+E,8BAA8B,CAAC4B,oBACzCE,OAAO,CAAC,0BAA0B;YAGvC,MAAMG,YAAY1G,aAAI,CAACN,IAAI,CAAC+F,kBAAkBa;YAE9C,IAAIJ,WAAW;gBACb,MAAMvG,QAAQ,MAAME,kBAAkB8F,IAAIE,QAAQ;gBAClDD,MAAM,CAACc,UAAU,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACvC1H,oBAAoByG,IAAIE,QAAQ,EAAEW,oBAAoB;oBACpDlH,MAAM;oBACNK;gBACF;YAEJ,OAAO,IAAIwG,SAAS;gBAClBP,MAAM,CAACc,UAAU,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACvC1H,oBAAoByG,IAAIE,QAAQ,EAAEW,oBAAoB;oBACpDlH,MAAM;gBACR;YAEJ,OAAO,IAAI8G,UAAU;gBACnBR,MAAM,CAACc,UAAU,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACvC1H,oBAAoByG,IAAIE,QAAQ,EAAEW,oBAAoB;oBACpDlH,MAAM;gBACR;YAEJ;QACF;QAEAkG,SAASqB,KAAK,CAACC,WAAW,CAACC,GAAG,CAAC9H,aAAa,CAAC6H;YAC3CA,YAAYD,KAAK,CAACG,aAAa,CAACC,QAAQ,CACtC;gBACEzG,MAAMvB;gBACNiI,OAAOC,gBAAO,CAACC,WAAW,CAACC,kCAAkC;YAC/D,GACA,OAAOzB,QAAQ0B;gBACb,MAAMC,WAA2B,EAAE;gBAEnC,eAAe;gBACf,IAAI,IAAI,CAACrD,YAAY,EAAE;oBACrBtD,WAAWC,IAAI,CAACE,OAAO,GAAG;oBAC1BH,WAAWC,IAAI,CAACC,MAAM,GAAG;gBAC3B,OAAO;oBACLF,WAAWI,IAAI,CAACD,OAAO,GAAG;oBAC1BH,WAAWI,IAAI,CAACF,MAAM,GAAG;gBAC3B;gBAEAgG,YAAYU,WAAW,CAACC,OAAO,CAAC,CAACC;oBAC/BA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;wBACzB,IAAI,CAACA,MAAMpH,IAAI,EAAE;wBAEjB,4CAA4C;wBAC5C,IACE,CAACoH,MAAMpH,IAAI,CAACC,UAAU,CAAC,aACvB,CACEmH,CAAAA,MAAMpH,IAAI,CAACC,UAAU,CAAC,WACrBmH,CAAAA,MAAMpH,IAAI,CAACgB,QAAQ,CAAC,YACnBoG,MAAMpH,IAAI,CAACgB,QAAQ,CAAC,SAAQ,CAAC,GAEjC;4BACA;wBACF;wBAEA,MAAMqG,eACJf,YAAYgB,UAAU,CAACC,uBAAuB,CAC5CH;wBAEJ,KAAK,MAAMjC,OAAOkC,aAAc;4BAC9BN,SAAS7G,IAAI,CAACgF,aAAaC,KAAKC;4BAEhC,oEAAoE;4BACpE,MAAMoC,YAAYrC;4BAGlB,IAAIqC,UAAUC,OAAO,EAAE;gCACrBD,UAAUC,OAAO,CAACR,OAAO,CAAC,CAACS;oCACzBX,SAAS7G,IAAI,CAACgF,aAAawC,iBAAiBtC;gCAC9C;4BACF;wBACF;oBACF;gBACF;gBAEA,MAAMuC,QAAQC,GAAG,CAACb;gBAElB,8EAA8E;gBAE9E,MAAMc,uBAAuBrI,aAAI,CAACN,IAAI,CACpC+F,kBACA;gBAGFG,MAAM,CAACyC,qBAAqB,GAAG,IAAI1B,gBAAO,CAACC,SAAS,CAClD;gBAGF,IAAI,IAAI,CAACvC,WAAW,EAAE;oBACpB,IAAI,IAAI,CAACJ,GAAG,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;wBAClCoE,oBAAY,CAACb,OAAO,CAAC,CAACc;4BACpB,IAAI,CAACzD,WAAW,CAACyD;wBACnB;oBACF;oBAEA,MAAMC,gBAAgBxI,aAAI,CAACN,IAAI,CAAC+F,kBAAkB;oBAElDG,MAAM,CAAC4C,cAAc,GAAG,IAAI7B,gBAAO,CAACC,SAAS,CAC3CpD;gBAEJ;gBAEA8D;YACF;QAEJ;IACF;AACF"}