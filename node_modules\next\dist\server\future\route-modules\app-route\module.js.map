{"version": 3, "sources": ["../../../../../src/server/future/route-modules/app-route/module.ts"], "names": ["AppRouteRouteModule", "RouteModule", "sharedModules", "is", "route", "definition", "kind", "RouteKind", "APP_ROUTE", "constructor", "userland", "resolvedPagePath", "nextConfigOutput", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "header<PERSON><PERSON>s", "staticGenerationBailout", "actionAsyncStorage", "methods", "autoImplementMethods", "nonStaticMethods", "getNonStaticMethods", "dynamic", "Error", "pathname", "process", "env", "NODE_ENV", "lowercased", "HTTP_METHODS", "map", "method", "toLowerCase", "Log", "error", "toUpperCase", "some", "resolve", "isHTTPMethod", "handleBadRequestResponse", "execute", "request", "context", "handler", "requestContext", "req", "renderOpts", "previewProps", "prerenderManifest", "preview", "staticGenerationContext", "urlPathname", "nextUrl", "fetchCache", "response", "run", "isAppRoute", "RequestAsyncStorageWrapper", "wrap", "StaticGenerationAsyncStorageWrapper", "staticGenerationStore", "getTracer", "join", "forceDynamic", "forceStatic", "dynamicShouldError", "revalidate", "wrappedRequest", "proxyRequest", "getPathnameFromAbsolutePath", "getRootSpanAttributes", "set", "trace", "AppRouteRouteHandlersSpan", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "attributes", "patchFetch", "res", "params", "parsedUrlQueryToParams", "undefined", "Response", "fetchMetrics", "waitUntil", "Promise", "all", "pendingRevalidates", "addImplicitTags", "fetchTags", "tags", "requestStore", "getStore", "mutableCookies", "headers", "Headers", "appendMutableCookies", "body", "status", "statusText", "handleInternalServerErrorResponse", "has", "get", "handle", "err", "resolveHandlerError"], "mappings": ";;;;;;;;;;;;;;;IAqHaA,mBAAmB;eAAnBA;;IAqWb,OAAkC;eAAlC;;;6BAhdO;4CAIA;qDAIA;kCAIA;sBACsD;4BACjB;wBAClB;2BACgB;6CACE;8BACf;qCACO;6DACf;sCACgB;qCACD;gCACC;2BACX;wCACa;4EAEV;iEACA;yCACW;6CAEJ;sDACS;4CACV;uEACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyExB,MAAMA,4BAA4BC,wBAAW;qBAgC3BC,gBAAgBA;IAevC,OAAcC,GAAGC,KAAkB,EAAgC;QACjE,OAAOA,MAAMC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACC,SAAS;IACtD;IAEAC,YAAY,EACVC,QAAQ,EACRL,UAAU,EACVM,gBAAgB,EAChBC,gBAAgB,EACW,CAAE;QAC7B,KAAK,CAAC;YAAEF;YAAUL;QAAW;QArD/B;;GAEC,QACeQ,sBAAsBA,gDAAmB;QAEzD;;GAEC,QACeC,+BAA+BA,kEAA4B;QAE3E;;;GAGC,QACeC,cAAcA;QAE9B;;;GAGC,QACeC,cAAcA;QAE9B;;;GAGC,QACeC,0BAA0BA,gDAAuB;QAIjE;;;GAGC,QACeC,qBAAqBA,8CAAkB;QAqBrD,IAAI,CAACP,gBAAgB,GAAGA;QACxB,IAAI,CAACC,gBAAgB,GAAGA;QAExB,yEAAyE;QACzE,mBAAmB;QACnB,IAAI,CAACO,OAAO,GAAGC,IAAAA,0CAAoB,EAACV;QAEpC,6CAA6C;QAC7C,IAAI,CAACW,gBAAgB,GAAGC,IAAAA,wCAAmB,EAACZ;QAE5C,qDAAqD;QACrD,IAAI,CAACa,OAAO,GAAG,IAAI,CAACb,QAAQ,CAACa,OAAO;QACpC,IAAI,IAAI,CAACX,gBAAgB,KAAK,UAAU;YACtC,IAAI,CAAC,IAAI,CAACW,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,QAAQ;gBAC5C,IAAI,CAACA,OAAO,GAAG;YACjB,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,iBAAiB;gBAC3C,MAAM,IAAIC,MACR,CAAC,gDAAgD,EAAEnB,WAAWoB,QAAQ,CAAC,wHAAwH,CAAC;YAEpM;QACF;QAEA,oEAAoE;QACpE,eAAe;QACf,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,6EAA6E;YAC7E,oCAAoC;YACpC,MAAMC,aAAaC,kBAAY,CAACC,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;YAClE,KAAK,MAAMD,UAAUH,WAAY;gBAC/B,IAAIG,UAAU,IAAI,CAACtB,QAAQ,EAAE;oBAC3BwB,KAAIC,KAAK,CACP,CAAC,2BAA2B,EAAEH,OAAO,MAAM,EACzC,IAAI,CAACrB,gBAAgB,CACtB,yBAAyB,EAAEqB,OAAOI,WAAW,GAAG,gCAAgC,CAAC;gBAEtF;YACF;YAEA,2EAA2E;YAC3E,gCAAgC;YAChC,IAAI,aAAa,IAAI,CAAC1B,QAAQ,EAAE;gBAC9BwB,KAAIC,KAAK,CACP,CAAC,4BAA4B,EAAE,IAAI,CAACxB,gBAAgB,CAAC,sDAAsD,CAAC;YAEhH;YAEA,0EAA0E;YAC1E,YAAY;YACZ,IAAI,CAACmB,kBAAY,CAACO,IAAI,CAAC,CAACL,SAAWA,UAAU,IAAI,CAACtB,QAAQ,GAAG;gBAC3DwB,KAAIC,KAAK,CACP,CAAC,6BAA6B,EAAE,IAAI,CAACxB,gBAAgB,CAAC,8CAA8C,CAAC;YAEzG;QACF;IACF;IAEA;;;;;GAKC,GACD,AAAQ2B,QAAQN,MAAc,EAAqB;QACjD,yEAAyE;QACzE,IAAI,CAACO,IAAAA,kBAAY,EAACP,SAAS,OAAOQ,0CAAwB;QAE1D,sBAAsB;QACtB,OAAO,IAAI,CAACrB,OAAO,CAACa,OAAO;IAC7B;IAEA;;GAEC,GACD,MAAcS,QACZC,OAAoB,EACpBC,OAAoC,EACjB;QACnB,iDAAiD;QACjD,MAAMC,UAAU,IAAI,CAACN,OAAO,CAACI,QAAQV,MAAM;QAE3C,mCAAmC;QACnC,MAAMa,iBAAiC;YACrCC,KAAKJ;QACP;QAGEG,eAAuBE,UAAU,GAAG;YACpCC,cAAcL,QAAQM,iBAAiB,CAACC,OAAO;QACjD;QAEA,6CAA6C;QAC7C,MAAMC,0BAAmD;YACvDC,aAAaV,QAAQW,OAAO,CAAC5B,QAAQ;YACrCsB,YAAYJ,QAAQI,UAAU;QAChC;QAEA,+CAA+C;QAC/CI,wBAAwBJ,UAAU,CAACO,UAAU,GAAG,IAAI,CAAC5C,QAAQ,CAAC4C,UAAU;QAExE,0EAA0E;QAC1E,wEAAwE;QACxE,+CAA+C;QAC/C,MAAMC,WAAoB,MAAM,IAAI,CAACrC,kBAAkB,CAACsC,GAAG,CACzD;YACEC,YAAY;QACd,GACA,IACEC,sDAA0B,CAACC,IAAI,CAC7B,IAAI,CAAC9C,mBAAmB,EACxBgC,gBACA,IACEe,wEAAmC,CAACD,IAAI,CACtC,IAAI,CAAC7C,4BAA4B,EACjCqC,yBACA,CAACU;wBAuDCC;oBAtDA,mEAAmE;oBACnE,6BAA6B;oBAC7B,IAAI,IAAI,CAACzC,gBAAgB,EAAE;wBACzB,IAAI,CAACJ,uBAAuB,CAC1B,CAAC,wBAAwB,EAAE,IAAI,CAACI,gBAAgB,CAAC0C,IAAI,CACnD,MACA,CAAC;oBAEP;oBAEA,oEAAoE;oBACpE,OAAQ,IAAI,CAACxC,OAAO;wBAClB,KAAK;4BACH,6DAA6D;4BAC7D,gCAAgC;4BAChCsC,sBAAsBG,YAAY,GAAG;4BACrC,IAAI,CAAC/C,uBAAuB,CAAC,CAAC,aAAa,CAAC,EAAE;gCAC5CM,SAAS,IAAI,CAACA,OAAO;4BACvB;4BACA;wBACF,KAAK;4BACH,4DAA4D;4BAC5D,+BAA+B;4BAC/BsC,sBAAsBI,WAAW,GAAG;4BACpC;wBACF,KAAK;4BACH,8DAA8D;4BAC9D,mDAAmD;4BACnDJ,sBAAsBK,kBAAkB,GAAG;4BAC3C;wBACF;4BACE;oBACJ;oBAEA,kEAAkE;oBAClE,oEAAoE;oBACpE,8BAA8B;oBAC9BL,sBAAsBM,UAAU,KAC9B,IAAI,CAACzD,QAAQ,CAACyD,UAAU,IAAI;oBAE9B,mEAAmE;oBACnE,yDAAyD;oBACzD,MAAMC,iBAAiBC,IAAAA,0BAAY,EACjC3B,SACA;wBAAEnB,SAAS,IAAI,CAACA,OAAO;oBAAC,GACxB;wBACEP,aAAa,IAAI,CAACA,WAAW;wBAC7BD,aAAa,IAAI,CAACA,WAAW;wBAC7BE,yBAAyB,IAAI,CAACA,uBAAuB;oBACvD;oBAGF,mDAAmD;oBACnD,MAAMb,QAAQkE,IAAAA,wDAA2B,EAAC,IAAI,CAAC3D,gBAAgB;qBAC/DmD,mCAAAA,IAAAA,iBAAS,IAAGS,qBAAqB,uBAAjCT,iCAAqCU,GAAG,CAAC,cAAcpE;oBACvD,OAAO0D,IAAAA,iBAAS,IAAGW,KAAK,CACtBC,oCAAyB,CAACC,UAAU,EACpC;wBACEC,UAAU,CAAC,0BAA0B,EAAExE,MAAM,CAAC;wBAC9CyE,YAAY;4BACV,cAAczE;wBAChB;oBACF,GACA;4BA0BIyD;wBAzBF,0BAA0B;wBAC1BiB,IAAAA,sBAAU,EAAC;4BACT/D,aAAa,IAAI,CAACA,WAAW;4BAC7BD,8BACE,IAAI,CAACA,4BAA4B;wBACrC;wBACA,MAAMiE,MAAM,MAAMnC,QAAQwB,gBAAgB;4BACxCY,QAAQrC,QAAQqC,MAAM,GAClBC,IAAAA,8CAAsB,EAACtC,QAAQqC,MAAM,IACrCE;wBACN;wBACA,IAAI,CAAEH,CAAAA,eAAeI,QAAO,GAAI;4BAC9B,MAAM,IAAI3D,MACR,CAAC,4CAA4C,EAAE,IAAI,CAACb,gBAAgB,CAAC,0FAA0F,CAAC;wBAEpK;wBACEgC,QAAQI,UAAU,CAASqC,YAAY,GACvCvB,sBAAsBuB,YAAY;wBAEpCzC,QAAQI,UAAU,CAACsC,SAAS,GAAGC,QAAQC,GAAG,CACxC1B,sBAAsB2B,kBAAkB,IAAI,EAAE;wBAGhDC,IAAAA,2BAAe,EAAC5B;wBACdlB,QAAQI,UAAU,CAAS2C,SAAS,IACpC7B,8BAAAA,sBAAsB8B,IAAI,qBAA1B9B,4BAA4BE,IAAI,CAAC;wBAEnC,4DAA4D;wBAC5D,0DAA0D;wBAC1D,QAAQ;wBACR,MAAM6B,eAAe,IAAI,CAAC/E,mBAAmB,CAACgF,QAAQ;wBACtD,IAAID,gBAAgBA,aAAaE,cAAc,EAAE;4BAC/C,MAAMC,UAAU,IAAIC,QAAQjB,IAAIgB,OAAO;4BACvC,IACEE,IAAAA,oCAAoB,EAClBF,SACAH,aAAaE,cAAc,GAE7B;gCACA,OAAO,IAAIX,SAASJ,IAAImB,IAAI,EAAE;oCAC5BC,QAAQpB,IAAIoB,MAAM;oCAClBC,YAAYrB,IAAIqB,UAAU;oCAC1BL;gCACF;4BACF;wBACF;wBAEA,OAAOhB;oBACT;gBAEJ;QAKV,yEAAyE;QACzE,kBAAkB;QAClB,IAAI,CAAExB,CAAAA,oBAAoB4B,QAAO,GAAI;YACnC,qEAAqE;YACrE,OAAOkB,IAAAA,mDAAiC;QAC1C;QAEA,IAAI9C,SAASwC,OAAO,CAACO,GAAG,CAAC,yBAAyB;YAChD,oEAAoE;YACpE,6EAA6E;YAC7E,MAAM,IAAI9E,MACR;QAGF,6EAA6E;QAC7E,iEAAiE;QAEjE,2EAA2E;QAC3E,6EAA6E;QAC7E,0EAA0E;QAC1E,mCAAmC;QACnC,sBAAsB;QACtB,8CAA8C;QAC9C,IAAI;QAEJ,yEAAyE;QACzE,gDAAgD;QAChD,oEAAoE;QACpE,kDAAkD;QAClD,qEAAqE;QACrE,yDAAyD;QAC3D;QAEA,IAAI+B,SAASwC,OAAO,CAACQ,GAAG,CAAC,yBAAyB,KAAK;YACrD,iEAAiE;YACjE,MAAM,IAAI/E,MACR;QAEJ;QAEA,OAAO+B;IACT;IAEA,MAAaiD,OACX9D,OAAoB,EACpBC,OAAoC,EACjB;QACnB,IAAI;YACF,yCAAyC;YACzC,MAAMY,WAAW,MAAM,IAAI,CAACd,OAAO,CAACC,SAASC;YAE7C,uCAAuC;YACvC,OAAOY;QACT,EAAE,OAAOkD,KAAK;YACZ,+DAA+D;YAC/D,MAAMlD,WAAWmD,IAAAA,wCAAmB,EAACD;YACrC,IAAI,CAAClD,UAAU,MAAMkD;YAErB,wCAAwC;YACxC,OAAOlD;QACT;IACF;AACF;MAEA,WAAevD"}