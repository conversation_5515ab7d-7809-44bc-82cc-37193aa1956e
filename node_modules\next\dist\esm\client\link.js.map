{"version": 3, "sources": ["../../src/client/link.tsx"], "names": ["React", "resolveHref", "isLocalURL", "formatUrl", "isAbsoluteUrl", "addLocale", "RouterContext", "AppRouterContext", "useIntersection", "getDomainLocale", "addBasePath", "PrefetchKind", "prefetched", "Set", "prefetch", "router", "href", "as", "options", "appOptions", "isAppRouter", "window", "bypassPrefetchedCheck", "locale", "undefined", "prefetched<PERSON><PERSON>", "has", "add", "prefetchPromise", "Promise", "resolve", "catch", "err", "process", "env", "NODE_ENV", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "replace", "shallow", "scroll", "prefetchEnabled", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "forceOptimisticNavigation", "startTransition", "formatStringOrUrl", "urlObjOrString", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "pagesRouter", "useContext", "appRouter", "appPrefetchKind", "AUTO", "FULL", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "hasWarned", "useRef", "current", "console", "warn", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "resolvedAs", "previousHref", "previousAs", "child", "Children", "only", "type", "childRef", "ref", "setIntersectionRef", "isVisible", "resetVisible", "rootMargin", "setRef", "useCallback", "el", "useEffect", "kind", "childProps", "defaultPrevented", "priority", "cur<PERSON><PERSON><PERSON>", "localeDomain", "isLocaleDomain", "locales", "domainLocales", "defaultLocale", "cloneElement"], "mappings": "AAAA;AAOA,OAAOA,WAAW,QAAO;AAEzB,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,UAAU,QAAQ,0CAAyC;AACpE,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SAASC,aAAa,QAAQ,sBAAqB;AACnD,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,aAAa,QAAQ,8CAA6C;AAC3E,SAASC,gBAAgB,QAAQ,kDAAiD;AAKlF,SAASC,eAAe,QAAQ,qBAAoB;AACpD,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,YAAY,QAAQ,mDAAkD;AA0F/E,MAAMC,aAAa,IAAIC;AAUvB,SAASC,SACPC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVC,OAAwB,EACxBC,UAAoC,EACpCC,WAAoB;IAEpB,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IAEA,gJAAgJ;IAChJ,IAAI,CAACD,eAAe,CAAClB,WAAWc,OAAO;QACrC;IACF;IAEA,4EAA4E;IAC5E,YAAY;IACZ,IAAI,CAACE,QAAQI,qBAAqB,EAAE;QAClC,MAAMC,SACJ,iEAAiE;QACjE,OAAOL,QAAQK,MAAM,KAAK,cACtBL,QAAQK,MAAM,GAEhB,YAAYR,SACVA,OAAOQ,MAAM,GACbC;QAEN,MAAMC,gBAAgBT,OAAO,MAAMC,KAAK,MAAMM;QAE9C,kEAAkE;QAClE,IAAIX,WAAWc,GAAG,CAACD,gBAAgB;YACjC;QACF;QAEA,+BAA+B;QAC/Bb,WAAWe,GAAG,CAACF;IACjB;IAEA,MAAMG,kBAAkBR,cACpB,AAACL,OAA6BD,QAAQ,CAACE,MAAMG,cAC7C,AAACJ,OAAsBD,QAAQ,CAACE,MAAMC,IAAIC;IAE9C,uDAAuD;IACvD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDW,QAAQC,OAAO,CAACF,iBAAiBG,KAAK,CAAC,CAACC;QACtC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMH;QACR;IACF;AACF;AAEA,SAASI,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACE,AAACD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBlC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACViC,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB,EAChB7B,MAAuB,EACvBH,WAAqB,EACrBiC,eAAyB;IAEzB,MAAM,EAAEC,QAAQ,EAAE,GAAGL,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMgB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACED,oBACCnB,CAAAA,gBAAgBa,MACf,gJAAgJ;IAC/I,CAAC7B,eAAe,CAAClB,WAAWc,KAAK,GACpC;QACA,8CAA8C;QAC9C;IACF;IAEAiC,EAAEQ,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeP,iBAAAA,SAAU;QAC/B,IAAI,oBAAoBrC,QAAQ;YAC9BA,MAAM,CAACmC,UAAU,YAAY,OAAO,CAAClC,MAAMC,IAAI;gBAC7CkC;gBACA5B;gBACA6B,QAAQO;YACV;QACF,OAAO;YACL5C,MAAM,CAACmC,UAAU,YAAY,OAAO,CAACjC,MAAMD,MAAM;gBAC/C4C,2BAA2B,CAACP;gBAC5BD,QAAQO;YACV;QACF;IACF;IAEA,IAAIvC,aAAa;QACfpB,MAAM6D,eAAe,CAACH;IACxB,OAAO;QACLA;IACF;AACF;AAOA,SAASI,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAO5D,UAAU4D;AACnB;AAEA;;CAEC,GACD,MAAMC,qBAAOhE,MAAMiE,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJrD,MAAMsD,QAAQ,EACdrD,IAAIsD,MAAM,EACVF,UAAUG,YAAY,EACtB1D,UAAU2D,eAAe,IAAI,EAC7BC,QAAQ,EACRxB,OAAO,EACPC,OAAO,EACPC,MAAM,EACN7B,MAAM,EACNoD,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,oBAACa,WAAGb;IACjB;IAEA,MAAMc,cAAcnF,MAAMoF,UAAU,CAAC9E;IACrC,MAAM+E,YAAYrF,MAAMoF,UAAU,CAAC7E;IACnC,MAAMQ,SAASoE,sBAAAA,cAAeE;IAE9B,0DAA0D;IAC1D,MAAMjE,cAAc,CAAC+D;IAErB,MAAM9B,kBAAkBoB,iBAAiB;IACzC;;;;;KAKC,GACD,MAAMa,kBACJb,iBAAiB,OAAO9D,aAAa4E,IAAI,GAAG5E,aAAa6E,IAAI;IAE/D,IAAIvD,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAASsD,gBAAgBC,IAIxB;YACC,OAAO,IAAIC,MACT,AAAC,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOzE,WAAW,cACf,qEACA,EAAC;QAEX;QAEA,sCAAsC;QACtC,MAAM0E,qBAAsD;YAC1D/E,MAAM;QACR;QACA,MAAMgF,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACP;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEzB,KAAK,CAACyB,IAAI,IAAI,QACb,OAAOzB,KAAK,CAACyB,IAAI,KAAK,YAAY,OAAOzB,KAAK,CAACyB,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQ3B,KAAK,CAACyB,IAAI,KAAK,OAAO,SAAS,OAAOzB,KAAK,CAACyB,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMQ,IAAWR;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMS,qBAAsD;YAC1DpF,IAAI;YACJiC,SAAS;YACTE,QAAQ;YACRD,SAAS;YACTuB,UAAU;YACV5D,UAAU;YACVS,QAAQ;YACRoD,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAMsB,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACP;YACrB,MAAMW,UAAU,OAAOpC,KAAK,CAACyB,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIzB,KAAK,CAACyB,IAAI,IAAIW,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IAAIX,QAAQ,UAAU;gBAC3B,IAAIzB,KAAK,CAACyB,IAAI,IAAIW,YAAY,UAAU;oBACtC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAIzB,KAAK,CAACyB,IAAI,IAAIW,YAAY,YAAY;oBACxC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAIzB,KAAK,CAACyB,IAAI,IAAI,QAAQW,YAAY,WAAW;oBAC/C,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWR;YACnB;QACF;QAEA,4FAA4F;QAC5F,sDAAsD;QACtD,MAAMY,YAAYxG,MAAMyG,MAAM,CAAC;QAC/B,IAAItC,MAAMrD,QAAQ,IAAI,CAAC0F,UAAUE,OAAO,IAAI,CAACtF,aAAa;YACxDoF,UAAUE,OAAO,GAAG;YACpBC,QAAQC,IAAI,CACV;QAEJ;IACF;IAEA,IAAI3E,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAIf,eAAe,CAACmD,QAAQ;YAC1B,IAAIvD;YACJ,IAAI,OAAOsD,aAAa,UAAU;gBAChCtD,OAAOsD;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAASuC,QAAQ,KAAK,UAC7B;gBACA7F,OAAOsD,SAASuC,QAAQ;YAC1B;YAEA,IAAI7F,MAAM;gBACR,MAAM8F,oBAAoB9F,KACvB+F,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,IAAInB,MACR,AAAC,mBAAiB3E,OAAK;gBAE3B;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGjB,MAAMoH,OAAO,CAAC;QACjC,IAAI,CAACjC,aAAa;YAChB,MAAMkC,eAAevD,kBAAkBQ;YACvC,OAAO;gBACLtD,MAAMqG;gBACNpG,IAAIsD,SAAST,kBAAkBS,UAAU8C;YAC3C;QACF;QAEA,MAAM,CAACA,cAAcC,WAAW,GAAGrH,YACjCkF,aACAb,UACA;QAGF,OAAO;YACLtD,MAAMqG;YACNpG,IAAIsD,SACAtE,YAAYkF,aAAaZ,UACzB+C,cAAcD;QACpB;IACF,GAAG;QAAClC;QAAab;QAAUC;KAAO;IAElC,MAAMgD,eAAevH,MAAMyG,MAAM,CAASzF;IAC1C,MAAMwG,aAAaxH,MAAMyG,MAAM,CAASxF;IAExC,oFAAoF;IACpF,IAAIwG;IACJ,IAAIzC,gBAAgB;QAClB,IAAI/C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAIwC,SAAS;gBACXgC,QAAQC,IAAI,CACV,AAAC,oDAAoDtC,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpB8B,QAAQC,IAAI,CACV,AAAC,yDAAyDtC,WAAS;YAEvE;YACA,IAAI;gBACFmD,QAAQzH,MAAM0H,QAAQ,CAACC,IAAI,CAACtD;YAC9B,EAAE,OAAOrC,KAAK;gBACZ,IAAI,CAACqC,UAAU;oBACb,MAAM,IAAIsB,MACR,AAAC,uDAAuDrB,WAAS;gBAErE;gBACA,MAAM,IAAIqB,MACR,AAAC,6DAA6DrB,WAAS,8FACpE,CAAA,OAAOjD,WAAW,cACf,sEACA,EAAC;YAEX;QACF,OAAO;YACLoG,QAAQzH,MAAM0H,QAAQ,CAACC,IAAI,CAACtD;QAC9B;IACF,OAAO;QACL,IAAIpC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAACkC,4BAAD,AAACA,SAAkBuD,IAAI,MAAK,KAAK;gBACnC,MAAM,IAAIjC,MACR;YAEJ;QACF;IACF;IAEA,MAAMkC,WAAgB7C,iBAClByC,SAAS,OAAOA,UAAU,YAAYA,MAAMK,GAAG,GAC/C1D;IAEJ,MAAM,CAAC2D,oBAAoBC,WAAWC,aAAa,GAAGzH,gBAAgB;QACpE0H,YAAY;IACd;IAEA,MAAMC,SAASnI,MAAMoI,WAAW,CAC9B,CAACC;QACC,4EAA4E;QAC5E,IAAIb,WAAWd,OAAO,KAAKzF,MAAMsG,aAAab,OAAO,KAAK1F,MAAM;YAC9DiH;YACAT,WAAWd,OAAO,GAAGzF;YACrBsG,aAAab,OAAO,GAAG1F;QACzB;QAEA+G,mBAAmBM;QACnB,IAAIR,UAAU;YACZ,IAAI,OAAOA,aAAa,YAAYA,SAASQ;iBACxC,IAAI,OAAOR,aAAa,UAAU;gBACrCA,SAASnB,OAAO,GAAG2B;YACrB;QACF;IACF,GACA;QAACpH;QAAI4G;QAAU7G;QAAMiH;QAAcF;KAAmB;IAGxD,2DAA2D;IAC3D/H,MAAMsI,SAAS,CAAC;QACd,gHAAgH;QAChH,IAAIrG,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,CAACpB,QAAQ;YACX;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAACiH,aAAa,CAAC3E,iBAAiB;YAClC;QACF;QAEA,oBAAoB;QACpBvC,SACEC,QACAC,MACAC,IACA;YAAEM;QAAO,GACT;YACEgH,MAAMjD;QACR,GACAlE;IAEJ,GAAG;QACDH;QACAD;QACAgH;QACAzG;QACA8B;QACA8B,+BAAAA,YAAa5D,MAAM;QACnBR;QACAK;QACAkE;KACD;IAED,MAAMkD,aAMF;QACFV,KAAKK;QACLxD,SAAQ1B,CAAC;YACP,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAACc,GAAG;oBACN,MAAM,IAAI0C,MACP;gBAEL;YACF;YAEA,IAAI,CAACX,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQ1B;YACV;YAEA,IACE+B,kBACAyC,MAAMtD,KAAK,IACX,OAAOsD,MAAMtD,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACA8C,MAAMtD,KAAK,CAACQ,OAAO,CAAC1B;YACtB;YAEA,IAAI,CAAClC,QAAQ;gBACX;YACF;YAEA,IAAIkC,EAAEwF,gBAAgB,EAAE;gBACtB;YACF;YAEAzF,YACEC,GACAlC,QACAC,MACAC,IACAiC,SACAC,SACAC,QACA7B,QACAH,aACAiC;QAEJ;QACAuB,cAAa3B,CAAC;YACZ,IAAI,CAAC+B,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB5B;YACnB;YAEA,IACE+B,kBACAyC,MAAMtD,KAAK,IACX,OAAOsD,MAAMtD,KAAK,CAACS,YAAY,KAAK,YACpC;gBACA6C,MAAMtD,KAAK,CAACS,YAAY,CAAC3B;YAC3B;YAEA,IAAI,CAAClC,QAAQ;gBACX;YACF;YAEA,IACE,AAAC,CAAA,CAACsC,mBAAmBpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAY,KAC1Df,aACA;gBACA;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEM;gBACAmH,UAAU;gBACV,gGAAgG;gBAChGpH,uBAAuB;YACzB,GACA;gBACEiH,MAAMjD;YACR,GACAlE;QAEJ;QACA0D,cAAa7B,CAAC;YACZ,IAAI,CAAC+B,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiB9B;YACnB;YAEA,IACE+B,kBACAyC,MAAMtD,KAAK,IACX,OAAOsD,MAAMtD,KAAK,CAACW,YAAY,KAAK,YACpC;gBACA2C,MAAMtD,KAAK,CAACW,YAAY,CAAC7B;YAC3B;YAEA,IAAI,CAAClC,QAAQ;gBACX;YACF;YAEA,IAAI,CAACsC,mBAAmBjC,aAAa;gBACnC;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEM;gBACAmH,UAAU;gBACV,gGAAgG;gBAChGpH,uBAAuB;YACzB,GACA;gBACEiH,MAAMjD;YACR,GACAlE;QAEJ;IACF;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,oFAAoF;IACpF,IAAIhB,cAAca,KAAK;QACrBuH,WAAWxH,IAAI,GAAGC;IACpB,OAAO,IACL,CAAC+D,kBACDN,YACC+C,MAAMG,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUH,MAAMtD,KAAK,AAAD,GAC7C;QACA,MAAMwE,YACJ,OAAOpH,WAAW,cAAcA,SAAS4D,+BAAAA,YAAa5D,MAAM;QAE9D,uEAAuE;QACvE,uEAAuE;QACvE,MAAMqH,eACJzD,CAAAA,+BAAAA,YAAa0D,cAAc,KAC3BpI,gBACEQ,IACA0H,WACAxD,+BAAAA,YAAa2D,OAAO,EACpB3D,+BAAAA,YAAa4D,aAAa;QAG9BP,WAAWxH,IAAI,GACb4H,gBACAlI,YAAYL,UAAUY,IAAI0H,WAAWxD,+BAAAA,YAAa6D,aAAa;IACnE;IAEA,OAAOhE,+BACLhF,MAAMiJ,YAAY,CAACxB,OAAOe,4BAE1B,oBAACtD;QAAG,GAAGD,SAAS;QAAG,GAAGuD,UAAU;OAC7BnE;AAGP;AAGF,eAAeL,KAAI"}