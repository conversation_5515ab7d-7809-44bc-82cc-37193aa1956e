import { AlertTriangle, ExternalLink, Copy, Check } from 'lucide-react';
import { useState } from 'react';

export default function DemoMode() {
  const [copied, setCopied] = useState(false);

  const envTemplate = `# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here`;

  const copyToClipboard = () => {
    navigator.clipboard.writeText(envTemplate);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-100 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full bg-white rounded-xl shadow-lg p-8">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="h-6 w-6 text-yellow-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Demo Mode</h1>
            <p className="text-gray-600">Firebase configuration needed</p>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <p className="text-yellow-800 text-sm">
            <strong>The application is running in demo mode.</strong> To use all features including 
            authentication, task storage, and AI assistant, please set up Firebase and OpenRouter.
          </p>
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Setup (5 minutes)</h2>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-medium">1</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Create Firebase Project</h3>
                  <p className="text-gray-600 text-sm">Go to Firebase Console and create a new project</p>
                  <a 
                    href="https://console.firebase.google.com" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm mt-1"
                  >
                    <span>Open Firebase Console</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-medium">2</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Enable Services</h3>
                  <p className="text-gray-600 text-sm">Enable Authentication (Email/Password) and Firestore Database</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-medium">3</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Get OpenRouter API Key</h3>
                  <p className="text-gray-600 text-sm">Sign up at OpenRouter for AI assistant functionality</p>
                  <a 
                    href="https://openrouter.ai" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm mt-1"
                  >
                    <span>Get OpenRouter Key</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-medium">4</span>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">Update Environment Variables</h3>
                  <p className="text-gray-600 text-sm mb-2">Copy your Firebase config to .env.local</p>
                  
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 relative">
                    <pre className="text-xs text-gray-700 overflow-x-auto">{envTemplate}</pre>
                    <button
                      onClick={copyToClipboard}
                      className="absolute top-2 right-2 p-1 bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors"
                      title="Copy to clipboard"
                    >
                      {copied ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4 text-gray-600" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-6">
            <h3 className="font-medium text-gray-900 mb-2">What you'll get after setup:</h3>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>✅ User authentication and secure login</li>
              <li>✅ Real-time task synchronization</li>
              <li>✅ AI-powered task management</li>
              <li>✅ Drag-and-drop Kanban board</li>
              <li>✅ Cross-device task access</li>
            </ul>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-800 text-sm">
              <strong>Need help?</strong> Check the README.md and QUICK_START.md files in your project 
              for detailed setup instructions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
