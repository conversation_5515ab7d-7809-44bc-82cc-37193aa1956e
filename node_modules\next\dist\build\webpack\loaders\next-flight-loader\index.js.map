{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/index.ts"], "names": ["transformSource", "noopHeadPath", "require", "resolve", "MODULE_PROXY_PATH", "source", "sourceMap", "buildInfo", "Error", "getModuleBuildInfo", "_module", "rsc", "getRSCModuleInformation", "type", "RSC_MODULE_TYPES", "client", "issuer<PERSON><PERSON>er", "layer", "sourceType", "parser", "detectedClientEntryType", "clientEntryType", "clientRefs", "WEBPACK_LAYERS", "<PERSON><PERSON><PERSON><PERSON>", "resourcePath", "includes", "callback", "assumedSourceType", "length", "esmSource", "cnt", "ref", "warnOnce", "replace", "RSC_MOD_REF_PROXY_ALIAS"], "mappings": ";;;;+BAcA;;;eAAwBA;;;2BAXjB;4BAC0B;0BACR;mCACe;oCACL;AAEnC,MAAMC,eAAeC,QAAQC,OAAO,CAAC;AACrC,gEAAgE;AAChE,MAAMC,oBACJ;AAEa,SAASJ,gBAEtBK,MAAc,EACdC,SAAc;QAaVC,gBAsFAA;IAjGJ,8BAA8B;IAC9B,IAAI,OAAOF,WAAW,UAAU;QAC9B,MAAM,IAAIG,MAAM;IAClB;IAEA,gDAAgD;IAChD,mEAAmE;IACnE,MAAMD,YAAYE,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjDH,UAAUI,GAAG,GAAGC,IAAAA,0CAAuB,EAACP,QAAQ;IAEhD,qBAAqB;IACrB,IAAIE,EAAAA,iBAAAA,UAAUI,GAAG,qBAAbJ,eAAeM,IAAI,MAAKC,4BAAgB,CAACC,MAAM,EAAE;YAEhC,sBAAA;QADnB,MAAMC,cAAc,IAAI,CAACN,OAAO,CAACO,KAAK;QACtC,MAAMC,cAAa,gBAAA,IAAI,CAACR,OAAO,sBAAZ,uBAAA,cAAcS,MAAM,qBAApB,qBAAsBD,UAAU;QACnD,MAAME,0BAA0Bb,UAAUI,GAAG,CAACU,eAAe;QAC7D,MAAMC,aAAaf,UAAUI,GAAG,CAACW,UAAU;QAE3C,IAAIN,gBAAgBO,yBAAc,CAACC,aAAa,EAAE;YAChD,8EAA8E;YAC9E,2EAA2E;YAC3E,4EAA4E;YAC5E,SAAS;YAET,+EAA+E;YAC/E,2EAA2E;YAC3E,6EAA6E;YAC7E,gCAAgC;YAChC,IAAI,CAAC,IAAI,CAACC,YAAY,CAACC,QAAQ,CAAC,iBAAiB;gBAC/C,IAAI,CAACC,QAAQ,CACX,IAAInB,MACF,CAAC,sUAAsU,CAAC;gBAG5U;YACF;QACF;QAEA,4EAA4E;QAC5E,6EAA6E;QAC7E,4DAA4D;QAC5D,IAAIoB,oBAAoBV;QACxB,IAAIU,sBAAsB,UAAUR,4BAA4B,QAAQ;YACtE,IACEE,WAAWO,MAAM,KAAK,KACrBP,WAAWO,MAAM,KAAK,KAAKP,UAAU,CAAC,EAAE,KAAK,IAC9C;gBACA,uEAAuE;gBACvE,yEAAyE;gBACzE,oBAAoB;gBACpBM,oBAAoB;YACtB,OAAO,IAAI,CAACN,WAAWI,QAAQ,CAAC,MAAM;gBACpC,2CAA2C;gBAC3CE,oBAAoB;YACtB;QACF;QAEA,IAAIA,sBAAsB,UAAU;YAClC,IAAIN,WAAWI,QAAQ,CAAC,MAAM;gBAC5B,IAAI,CAACC,QAAQ,CACX,IAAInB,MACF,CAAC,oGAAoG,CAAC;gBAG1G;YACF;YAEA,IAAIsB,YAAY,CAAC;6BACM,EAAE1B,kBAAkB;sCACX,EAAE,IAAI,CAACqB,YAAY,CAAC;;;;;;;;AAQ1D,CAAC;YACK,IAAIM,MAAM;YACV,KAAK,MAAMC,OAAOV,WAAY;gBAC5B,IAAIU,QAAQ,IAAI;oBACdF,aAAa,CAAC,wCAAwC,EAAE,IAAI,CAACL,YAAY,CAAC,KAAK,CAAC;gBAClF,OAAO,IAAIO,QAAQ,WAAW;oBAC5BF,aAAa,CAAC;;2BAEG,CAAC;gBACpB,OAAO;oBACLA,aAAa,CAAC;OACjB,EAAEC,IAAI,2BAA2B,EAAE,IAAI,CAACN,YAAY,CAAC,CAAC,EAAEO,IAAI;UACzD,EAAED,MAAM,IAAI,EAAEC,IAAI,GAAG,CAAC;gBACxB;YACF;YAEA,IAAI,CAACL,QAAQ,CAAC,MAAMG,WAAWxB;YAC/B;QACF;IACF;IAEA,IAAIC,EAAAA,kBAAAA,UAAUI,GAAG,qBAAbJ,gBAAeM,IAAI,MAAKC,4BAAgB,CAACC,MAAM,EAAE;QACnD,IAAId,iBAAiB,IAAI,CAACwB,YAAY,EAAE;YACtCQ,IAAAA,kBAAQ,EACN,CAAC,0OAA0O,CAAC;QAEhP;IACF;IAEA,IAAI,CAACN,QAAQ,CACX,MACAtB,OAAO6B,OAAO,CAACC,kCAAuB,EAAE/B,oBACxCE;AAEJ"}