/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/firebase-config */ \"./lib/firebase-config.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__, _lib_store__WEBPACK_IMPORTED_MODULE_4__]);\n([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__, _lib_store__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { setUser: setStoreUser, loadTasks } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth) {\n            setLoading(false);\n            return;\n        }\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, async (user)=>{\n            setUser(user);\n            setStoreUser(user);\n            if (user) {\n                await loadTasks(user.uid);\n            }\n            setLoading(false);\n        });\n        return unsubscribe;\n    }, [\n        setStoreUser,\n        loadTasks\n    ]);\n    const signup = async (email, password, displayName)=>{\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.updateProfile)(userCredential.user, {\n            displayName\n        });\n        return userCredential;\n    };\n    const login = async (email, password)=>{\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n    };\n    const logout = async ()=>{\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth);\n    };\n    const value = {\n        user,\n        signup,\n        login,\n        logout,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.js\n");

/***/ }),

/***/ "./lib/firebase-config.js":
/*!********************************!*\
  !*** ./lib/firebase-config.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ\" || 0,\n    authDomain: \"zatconss.firebaseapp.com\" || 0,\n    databaseURL: \"https://zatconss-default-rtdb.firebaseio.com\",\n    projectId: \"zatconss\" || 0,\n    storageBucket: \"zatconss.firebasestorage.app\" || 0,\n    messagingSenderId: \"947257597349\" || 0,\n    appId: \"1:947257597349:web:4f62c8e2bf4952eebe5c4c\" || 0,\n    measurementId: \"G-ZCHBDYX3VW\"\n};\nlet app;\nlet auth;\nlet db;\ntry {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n    auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\n    db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n    if ( true && firebaseConfig.apiKey === \"demo-key\") {\n        console.warn(\"⚠️  Using demo Firebase config. Please set up your Firebase project and update .env.local\");\n    }\n} catch (error) {\n    console.error(\"Firebase initialization error:\", error);\n    console.log(\"\\uD83D\\uDCDD Please check your Firebase configuration in .env.local\");\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase-config.js\n");

/***/ }),

/***/ "./lib/firebase-utils.js":
/*!*******************************!*\
  !*** ./lib/firebase-utils.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTask: () => (/* binding */ createTask),\n/* harmony export */   deleteTask: () => (/* binding */ deleteTask),\n/* harmony export */   getUserTasks: () => (/* binding */ getUserTasks),\n/* harmony export */   updateTask: () => (/* binding */ updateTask)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase-config */ \"./lib/firebase-config.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase_config__WEBPACK_IMPORTED_MODULE_1__]);\n([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase_config__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst createTask = async (userId, taskData)=>{\n    try {\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\"), {\n            ...taskData,\n            userId,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n        return {\n            id: docRef.id,\n            ...taskData\n        };\n    } catch (error) {\n        console.error(\"Error creating task:\", error);\n        throw error;\n    }\n};\nconst updateTask = async (taskId, updates)=>{\n    try {\n        const taskRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\", taskId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(taskRef, {\n            ...updates,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n        return {\n            id: taskId,\n            ...updates\n        };\n    } catch (error) {\n        console.error(\"Error updating task:\", error);\n        throw error;\n    }\n};\nconst deleteTask = async (taskId)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\", taskId));\n        return taskId;\n    } catch (error) {\n        console.error(\"Error deleting task:\", error);\n        throw error;\n    }\n};\nconst getUserTasks = async (userId)=>{\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, \"tasks\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"userId\", \"==\", userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"createdAt\", \"desc\"));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const tasks = [];\n        querySnapshot.forEach((doc)=>{\n            tasks.push({\n                id: doc.id,\n                ...doc.data()\n            });\n        });\n        return tasks;\n    } catch (error) {\n        console.error(\"Error fetching tasks:\", error);\n        throw error;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase-utils.js\n");

/***/ }),

/***/ "./lib/store.js":
/*!**********************!*\
  !*** ./lib/store.js ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTaskStore: () => (/* binding */ useTaskStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\n/* harmony import */ var _firebase_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase-utils */ \"./lib/firebase-utils.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__, _firebase_utils__WEBPACK_IMPORTED_MODULE_1__]);\n([zustand__WEBPACK_IMPORTED_MODULE_0__, _firebase_utils__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst useTaskStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        tasks: [],\n        user: null,\n        loading: false,\n        error: null,\n        setUser: (user)=>set({\n                user\n            }),\n        setLoading: (loading)=>set({\n                loading\n            }),\n        setError: (error)=>set({\n                error\n            }),\n        loadTasks: async (userId)=>{\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                const tasks = await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.getUserTasks)(userId);\n                set({\n                    tasks,\n                    loading: false\n                });\n            } catch (error) {\n                set({\n                    error: error.message,\n                    loading: false\n                });\n            }\n        },\n        addTask: async (taskData)=>{\n            const { user } = get();\n            if (!user) return;\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                const newTask = await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.createTask)(user.uid, taskData);\n                set((state)=>({\n                        tasks: [\n                            newTask,\n                            ...state.tasks\n                        ],\n                        loading: false\n                    }));\n                return newTask;\n            } catch (error) {\n                set({\n                    error: error.message,\n                    loading: false\n                });\n                throw error;\n            }\n        },\n        updateTask: async (taskId, updates)=>{\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.updateTask)(taskId, updates);\n                set((state)=>({\n                        tasks: state.tasks.map((task)=>task.id === taskId ? {\n                                ...task,\n                                ...updates\n                            } : task),\n                        loading: false\n                    }));\n            } catch (error) {\n                set({\n                    error: error.message,\n                    loading: false\n                });\n                throw error;\n            }\n        },\n        deleteTask: async (taskId)=>{\n            set({\n                loading: true,\n                error: null\n            });\n            try {\n                await (0,_firebase_utils__WEBPACK_IMPORTED_MODULE_1__.deleteTask)(taskId);\n                set((state)=>({\n                        tasks: state.tasks.filter((task)=>task.id !== taskId),\n                        loading: false\n                    }));\n            } catch (error) {\n                set({\n                    error: error.message,\n                    loading: false\n                });\n                throw error;\n            }\n        },\n        moveTask: async (taskId, newColumn)=>{\n            const { updateTask } = get();\n            await updateTask(taskId, {\n                status: newColumn\n            });\n        },\n        getTasksByStatus: (status)=>{\n            const { tasks } = get();\n            return tasks.filter((task)=>task.status === status);\n        },\n        getTaskCount: ()=>{\n            const { tasks } = get();\n            return tasks.length;\n        },\n        findTaskByName: (name)=>{\n            const { tasks } = get();\n            return tasks.find((task)=>task.title.toLowerCase().includes(name.toLowerCase()));\n        }\n    }));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvc3RvcmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWlDO0FBQ21EO0FBRTdFLE1BQU1LLGVBQWVMLCtDQUFNQSxDQUFDLENBQUNNLEtBQUtDLE1BQVM7UUFDaERDLE9BQU8sRUFBRTtRQUNUQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsT0FBTztRQUVQQyxTQUFTLENBQUNILE9BQVNILElBQUk7Z0JBQUVHO1lBQUs7UUFFOUJJLFlBQVksQ0FBQ0gsVUFBWUosSUFBSTtnQkFBRUk7WUFBUTtRQUV2Q0ksVUFBVSxDQUFDSCxRQUFVTCxJQUFJO2dCQUFFSztZQUFNO1FBRWpDSSxXQUFXLE9BQU9DO1lBQ2hCVixJQUFJO2dCQUFFSSxTQUFTO2dCQUFNQyxPQUFPO1lBQUs7WUFDakMsSUFBSTtnQkFDRixNQUFNSCxRQUFRLE1BQU1KLDZEQUFZQSxDQUFDWTtnQkFDakNWLElBQUk7b0JBQUVFO29CQUFPRSxTQUFTO2dCQUFNO1lBQzlCLEVBQUUsT0FBT0MsT0FBTztnQkFDZEwsSUFBSTtvQkFBRUssT0FBT0EsTUFBTU0sT0FBTztvQkFBRVAsU0FBUztnQkFBTTtZQUM3QztRQUNGO1FBRUFRLFNBQVMsT0FBT0M7WUFDZCxNQUFNLEVBQUVWLElBQUksRUFBRSxHQUFHRjtZQUNqQixJQUFJLENBQUNFLE1BQU07WUFFWEgsSUFBSTtnQkFBRUksU0FBUztnQkFBTUMsT0FBTztZQUFLO1lBQ2pDLElBQUk7Z0JBQ0YsTUFBTVMsVUFBVSxNQUFNbkIsMkRBQVVBLENBQUNRLEtBQUtZLEdBQUcsRUFBRUY7Z0JBQzNDYixJQUFJLENBQUNnQixRQUFXO3dCQUNkZCxPQUFPOzRCQUFDWTsrQkFBWUUsTUFBTWQsS0FBSzt5QkFBQzt3QkFDaENFLFNBQVM7b0JBQ1g7Z0JBQ0EsT0FBT1U7WUFDVCxFQUFFLE9BQU9ULE9BQU87Z0JBQ2RMLElBQUk7b0JBQUVLLE9BQU9BLE1BQU1NLE9BQU87b0JBQUVQLFNBQVM7Z0JBQU07Z0JBQzNDLE1BQU1DO1lBQ1I7UUFDRjtRQUVBVCxZQUFZLE9BQU9xQixRQUFRQztZQUN6QmxCLElBQUk7Z0JBQUVJLFNBQVM7Z0JBQU1DLE9BQU87WUFBSztZQUNqQyxJQUFJO2dCQUNGLE1BQU1ULDJEQUFVQSxDQUFDcUIsUUFBUUM7Z0JBQ3pCbEIsSUFBSSxDQUFDZ0IsUUFBVzt3QkFDZGQsT0FBT2MsTUFBTWQsS0FBSyxDQUFDaUIsR0FBRyxDQUFDQyxDQUFBQSxPQUNyQkEsS0FBS0MsRUFBRSxLQUFLSixTQUFTO2dDQUFFLEdBQUdHLElBQUk7Z0NBQUUsR0FBR0YsT0FBTzs0QkFBQyxJQUFJRTt3QkFFakRoQixTQUFTO29CQUNYO1lBQ0YsRUFBRSxPQUFPQyxPQUFPO2dCQUNkTCxJQUFJO29CQUFFSyxPQUFPQSxNQUFNTSxPQUFPO29CQUFFUCxTQUFTO2dCQUFNO2dCQUMzQyxNQUFNQztZQUNSO1FBQ0Y7UUFFQVIsWUFBWSxPQUFPb0I7WUFDakJqQixJQUFJO2dCQUFFSSxTQUFTO2dCQUFNQyxPQUFPO1lBQUs7WUFDakMsSUFBSTtnQkFDRixNQUFNUiwyREFBVUEsQ0FBQ29CO2dCQUNqQmpCLElBQUksQ0FBQ2dCLFFBQVc7d0JBQ2RkLE9BQU9jLE1BQU1kLEtBQUssQ0FBQ29CLE1BQU0sQ0FBQ0YsQ0FBQUEsT0FBUUEsS0FBS0MsRUFBRSxLQUFLSjt3QkFDOUNiLFNBQVM7b0JBQ1g7WUFDRixFQUFFLE9BQU9DLE9BQU87Z0JBQ2RMLElBQUk7b0JBQUVLLE9BQU9BLE1BQU1NLE9BQU87b0JBQUVQLFNBQVM7Z0JBQU07Z0JBQzNDLE1BQU1DO1lBQ1I7UUFDRjtRQUVBa0IsVUFBVSxPQUFPTixRQUFRTztZQUN2QixNQUFNLEVBQUU1QixVQUFVLEVBQUUsR0FBR0s7WUFDdkIsTUFBTUwsV0FBV3FCLFFBQVE7Z0JBQUVRLFFBQVFEO1lBQVU7UUFDL0M7UUFFQUUsa0JBQWtCLENBQUNEO1lBQ2pCLE1BQU0sRUFBRXZCLEtBQUssRUFBRSxHQUFHRDtZQUNsQixPQUFPQyxNQUFNb0IsTUFBTSxDQUFDRixDQUFBQSxPQUFRQSxLQUFLSyxNQUFNLEtBQUtBO1FBQzlDO1FBRUFFLGNBQWM7WUFDWixNQUFNLEVBQUV6QixLQUFLLEVBQUUsR0FBR0Q7WUFDbEIsT0FBT0MsTUFBTTBCLE1BQU07UUFDckI7UUFFQUMsZ0JBQWdCLENBQUNDO1lBQ2YsTUFBTSxFQUFFNUIsS0FBSyxFQUFFLEdBQUdEO1lBQ2xCLE9BQU9DLE1BQU02QixJQUFJLENBQUNYLENBQUFBLE9BQ2hCQSxLQUFLWSxLQUFLLENBQUNDLFdBQVcsR0FBR0MsUUFBUSxDQUFDSixLQUFLRyxXQUFXO1FBRXREO0lBQ0YsSUFBSSIsInNvdXJjZXMiOlsid2VicGFjazovL3Rhc2stbWFuYWdlbWVudC1hcHAvLi9saWIvc3RvcmUuanM/MDRhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcbmltcG9ydCB7IGNyZWF0ZVRhc2ssIHVwZGF0ZVRhc2ssIGRlbGV0ZVRhc2ssIGdldFVzZXJUYXNrcyB9IGZyb20gJy4vZmlyZWJhc2UtdXRpbHMnO1xuXG5leHBvcnQgY29uc3QgdXNlVGFza1N0b3JlID0gY3JlYXRlKChzZXQsIGdldCkgPT4gKHtcbiAgdGFza3M6IFtdLFxuICB1c2VyOiBudWxsLFxuICBsb2FkaW5nOiBmYWxzZSxcbiAgZXJyb3I6IG51bGwsXG5cbiAgc2V0VXNlcjogKHVzZXIpID0+IHNldCh7IHVzZXIgfSksXG4gIFxuICBzZXRMb2FkaW5nOiAobG9hZGluZykgPT4gc2V0KHsgbG9hZGluZyB9KSxcbiAgXG4gIHNldEVycm9yOiAoZXJyb3IpID0+IHNldCh7IGVycm9yIH0pLFxuXG4gIGxvYWRUYXNrczogYXN5bmMgKHVzZXJJZCkgPT4ge1xuICAgIHNldCh7IGxvYWRpbmc6IHRydWUsIGVycm9yOiBudWxsIH0pO1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB0YXNrcyA9IGF3YWl0IGdldFVzZXJUYXNrcyh1c2VySWQpO1xuICAgICAgc2V0KHsgdGFza3MsIGxvYWRpbmc6IGZhbHNlIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBzZXQoeyBlcnJvcjogZXJyb3IubWVzc2FnZSwgbG9hZGluZzogZmFsc2UgfSk7XG4gICAgfVxuICB9LFxuXG4gIGFkZFRhc2s6IGFzeW5jICh0YXNrRGF0YSkgPT4ge1xuICAgIGNvbnN0IHsgdXNlciB9ID0gZ2V0KCk7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG4gICAgXG4gICAgc2V0KHsgbG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG5ld1Rhc2sgPSBhd2FpdCBjcmVhdGVUYXNrKHVzZXIudWlkLCB0YXNrRGF0YSk7XG4gICAgICBzZXQoKHN0YXRlKSA9PiAoe1xuICAgICAgICB0YXNrczogW25ld1Rhc2ssIC4uLnN0YXRlLnRhc2tzXSxcbiAgICAgICAgbG9hZGluZzogZmFsc2VcbiAgICAgIH0pKTtcbiAgICAgIHJldHVybiBuZXdUYXNrO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBzZXQoeyBlcnJvcjogZXJyb3IubWVzc2FnZSwgbG9hZGluZzogZmFsc2UgfSk7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH0sXG5cbiAgdXBkYXRlVGFzazogYXN5bmMgKHRhc2tJZCwgdXBkYXRlcykgPT4ge1xuICAgIHNldCh7IGxvYWRpbmc6IHRydWUsIGVycm9yOiBudWxsIH0pO1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCB1cGRhdGVUYXNrKHRhc2tJZCwgdXBkYXRlcyk7XG4gICAgICBzZXQoKHN0YXRlKSA9PiAoe1xuICAgICAgICB0YXNrczogc3RhdGUudGFza3MubWFwKHRhc2sgPT4gXG4gICAgICAgICAgdGFzay5pZCA9PT0gdGFza0lkID8geyAuLi50YXNrLCAuLi51cGRhdGVzIH0gOiB0YXNrXG4gICAgICAgICksXG4gICAgICAgIGxvYWRpbmc6IGZhbHNlXG4gICAgICB9KSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldCh7IGVycm9yOiBlcnJvci5tZXNzYWdlLCBsb2FkaW5nOiBmYWxzZSB9KTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfSxcblxuICBkZWxldGVUYXNrOiBhc3luYyAodGFza0lkKSA9PiB7XG4gICAgc2V0KHsgbG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGRlbGV0ZVRhc2sodGFza0lkKTtcbiAgICAgIHNldCgoc3RhdGUpID0+ICh7XG4gICAgICAgIHRhc2tzOiBzdGF0ZS50YXNrcy5maWx0ZXIodGFzayA9PiB0YXNrLmlkICE9PSB0YXNrSWQpLFxuICAgICAgICBsb2FkaW5nOiBmYWxzZVxuICAgICAgfSkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBzZXQoeyBlcnJvcjogZXJyb3IubWVzc2FnZSwgbG9hZGluZzogZmFsc2UgfSk7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH0sXG5cbiAgbW92ZVRhc2s6IGFzeW5jICh0YXNrSWQsIG5ld0NvbHVtbikgPT4ge1xuICAgIGNvbnN0IHsgdXBkYXRlVGFzayB9ID0gZ2V0KCk7XG4gICAgYXdhaXQgdXBkYXRlVGFzayh0YXNrSWQsIHsgc3RhdHVzOiBuZXdDb2x1bW4gfSk7XG4gIH0sXG5cbiAgZ2V0VGFza3NCeVN0YXR1czogKHN0YXR1cykgPT4ge1xuICAgIGNvbnN0IHsgdGFza3MgfSA9IGdldCgpO1xuICAgIHJldHVybiB0YXNrcy5maWx0ZXIodGFzayA9PiB0YXNrLnN0YXR1cyA9PT0gc3RhdHVzKTtcbiAgfSxcblxuICBnZXRUYXNrQ291bnQ6ICgpID0+IHtcbiAgICBjb25zdCB7IHRhc2tzIH0gPSBnZXQoKTtcbiAgICByZXR1cm4gdGFza3MubGVuZ3RoO1xuICB9LFxuXG4gIGZpbmRUYXNrQnlOYW1lOiAobmFtZSkgPT4ge1xuICAgIGNvbnN0IHsgdGFza3MgfSA9IGdldCgpO1xuICAgIHJldHVybiB0YXNrcy5maW5kKHRhc2sgPT4gXG4gICAgICB0YXNrLnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobmFtZS50b0xvd2VyQ2FzZSgpKVxuICAgICk7XG4gIH1cbn0pKTtcbiJdLCJuYW1lcyI6WyJjcmVhdGUiLCJjcmVhdGVUYXNrIiwidXBkYXRlVGFzayIsImRlbGV0ZVRhc2siLCJnZXRVc2VyVGFza3MiLCJ1c2VUYXNrU3RvcmUiLCJzZXQiLCJnZXQiLCJ0YXNrcyIsInVzZXIiLCJsb2FkaW5nIiwiZXJyb3IiLCJzZXRVc2VyIiwic2V0TG9hZGluZyIsInNldEVycm9yIiwibG9hZFRhc2tzIiwidXNlcklkIiwibWVzc2FnZSIsImFkZFRhc2siLCJ0YXNrRGF0YSIsIm5ld1Rhc2siLCJ1aWQiLCJzdGF0ZSIsInRhc2tJZCIsInVwZGF0ZXMiLCJtYXAiLCJ0YXNrIiwiaWQiLCJmaWx0ZXIiLCJtb3ZlVGFzayIsIm5ld0NvbHVtbiIsInN0YXR1cyIsImdldFRhc2tzQnlTdGF0dXMiLCJnZXRUYXNrQ291bnQiLCJsZW5ndGgiLCJmaW5kVGFza0J5TmFtZSIsIm5hbWUiLCJmaW5kIiwidGl0bGUiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./lib/store.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBK0I7QUFDd0I7QUFFeEMsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFDRSw4REFBQ0gsK0RBQVlBO2tCQUNYLDRFQUFDRTtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBRzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGFzay1tYW5hZ2VtZW50LWFwcC8uL3BhZ2VzL19hcHAuanM/ZTBhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "firebase/app":
/*!*******************************!*\
  !*** external "firebase/app" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ "firebase/auth":
/*!********************************!*\
  !*** external "firebase/auth" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ "firebase/firestore":
/*!*************************************!*\
  !*** external "firebase/firestore" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.js"));
module.exports = __webpack_exports__;

})();