{"version": 3, "sources": ["../../../src/server/app-render/action-encryption.ts"], "names": ["encryptActionBoundArgs", "decryptActionBoundArgs", "decodeActionBoundArg", "actionId", "arg", "key", "getActionEncryptionKey", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "undefined", "decrypted", "arrayBufferToString", "decrypt", "stringToUint8Array", "startsWith", "length", "encodeActionBoundArg", "randomBytes", "Uint8Array", "crypto", "getRandomValues", "buffer", "encrypted", "encrypt", "btoa", "args", "clientReferenceManifestSingleton", "getClientReferenceManifestSingleton", "serialized", "streamToString", "renderToReadableStream", "clientModules", "decryped", "deserialized", "createFromReadableStream", "ReadableStream", "start", "controller", "enqueue", "TextEncoder", "encode", "close", "ssrManifest", "moduleLoading", "moduleMap", "serverModuleMap", "getServerModuleMap", "transformed", "decodeReply", "encodeReply"], "mappings": "AAAA,oDAAoD;;;;;;;;;;;;;;;IA2E9BA,sBAAsB;eAAtBA;;IAiBAC,sBAAsB;eAAtBA;;;QA3Ff;4BAMA;4BAKA;sCAEwB;uCASxB;AAEP,eAAeC,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,IAAAA,6CAAsB;IACxC,IAAI,OAAOD,QAAQ,aAAa;QAC9B,MAAM,IAAIE,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKL;IAC7B,MAAMM,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IACtC,IAAIC,YAAYC,WAAW;QACzB,MAAM,IAAIN,MAAM;IAClB;IAEA,MAAMO,YAAYC,IAAAA,0CAAmB,EACnC,MAAMC,IAAAA,8BAAO,EAACX,KAAKY,IAAAA,yCAAkB,EAACP,UAAUO,IAAAA,yCAAkB,EAACL;IAGrE,IAAI,CAACE,UAAUI,UAAU,CAACf,WAAW;QACnC,MAAM,IAAII,MAAM;IAClB;IAEA,OAAOO,UAAUH,KAAK,CAACR,SAASgB,MAAM;AACxC;AAEA,eAAeC,qBAAqBjB,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,IAAAA,6CAAsB;IACxC,IAAID,QAAQQ,WAAW;QACrB,MAAM,IAAIN,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,6BAA6B;IAC7B,MAAMc,cAAc,IAAIC,WAAW;IACnCC,OAAOC,eAAe,CAACH;IACvB,MAAMX,UAAUK,IAAAA,0CAAmB,EAACM,YAAYI,MAAM;IAEtD,MAAMC,YAAY,MAAMC,IAAAA,8BAAO,EAC7BtB,KACAgB,aACAJ,IAAAA,yCAAkB,EAACd,WAAWC;IAGhC,OAAOwB,KAAKlB,UAAUK,IAAAA,0CAAmB,EAACW;AAC5C;AAGO,eAAe1B,uBAAuBG,QAAgB,EAAE0B,IAAW;IACxE,MAAMC,mCAAmCC,IAAAA,0DAAmC;IAE5E,oDAAoD;IACpD,MAAMC,aAAa,MAAMC,IAAAA,oCAAc,EACrCC,IAAAA,kCAAsB,EAACL,MAAMC,iCAAiCK,aAAa;IAG7E,gEAAgE;IAChE,gFAAgF;IAChF,iBAAiB;IACjB,MAAMT,YAAY,MAAMN,qBAAqBjB,UAAU6B;IAEvD,OAAON;AACT;AAGO,eAAezB,uBACpBE,QAAgB,EAChBuB,SAA0B;IAE1B,gEAAgE;IAChE,MAAMU,WAAW,MAAMlC,qBAAqBC,UAAU,MAAMuB;IAE5D,wDAAwD;IACxD,MAAMW,eAAe,MAAMC,IAAAA,oCAAwB,EACjD,IAAIC,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAAC,IAAIC,cAAcC,MAAM,CAACR;YAC5CK,WAAWI,KAAK;QAClB;IACF,IACA;QACEC,aAAa;YACX,0EAA0E;YAC1E,uEAAuE;YACvE,8BAA8B;YAC9B,+CAA+C;YAC/CC,eAAe,CAAC;YAChBC,WAAW,CAAC;QACd;IACF;IAGF,oEAAoE;IACpE,MAAMC,kBAAkBC,IAAAA,yCAAkB;IAC1C,MAAMC,cAAc,MAAMC,IAAAA,uBAAW,EACnC,MAAMC,IAAAA,uBAAW,EAAChB,eAClBY;IAGF,OAAOE;AACT"}