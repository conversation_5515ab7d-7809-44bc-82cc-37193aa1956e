"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/TaskCard.js":
/*!********************************!*\
  !*** ./components/TaskCard.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TaskCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/sortable */ \"./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/utilities */ \"./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Edit,MoreVertical,Trash2!=!lucide-react */ \"__barrel_optimize__?names=Calendar,Edit,MoreVertical,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/utils */ \"./lib/utils.js\");\n/* harmony import */ var _EditTaskModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./EditTaskModal */ \"./components/EditTaskModal.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TaskCard(param) {\n    let { task, isDragging = false } = param;\n    var _task_createdAt_toDate, _task_createdAt;\n    _s();\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEdit, setShowEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { deleteTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    const { attributes, listeners, setNodeRef, transform, transition, isDragging: isSortableDragging } = (0,_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__.useSortable)({\n        id: task.id\n    });\n    const style = {\n        transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_3__.CSS.Transform.toString(transform),\n        transition,\n        opacity: isDragging || isSortableDragging ? 0.5 : 1\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-red-200 dark:border-red-700\";\n            case \"medium\":\n                return \"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700\";\n            case \"low\":\n                return \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-green-200 dark:border-green-700\";\n            default:\n                return \"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-600\";\n        }\n    };\n    const handleDelete = async ()=>{\n        if (window.confirm(\"Are you sure you want to delete this task?\")) {\n            try {\n                await deleteTask(task.id);\n            } catch (error) {\n                console.error(\"Failed to delete task:\", error);\n            }\n        }\n        setShowMenu(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: setNodeRef,\n                style: style,\n                ...attributes,\n                ...listeners,\n                className: \"bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 shadow-sm hover:shadow-lg dark:hover:shadow-xl transition-all duration-200 cursor-grab active:cursor-grabbing relative group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-gray-900 dark:text-white text-sm leading-tight flex-1 pr-2\",\n                                children: task.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            setShowMenu(!showMenu);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 opacity-0 group-hover:opacity-100 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__.MoreVertical, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setShowEdit(true);\n                                                    setShowMenu(false);\n                                                },\n                                                className: \"flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Edit, {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Edit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDelete();\n                                                },\n                                                className: \"flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Trash2, {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Delete\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-xs mb-3 line-clamp-2\",\n                        children: task.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border \".concat(getPriorityColor(task.priority)),\n                                children: task.priority\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            task.createdAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1 text-xs text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Edit_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Calendar, {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(((_task_createdAt_toDate = (_task_createdAt = task.createdAt).toDate) === null || _task_createdAt_toDate === void 0 ? void 0 : _task_createdAt_toDate.call(_task_createdAt)) || task.createdAt)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            showEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditTaskModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                task: task,\n                isOpen: showEdit,\n                onClose: ()=>setShowEdit(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\TaskCard.js\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TaskCard, \"k3uaefov2Zuy9ReDC9kiiWO506o=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore,\n        _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_2__.useSortable\n    ];\n});\n_c = TaskCard;\nvar _c;\n$RefreshReg$(_c, \"TaskCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TaskCard.js\n"));

/***/ })

});