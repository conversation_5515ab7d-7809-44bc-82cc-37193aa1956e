# Quick Start Guide

## 🚀 Get Started in 5 Minutes

### 1. Prerequisites
- Node.js 18+ installed
- Firebase account (free)
- OpenRouter account (free tier available)

### 2. Firebase Setup (2 minutes)
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create new project → "task-management-app"
3. Enable Authentication → Sign-in method → Email/Password
4. Create Firestore database → Start in test mode
5. Copy config from Project Settings → General → Your apps

### 3. OpenRouter Setup (1 minute)
1. Visit [OpenRouter](https://openrouter.ai)
2. Sign up and get API key
3. Add $5 credits (optional, free tier available)

### 4. Environment Setup (1 minute)
```bash
cp .env.local.example .env.local
```

Edit `.env.local`:
```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

OPENROUTER_API_KEY=your_openrouter_key
```

### 5. Run the App (1 minute)
```bash
npm run dev
```

Visit http://localhost:3000

## 🎯 First Steps

1. **Sign Up**: Create your account
2. **Add Task**: Click "Add Task" button
3. **Try Kanban**: Drag tasks between columns
4. **Use AI**: Click "Talk to Assistant" and say "Create a task called Test Task"
5. **Switch Views**: Toggle between Kanban and Todo list

## 🔧 Firestore Security Rules

Add these rules in Firebase Console → Firestore → Rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /tasks/{taskId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
  }
}
```

## 🤖 AI Commands to Try

- "Create a new task called Design Homepage"
- "Move task Design Homepage to In Progress"
- "How many tasks do I have?"
- "What should I focus on today?"
- "Delete task Design Homepage"

## 📱 Features Overview

### Kanban Board
- Drag and drop tasks
- Three columns: To Do, In Progress, Done
- Visual task management

### Todo List
- Simple checkbox interface
- Reorder tasks by dragging
- Quick task completion

### AI Assistant
- Natural language commands
- Task creation and management
- Smart suggestions and insights

### Task Management
- Create, edit, delete tasks
- Priority levels (High, Medium, Low)
- Real-time synchronization

## 🚨 Troubleshooting

### Common Issues

**Firebase not connecting?**
- Check environment variables are correct
- Ensure Firebase project is active
- Verify authentication is enabled

**AI not working?**
- Check OpenRouter API key
- Verify you have credits
- Check browser console for errors

**Build errors?**
- Run `npm install` again
- Clear cache: `rm -rf .next`
- Check Node.js version (18+)

**Tasks not saving?**
- Check Firestore security rules
- Verify user is authenticated
- Check browser network tab

### Getting Help

1. Check the full README.md for detailed documentation
2. Review DEVELOPER_DOCS.md for technical details
3. Check browser console for error messages
4. Verify all environment variables are set

## 🎨 Customization

### Change AI Model
Edit `lib/openrouter-config.js`:
```javascript
model: 'anthropic/claude-3-sonnet', // or 'openai/gpt-4'
```

### Modify Colors
Edit `tailwind.config.js` to customize the theme.

### Add New Task Fields
1. Update forms in `components/AddTaskModal.js`
2. Modify `lib/firebase-utils.js`
3. Update `components/TaskCard.js`

## 🚀 Deployment

### Vercel (Recommended)
1. Push to GitHub
2. Connect to Vercel
3. Add environment variables
4. Deploy!

### Other Options
- Netlify
- Firebase Hosting
- AWS Amplify

## 📊 What's Next?

After setup, explore:
- Creating multiple tasks
- Using different priority levels
- Experimenting with AI commands
- Switching between Kanban and Todo views
- Editing and deleting tasks

## 💡 Pro Tips

1. **Keyboard Shortcuts**: Use Tab to navigate forms quickly
2. **Bulk Operations**: Use AI to create multiple tasks at once
3. **Mobile**: The app works great on mobile devices
4. **Offline**: Tasks sync when you come back online
5. **Performance**: The app handles hundreds of tasks efficiently

Enjoy your new task management system! 🎉
