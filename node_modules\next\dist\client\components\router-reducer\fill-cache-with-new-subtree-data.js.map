{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-new-subtree-data.ts"], "names": ["fillCacheWithNewSubTreeData", "newCache", "existingCache", "flightDataPath", "wasPrefetched", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "data", "status", "CacheStates", "READY", "subTreeData", "invalidateCacheByRouterState", "fillLazyItemsTillLeafWithHead", "slice"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;+CAVY;8CAGiB;+CACC;sCACT;AAK9B,SAASA,4BACdC,QAAmB,EACnBC,aAAwB,EACxBC,cAA8B,EAC9BC,aAAuB;IAEvB,MAAMC,cAAcF,eAAeG,MAAM,IAAI;IAC7C,MAAM,CAACC,kBAAkBC,QAAQ,GAAGL;IAEpC,MAAMM,WAAWC,IAAAA,0CAAoB,EAACF;IAEtC,MAAMG,0BACJT,cAAcU,cAAc,CAACC,GAAG,CAACN;IAEnC,IAAI,CAACI,yBAAyB;QAC5B,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIG,kBAAkBb,SAASW,cAAc,CAACC,GAAG,CAACN;IAClD,IAAI,CAACO,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BV,SAASW,cAAc,CAACI,GAAG,CAACT,kBAAkBO;IAChD;IAEA,MAAMG,yBAAyBN,wBAAwBE,GAAG,CAACJ;IAC3D,IAAIS,iBAAiBJ,gBAAgBD,GAAG,CAACJ;IAEzC,IAAIJ,aAAa;QACf,IACE,CAACa,kBACD,CAACA,eAAeC,IAAI,IACpBD,mBAAmBD,wBACnB;YACAC,iBAAiB;gBACfE,QAAQC,0CAAW,CAACC,KAAK;gBACzBH,MAAM;gBACNI,aAAapB,cAAc,CAAC,EAAE;gBAC9B,oEAAoE;gBACpES,gBAAgBK,yBACZ,IAAIF,IAAIE,uBAAuBL,cAAc,IAC7C,IAAIG;YACV;YAEA,IAAIE,wBAAwB;gBAC1BO,IAAAA,0DAA4B,EAC1BN,gBACAD,wBACAd,cAAc,CAAC,EAAE;YAErB;YAEAsB,IAAAA,4DAA6B,EAC3BP,gBACAD,wBACAd,cAAc,CAAC,EAAE,EACjBA,cAAc,CAAC,EAAE,EACjBC;YAGFU,gBAAgBE,GAAG,CAACP,UAAUS;QAChC;QACA;IACF;IAEA,IAAI,CAACA,kBAAkB,CAACD,wBAAwB;QAC9C,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIC,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfE,QAAQF,eAAeE,MAAM;YAC7BD,MAAMD,eAAeC,IAAI;YACzBI,aAAaL,eAAeK,WAAW;YACvCX,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;QACvD;QACAE,gBAAgBE,GAAG,CAACP,UAAUS;IAChC;IAEAlB,4BACEkB,gBACAD,wBACAd,eAAeuB,KAAK,CAAC,IACrBtB;AAEJ"}