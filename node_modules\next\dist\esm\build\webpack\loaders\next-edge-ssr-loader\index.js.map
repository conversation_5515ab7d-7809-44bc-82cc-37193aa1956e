{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-ssr-loader/index.ts"], "names": ["getModuleBuildInfo", "WEBPACK_RESOURCE_QUERIES", "RouteKind", "normalizePagePath", "loadEntrypoint", "swapDistFolderWithEsmDistFolder", "path", "replace", "getRouteModuleOptions", "page", "options", "definition", "kind", "PAGES", "pathname", "bundlePath", "filename", "edgeSSRLoader", "dev", "buildId", "absolutePagePath", "absoluteAppPath", "absoluteDocumentPath", "absolute500Path", "absoluteErrorPath", "isServerComponent", "stringifiedConfig", "stringifiedConfigBase64", "appDirLoader", "appDirLoaderBase64", "pagesType", "sriEnabled", "incremental<PERSON>ache<PERSON>andlerPath", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "serverActionsBodySizeLimit", "getOptions", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "isAppDir", "buildInfo", "_module", "nextEdgeSSR", "route", "pagePath", "utils", "contextify", "context", "rootContext", "appPath", "errorPath", "documentPath", "userland500Path", "stringifiedPagePath", "stringify", "pageModPath", "substring", "length", "edgeSSREntry", "VAR_USERLAND", "VAR_PAGE", "VAR_BUILD_ID", "nextConfig", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "VAR_MODULE_DOCUMENT", "VAR_MODULE_APP", "VAR_MODULE_GLOBAL_ERROR", "pageRouteModuleOptions", "errorRouteModuleOptions", "user500RouteModuleOptions", "userland500Page"], "mappings": "AAKA,SAASA,kBAAkB,QAAQ,2BAA0B;AAC7D,SAASC,wBAAwB,QAAQ,4BAA2B;AACpE,SAASC,SAAS,QAAQ,uCAAsC;AAChE,SAASC,iBAAiB,QAAQ,uDAAsD;AACxF,SAASC,cAAc,QAAQ,2BAA0B;AAsBzD;;;;;;;AAOA,GACA,SAASC,gCAAgCC,IAAY;IACnD,OAAOA,KAAKC,OAAO,CAAC,mBAAmB;AACzC;AAEA,SAASC,sBAAsBC,IAAY;IACzC,MAAMC,UAAoE;QACxEC,YAAY;YACVC,MAAMV,UAAUW,KAAK;YACrBJ,MAAMN,kBAAkBM;YACxBK,UAAUL;YACV,2CAA2C;YAC3CM,YAAY;YACZC,UAAU;QACZ;IACF;IAEA,OAAON;AACT;AAEA,MAAMO,gBACJ,eAAeA;IACb,MAAM,EACJC,GAAG,EACHT,IAAI,EACJU,OAAO,EACPC,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmBC,uBAAuB,EAC1CC,cAAcC,kBAAkB,EAChCC,SAAS,EACTC,UAAU,EACVC,2BAA2B,EAC3BC,eAAe,EACfC,kBAAkBC,sBAAsB,EACxCC,0BAA0B,EAC3B,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAMH,mBAAqCI,KAAKC,KAAK,CACnDC,OAAOC,IAAI,CAACN,wBAAwB,UAAUO,QAAQ;IAGxD,MAAMhB,oBAAoBc,OAAOC,IAAI,CACnCd,2BAA2B,IAC3B,UACAe,QAAQ;IACV,MAAMd,eAAeY,OAAOC,IAAI,CAC9BZ,sBAAsB,IACtB,UACAa,QAAQ;IACV,MAAMC,WAAWb,cAAc;IAE/B,MAAMc,YAAY5C,mBAAmB,IAAI,CAAC6C,OAAO;IACjDD,UAAUE,WAAW,GAAG;QACtBrB;QACAhB,MAAMA;QACNkC;IACF;IACAC,UAAUG,KAAK,GAAG;QAChBtC;QACAW;QACAa;QACAC;IACF;IAEA,MAAMc,WAAW,IAAI,CAACC,KAAK,CAACC,UAAU,CACpC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChChC;IAEF,MAAMiC,UAAU,IAAI,CAACJ,KAAK,CAACC,UAAU,CACnC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChC/C,gCAAgCgB;IAElC,MAAMiC,YAAY,IAAI,CAACL,KAAK,CAACC,UAAU,CACrC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChC/C,gCAAgCmB;IAElC,MAAM+B,eAAe,IAAI,CAACN,KAAK,CAACC,UAAU,CACxC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChC/C,gCAAgCiB;IAElC,MAAMkC,kBAAkBjC,kBACpB,IAAI,CAAC0B,KAAK,CAACC,UAAU,CACnB,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChC/C,gCAAgCkB,oBAElC;IAEJ,MAAMkC,sBAAsBnB,KAAKoB,SAAS,CAACV;IAE3C,MAAMW,cAAc,CAAC,EAAE/B,aAAa,EAAE6B,oBAAoBG,SAAS,CACjE,GACAH,oBAAoBI,MAAM,GAAG,GAC7B,EAAElB,WAAW,CAAC,CAAC,EAAE1C,yBAAyB6D,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC;IAEjE,IAAInB,UAAU;QACZ,OAAO,MAAMvC,eACX,gBACA;YACE2D,cAAcJ;YACdK,UAAUvD;YACVwD,cAAc9C;QAChB,GACA;YACEY,YAAYO,KAAKoB,SAAS,CAAC3B;YAC3BmC,YAAYxC;YACZD,mBAAmBa,KAAKoB,SAAS,CAACjC;YAClCP,KAAKoB,KAAKoB,SAAS,CAACxC;YACpBkB,4BACE,OAAOA,+BAA+B,cAClC,cACAE,KAAKoB,SAAS,CAACtB;QACvB,GACA;YACE+B,yBAAyBnC,+BAA+B;QAC1D;IAEJ,OAAO;QACL,OAAO,MAAM5B,eACX,YACA;YACE2D,cAAcJ;YACdK,UAAUvD;YACVwD,cAAc9C;YACdiD,qBAAqBb;YACrBc,gBAAgBhB;YAChBiB,yBAAyBhB;QAC3B,GACA;YACExB,WAAWQ,KAAKoB,SAAS,CAAC5B;YAC1BC,YAAYO,KAAKoB,SAAS,CAAC3B;YAC3BmC,YAAYxC;YACZR,KAAKoB,KAAKoB,SAAS,CAACxC;YACpBqD,wBAAwBjC,KAAKoB,SAAS,CAAClD,sBAAsBC;YAC7D+D,yBAAyBlC,KAAKoB,SAAS,CACrClD,sBAAsB;YAExBiE,2BAA2BnC,KAAKoB,SAAS,CACvClD,sBAAsB;QAE1B,GACA;YACEkE,iBAAiBlB;YACjBW,yBAAyBnC,+BAA+B;QAC1D;IAEJ;AACF;AACF,eAAef,cAAa"}